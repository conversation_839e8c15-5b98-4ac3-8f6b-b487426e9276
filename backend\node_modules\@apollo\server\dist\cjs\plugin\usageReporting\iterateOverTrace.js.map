{"version": 3, "file": "iterateOverTrace.js", "sourceRoot": "", "sources": ["../../../../src/plugin/usageReporting/iterateOverTrace.ts"], "names": [], "mappings": ";;;AAoBA,SAAgB,gBAAgB,CAC9B,KAAY,EACZ,CAAyD,EACzD,WAAoB;IAEpB,MAAM,QAAQ,GAAG,WAAW;QAC1B,CAAC,CAAC,IAAI,mCAAmC,EAAE;QAC3C,CAAC,CAAC,kCAAkC,CAAC;IACvC,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;QACf,IAAI,oBAAoB,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;YAAE,OAAO;IAC5D,CAAC;IAED,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;QACpB,IAAI,oBAAoB,CAAC,KAAK,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;YAAE,OAAO;IACjE,CAAC;AACH,CAAC;AAfD,4CAeC;AAGD,SAAS,oBAAoB,CAC3B,IAA0B,EAC1B,QAA0B,EAC1B,CAAyD;IAEzD,IAAI,CAAC,IAAI;QAAE,OAAO,KAAK,CAAC;IAExB,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACtD,OAAO,oBAAoB,CACzB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EACrB,QAAQ,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,EACnD,CAAC,CACF,CAAC;IACJ,CAAC;IACD,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC;QACvB,OAAO,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC9D,CAAC;IACD,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;QAGzB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CACvC,oBAAoB,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CACxC,CAAC;IACJ,CAAC;IACD,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;QAGzB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CACvC,oBAAoB,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,CACxC,CAAC;IACJ,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAGD,SAAS,oBAAoB,CAC3B,IAAiB,EACjB,IAAsB,EACtB,CAAyD;IAIzD,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CAGL,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACzB,MAAM,SAAS,GAAG,KAAK,CAAC,YAAY;YAClC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC;YAChC,CAAC,CAAC,IAAI,CAAC;QACT,OAAO,oBAAoB,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,IAAI,KAAK,CACZ,CAAC;AACJ,CAAC;AAOD,MAAM,kCAAkC,GAAqB;IAC3D,OAAO;QACL,MAAM,KAAK,CAAC,uBAAuB,CAAC,CAAC;IACvC,CAAC;IACD,KAAK;QACH,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAC;AAKF,MAAM,mCAAmC;IACvC,OAAO;QACL,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,KAAK,CAAC,YAAoB;QACxB,OAAO,IAAI,oCAAoC,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IACtE,CAAC;CACF;AACD,MAAM,oCAAoC;IACxC,YACW,YAAoB,EACpB,IAAqC;QADrC,iBAAY,GAAZ,YAAY,CAAQ;QACpB,SAAI,GAAJ,IAAI,CAAiC;IAC7C,CAAC;IACJ,OAAO;QACL,MAAM,GAAG,GAAG,EAAE,CAAC;QACf,IAAI,IAAI,GAAoC,IAAI,CAAC;QACjD,OAAO,IAAI,YAAY,oCAAoC,EAAE,CAAC;YAC5D,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC5B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACnB,CAAC;QACD,OAAO,GAAG,CAAC,OAAO,EAAE,CAAC;IACvB,CAAC;IACD,KAAK,CAAC,YAAoB;QACxB,OAAO,IAAI,oCAAoC,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IACtE,CAAC;CACF"}