// backend/src/schema/resolvers/index.ts
import { userResolvers } from './user'
import { courseResolvers } from './course'
import { lessonResolvers } from './lesson'

export const resolvers = {
  Query: {
    ...userResolvers.Query,
    ...courseResolvers.Query,
    ...lessonResolvers.Query,
  },
  Mutation: {
    ...userResolvers.Mutation,
    ...courseResolvers.Mutation,
    ...lessonResolvers.Mutation,
  },
  User: userResolvers.User,
  Course: courseResolvers.Course,
  Lesson: lessonResolvers.Lesson,
}
// backend/src/schema/resolvers/user.ts
import { Context } from '../../context'
import { hashPassword, verifyPassword, generateToken } from '../../utils/auth'

export const userResolvers = {
  Query: {
    me: async (_: any, __: any, { prisma, userId }: Context) => {
      if (!userId) return null
      
      return prisma.user.findUnique({
        where: { id: userId },
        include: {
          profile: true,
          enrolledCourses: true,
        }
      })
    },
    
    leaderboard: async (_: any, __: any, { prisma }: Context) => {
      return prisma.user.findMany({
        where: { role: 'STUDENT' },
        include: { profile: true },
        orderBy: { profile: { xp: 'desc' } },
        take: 10,
      })
    },
  },
  
  Mutation: {
    register: async (_: any, args: any, { prisma }: Context) => {
      const { email, password, name } = args
      
      const hashedPassword = await hashPassword(password)
      
      const user = await prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          name,
          profile: {
            create: {
              level: 'مبتدئ',
              xp: 0,
            }
          }
        },
        include: { profile: true }
      })
      
      const token = generateToken({
        userId: user.id,
        email: user.email,
        role: user.role,
      })
      
      return { token, user }
    },
    
    login: async (_: any, args: any, { prisma }: Context) => {
      const { email, password } = args
      
      const user = await prisma.user.findUnique({
        where: { email },
        include: { profile: true }
      })
      
      if (!user) {
        throw new Error('Invalid credentials')
      }
      
      const valid = await verifyPassword(password, user.password)
      
      if (!valid) {
        throw new Error('Invalid credentials')
      }
      
      const token = generateToken({
        userId: user.id,
        email: user.email,
        role: user.role,
      })
      
      return { token, user }
    },
  },
  
  User: {
    progress: async (parent: any, _: any, { prisma }: Context) => {
      return prisma.courseProgress.findMany({
        where: { userId: parent.id },
        include: { course: true }
      })
    },
  }
}
