{"version": 3, "file": "fixPolyfills.native.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/fixPolyfills.native.ts"], "names": [], "mappings": "AAAA,kEAAkE;AAClE,6DAA6D;AAC7D,IAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAC1B,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;IAC1B,IAAA,KAAG,GAAK,OAAO,IAAZ,CAAa;IACxB,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG;QAAU,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACnC,KAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAED,kEAAkE;AAClE,IAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAC1B,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;IACvB,IAAA,KAAG,GAAK,OAAO,IAAZ,CAAa;IACxB,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG;QAAU,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACnC,KAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAED,IAAM,MAAM,GAAG,EAAE,CAAC;AAClB,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;IACxC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACxB,CAAC;AAED,IAAI,CAAC;IACH,2EAA2E;IAC3E,4EAA4E;IAC5E,6EAA6E;IAC7E,6CAA6C;IAC7C,gGAAgG;IAChG,mFAAmF;IACnF,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7C,CAAC;AAAC,WAAM,CAAC;IACP,IAAM,IAAI,GAAG,UAA6B,MAAS;QACjD,OAAO,CACL,MAAM;YACL,CAAC,UAAC,GAAG;gBACJ,IAAI,CAAC;oBACH,+DAA+D;oBAC/D,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACpC,CAAC;wBAAS,CAAC;oBACT,qEAAqE;oBACrE,yCAAyC;oBACzC,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC,CAAO,CACT,CAAC;IACJ,CAAC,CAAC;IACF,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACpC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAChC,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;AAC5D,CAAC", "sourcesContent": ["// Make sure Map.prototype.set returns the Map instance, per spec.\n// https://github.com/apollographql/apollo-client/issues/4024\nconst testMap = new Map();\nif (testMap.set(1, 2) !== testMap) {\n  const { set } = testMap;\n  Map.prototype.set = function (...args) {\n    set.apply(this, args);\n    return this;\n  };\n}\n\n// Make sure Set.prototype.add returns the Set instance, per spec.\nconst testSet = new Set();\nif (testSet.add(3) !== testSet) {\n  const { add } = testSet;\n  Set.prototype.add = function (...args) {\n    add.apply(this, args);\n    return this;\n  };\n}\n\nconst frozen = {};\nif (typeof Object.freeze === \"function\") {\n  Object.freeze(frozen);\n}\n\ntry {\n  // If non-extensible objects can't be stored as keys in a Map, make sure we\n  // do not freeze/seal/etc. an object without first attempting to put it in a\n  // Map. For example, this gives the React Native Map polyfill a chance to tag\n  // objects before they become non-extensible:\n  // https://github.com/facebook/react-native/blob/98a6f19d7c/Libraries/vendor/core/Map.js#L44-L50\n  // https://github.com/apollographql/react-apollo/issues/2442#issuecomment-426489517\n  testMap.set(frozen, frozen).delete(frozen);\n} catch {\n  const wrap = <M extends <T>(obj: T) => T>(method: M): M => {\n    return (\n      method &&\n      (((obj) => {\n        try {\n          // If .set succeeds, also call .delete to avoid leaking memory.\n          testMap.set(obj, obj).delete(obj);\n        } finally {\n          // If .set or .delete fails, the exception will be silently swallowed\n          // by this return-from-finally statement:\n          return method.call(Object, obj);\n        }\n      }) as M)\n    );\n  };\n  Object.freeze = wrap(Object.freeze);\n  Object.seal = wrap(Object.seal);\n  Object.preventExtensions = wrap(Object.preventExtensions);\n}\n\nexport {};\n"]}