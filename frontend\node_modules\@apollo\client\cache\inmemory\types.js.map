{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { DocumentNode, FieldNode } from \"graphql\";\n\nimport type { Transaction } from \"../core/cache.js\";\nimport type {\n  StoreObject,\n  StoreValue,\n  Reference,\n} from \"../../utilities/index.js\";\nimport type { FieldValueGetter } from \"./entityStore.js\";\nimport type {\n  TypePolicies,\n  PossibleTypesMap,\n  KeyFieldsFunction,\n  StorageType,\n  FieldMergeFunction,\n} from \"./policies.js\";\nimport type {\n  Modifiers,\n  ToReferenceFunction,\n  CanReadFunction,\n  AllFieldsModifier,\n} from \"../core/types/common.js\";\n\nimport type { FragmentRegistryAPI } from \"./fragmentRegistry.js\";\n\nexport type { StoreObject, StoreValue, Reference };\n\nexport interface IdGetterObj extends Object {\n  __typename?: string;\n  id?: string;\n  _id?: string;\n}\n\nexport declare type IdGetter = (value: IdGetterObj) => string | undefined;\n\n/**\n * This is an interface used to access, set and remove\n * StoreObjects from the cache\n */\nexport interface NormalizedCache {\n  has(dataId: string): boolean;\n  get(dataId: string, fieldName: string): StoreValue;\n\n  // The store.merge method allows either argument to be a string ID, but\n  // the other argument has to be a StoreObject. Either way, newer fields\n  // always take precedence over older fields.\n  merge(olderId: string, newerObject: StoreObject): void;\n  merge(olderObject: StoreObject, newerId: string): void;\n\n  modify<Entity extends Record<string, any>>(\n    dataId: string,\n    fields: Modifiers<Entity> | AllFieldsModifier<Entity>\n  ): boolean;\n  delete(dataId: string, fieldName?: string): boolean;\n  clear(): void;\n\n  // non-Map elements:\n  /**\n   * returns an Object with key-value pairs matching the contents of the store\n   */\n  toObject(): NormalizedCacheObject;\n  /**\n   * replace the state of the store\n   */\n  replace(newData: NormalizedCacheObject): void;\n\n  /**\n   * Retain (or release) a given root ID to protect (or expose) it and its\n   * transitive child entities from (or to) garbage collection. The current\n   * retainment count is returned by both methods. Note that releasing a root\n   * ID does not cause that entity to be garbage collected, but merely removes\n   * it from the set of root IDs that will be considered during the next\n   * mark-and-sweep collection.\n   */\n  retain(rootId: string): number;\n  release(rootId: string): number;\n\n  getFieldValue: FieldValueGetter;\n  toReference: ToReferenceFunction;\n  canRead: CanReadFunction;\n\n  getStorage(\n    idOrObj: string | StoreObject,\n    ...storeFieldNames: (string | number)[]\n  ): StorageType;\n}\n\n/**\n * This is a normalized representation of the Apollo query result cache. It consists of\n * a flattened representation of query result trees.\n */\nexport interface NormalizedCacheObject {\n  __META?: {\n    // Well-known singleton IDs like ROOT_QUERY and ROOT_MUTATION are\n    // always considered to be root IDs during cache.gc garbage\n    // collection, but other IDs can become roots if they are written\n    // directly with cache.writeFragment or retained explicitly with\n    // cache.retain. When such IDs exist, we include them in the __META\n    // section so that they can survive cache.{extract,restore}.\n    extraRootIds: string[];\n  };\n  [dataId: string]: StoreObject | undefined;\n}\n\nexport type OptimisticStoreItem = {\n  id: string;\n  data: NormalizedCacheObject;\n  transaction: Transaction<NormalizedCacheObject>;\n};\n\nexport type ReadQueryOptions = {\n  /**\n   * The Apollo Client store object.\n   */\n  store: NormalizedCache;\n  /**\n   * A parsed GraphQL query document.\n   */\n  query: DocumentNode;\n  variables?: Object;\n  previousResult?: any;\n  /**\n   * @deprecated\n   * Using `canonizeResults` can result in memory leaks so we generally do not\n   * recommend using this option anymore.\n   * A future version of Apollo Client will contain a similar feature without\n   * the risk of memory leaks.\n   */\n  canonizeResults?: boolean;\n  rootId?: string;\n  config?: ApolloReducerConfig;\n};\n\nexport type DiffQueryAgainstStoreOptions = ReadQueryOptions & {\n  returnPartialData?: boolean;\n};\n\nexport type ApolloReducerConfig = {\n  dataIdFromObject?: KeyFieldsFunction;\n  addTypename?: boolean;\n};\n\nexport interface InMemoryCacheConfig extends ApolloReducerConfig {\n  resultCaching?: boolean;\n  possibleTypes?: PossibleTypesMap;\n  typePolicies?: TypePolicies;\n  /**\n   * @deprecated\n   * Please use `cacheSizes` instead.\n   */\n  resultCacheMaxSize?: number;\n  /**\n   * @deprecated\n   * Using `canonizeResults` can result in memory leaks so we generally do not\n   * recommend using this option anymore.\n   * A future version of Apollo Client will contain a similar feature.\n   */\n  canonizeResults?: boolean;\n  fragments?: FragmentRegistryAPI;\n}\n\nexport interface MergeInfo {\n  field: FieldNode;\n  typename: string | undefined;\n  merge: FieldMergeFunction;\n}\n\nexport interface MergeTree {\n  info?: MergeInfo;\n  map: Map<string | number, MergeTree>;\n}\n\nexport interface ReadMergeModifyContext {\n  store: NormalizedCache;\n  variables?: Record<string, any>;\n  // A JSON.stringify-serialized version of context.variables.\n  varString?: string;\n}\n"]}