{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/express4/index.ts"], "names": [], "mappings": ";;;AAQA,6BAAwC;AACxC,wDAAkD;AAmBlD,SAAgB,iBAAiB,CAC/B,MAA8B,EAC9B,OAA4C;IAE5C,MAAM,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;IAK5C,MAAM,cAAc,GAGhB,KAAK,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAErB,MAAM,OAAO,GACX,OAAO,EAAE,OAAO,IAAI,cAAc,CAAC;IAErC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;QACxB,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YAKd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAChB,GAAG,CAAC,IAAI,CACN,sEAAsE;gBACpE,wDAAwD,CAC3D,CAAC;YACF,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,wBAAS,EAAE,CAAC;QAChC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACvD,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBAOxB,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAED,MAAM,kBAAkB,GAAuB;YAC7C,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE;YAChC,OAAO;YACP,MAAM,EAAE,IAAA,WAAQ,EAAC,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,EAAE;YACtC,IAAI,EAAE,GAAG,CAAC,IAAI;SACf,CAAC;QAEF,MAAM;aACH,yBAAyB,CAAC;YACzB,kBAAkB;YAClB,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;SACrC,CAAC;aACD,IAAI,CAAC,KAAK,EAAE,mBAAmB,EAAE,EAAE;YAClC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,mBAAmB,CAAC,OAAO,EAAE,CAAC;gBACvD,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC5B,CAAC;YACD,GAAG,CAAC,UAAU,GAAG,mBAAmB,CAAC,MAAM,IAAI,GAAG,CAAC;YAEnD,IAAI,mBAAmB,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBACjD,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC1C,OAAO;YACT,CAAC;YAED,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,mBAAmB,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;gBACjE,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAMjB,IAAI,OAAQ,GAAW,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;oBAC5C,GAAW,CAAC,KAAK,EAAE,CAAC;gBACvB,CAAC;YACH,CAAC;YACD,GAAG,CAAC,GAAG,EAAE,CAAC;QACZ,CAAC,CAAC;aACD,KAAK,CAAC,IAAI,CAAC,CAAC;IACjB,CAAC,CAAC;AACJ,CAAC;AAlFD,8CAkFC"}