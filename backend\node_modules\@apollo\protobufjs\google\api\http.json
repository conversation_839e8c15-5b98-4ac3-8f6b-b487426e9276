{"nested": {"google": {"nested": {"api": {"nested": {"Http": {"fields": {"rules": {"rule": "repeated", "type": "HttpRule", "id": 1}}}, "HttpRule": {"oneofs": {"pattern": {"oneof": ["get", "put", "post", "delete", "patch", "custom"]}}, "fields": {"get": {"type": "string", "id": 2}, "put": {"type": "string", "id": 3}, "post": {"type": "string", "id": 4}, "delete": {"type": "string", "id": 5}, "patch": {"type": "string", "id": 6}, "custom": {"type": "CustomHttpPattern", "id": 8}, "selector": {"type": "string", "id": 1}, "body": {"type": "string", "id": 7}, "additionalBindings": {"rule": "repeated", "type": "HttpRule", "id": 11}}}, "CustomHttpPattern": {"fields": {"kind": {"type": "string", "id": 1}, "path": {"type": "string", "id": 2}}}}}}}}}