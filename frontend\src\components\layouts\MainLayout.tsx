// frontend/src/components/layouts/MainLayout.tsx
import { Header } from '@/components/ui/Header'
import { Footer } from '@/components/ui/Footer'
import { ReactNode } from 'react'

interface MainLayoutProps {
  children: ReactNode
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        {children}
      </main>
      <Footer />
    </div>
  )
}
