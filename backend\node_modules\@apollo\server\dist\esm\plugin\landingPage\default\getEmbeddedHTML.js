function getConfigStringForHtml(config) {
    return JSON.stringify(config)
        .replace('<', '\\u003c')
        .replace('>', '\\u003e')
        .replace('&', '\\u0026')
        .replace("'", '\\u0027');
}
export const getEmbeddedExplorerHTML = (explorerCdnVersion, config, apolloServerVersion, nonce) => {
    const productionLandingPageEmbedConfigOrDefault = {
        displayOptions: {},
        persistExplorerState: false,
        runTelemetry: true,
        ...(typeof config.embed === 'boolean' ? {} : config.embed),
    };
    const embeddedExplorerParams = {
        graphRef: config.graphRef,
        target: '#embeddableExplorer',
        initialState: {
            ...('document' in config || 'headers' in config || 'variables' in config
                ? {
                    document: config.document,
                    headers: config.headers,
                    variables: config.variables,
                }
                : {}),
            ...('collectionId' in config
                ? {
                    collectionId: config.collectionId,
                    operationId: config.operationId,
                }
                : {}),
            displayOptions: {
                ...productionLandingPageEmbedConfigOrDefault.displayOptions,
            },
        },
        persistExplorerState: productionLandingPageEmbedConfigOrDefault.persistExplorerState,
        includeCookies: config.includeCookies,
        runtime: apolloServerVersion,
        runTelemetry: productionLandingPageEmbedConfigOrDefault.runTelemetry,
        allowDynamicStyles: false,
    };
    return `
<div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>Apollo Explorer cannot be loaded; it appears that you might be offline.</p>
</div>
<style nonce=${nonce}>
  iframe {
    background-color: white;
    height: 100%;
    width: 100%;
    border: none;
  }
  #embeddableExplorer {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
  }
</style>
<div id="embeddableExplorer"></div>
<script nonce="${nonce}" src="https://embeddable-explorer.cdn.apollographql.com/${encodeURIComponent(explorerCdnVersion)}/embeddable-explorer.umd.production.min.js?runtime=${encodeURIComponent(apolloServerVersion)}"></script>
<script nonce="${nonce}">
  var endpointUrl = window.location.href;
  var embeddedExplorerConfig = ${getConfigStringForHtml(embeddedExplorerParams)};
  new window.EmbeddedExplorer({
    ...embeddedExplorerConfig,
    endpointUrl,
  });
</script>
`;
};
export const getEmbeddedSandboxHTML = (sandboxCdnVersion, config, apolloServerVersion, nonce) => {
    const localDevelopmentEmbedConfigOrDefault = {
        runTelemetry: true,
        endpointIsEditable: false,
        initialState: {},
        ...(typeof config.embed === 'boolean' ? {} : (config.embed ?? {})),
    };
    const embeddedSandboxConfig = {
        target: '#embeddableSandbox',
        initialState: {
            ...('document' in config || 'headers' in config || 'variables' in config
                ? {
                    document: config.document,
                    variables: config.variables,
                    headers: config.headers,
                }
                : {}),
            ...('collectionId' in config
                ? {
                    collectionId: config.collectionId,
                    operationId: config.operationId,
                }
                : {}),
            includeCookies: config.includeCookies,
            ...localDevelopmentEmbedConfigOrDefault.initialState,
        },
        hideCookieToggle: false,
        endpointIsEditable: localDevelopmentEmbedConfigOrDefault.endpointIsEditable,
        runtime: apolloServerVersion,
        runTelemetry: localDevelopmentEmbedConfigOrDefault.runTelemetry,
        allowDynamicStyles: false,
    };
    return `
<div class="fallback">
  <h1>Welcome to Apollo Server</h1>
  <p>Apollo Sandbox cannot be loaded; it appears that you might be offline.</p>
</div>
<style nonce=${nonce}>
  iframe {
    background-color: white;
    height: 100%;
    width: 100%;
    border: none;
  }
  #embeddableSandbox {
    width: 100vw;
    height: 100vh;
    position: absolute;
    top: 0;
  }
</style>
<div id="embeddableSandbox"></div>
<script nonce="${nonce}" src="https://embeddable-sandbox.cdn.apollographql.com/${encodeURIComponent(sandboxCdnVersion)}/embeddable-sandbox.umd.production.min.js?runtime=${encodeURIComponent(apolloServerVersion)}"></script>
<script nonce="${nonce}">
  var initialEndpoint = window.location.href;
  var embeddedSandboxConfig = ${getConfigStringForHtml(embeddedSandboxConfig)};
  new window.EmbeddedSandbox(
    {
      ...embeddedSandboxConfig,
      initialEndpoint,
    }
  );
</script>
`;
};
//# sourceMappingURL=getEmbeddedHTML.js.map