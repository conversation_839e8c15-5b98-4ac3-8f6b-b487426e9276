"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.unwrapResolverError = exports.ApolloServerValidationErrorCode = exports.ApolloServerErrorCode = void 0;
const graphql_1 = require("graphql");
var ApolloServerErrorCode;
(function (ApolloServerErrorCode) {
    ApolloServerErrorCode["INTERNAL_SERVER_ERROR"] = "INTERNAL_SERVER_ERROR";
    ApolloServerErrorCode["GRAPHQL_PARSE_FAILED"] = "GRAPHQL_PARSE_FAILED";
    ApolloServerErrorCode["GRAPHQL_VALIDATION_FAILED"] = "GRAPHQL_VALIDATION_FAILED";
    ApolloServerErrorCode["PERSISTED_QUERY_NOT_FOUND"] = "PERSISTED_QUERY_NOT_FOUND";
    ApolloServerErrorCode["PERSISTED_QUERY_NOT_SUPPORTED"] = "PERSISTED_QUERY_NOT_SUPPORTED";
    ApolloServerErrorCode["BAD_USER_INPUT"] = "BAD_USER_INPUT";
    ApolloServerErrorCode["OPERATION_RESOLUTION_FAILURE"] = "OPERATION_RESOLUTION_FAILURE";
    ApolloServerErrorCode["BAD_REQUEST"] = "BAD_REQUEST";
})(ApolloServerErrorCode || (exports.ApolloServerErrorCode = ApolloServerErrorCode = {}));
var ApolloServerValidationErrorCode;
(function (ApolloServerValidationErrorCode) {
    ApolloServerValidationErrorCode["INTROSPECTION_DISABLED"] = "INTROSPECTION_DISABLED";
    ApolloServerValidationErrorCode["MAX_RECURSIVE_SELECTIONS_EXCEEDED"] = "MAX_RECURSIVE_SELECTIONS_EXCEEDED";
})(ApolloServerValidationErrorCode || (exports.ApolloServerValidationErrorCode = ApolloServerValidationErrorCode = {}));
function unwrapResolverError(error) {
    if (error instanceof graphql_1.GraphQLError && error.path && error.originalError) {
        return error.originalError;
    }
    return error;
}
exports.unwrapResolverError = unwrapResolverError;
//# sourceMappingURL=index.js.map