{"version": 3, "file": "defaultSendOperationsAsTrace.js", "sourceRoot": "", "sources": ["../../../../src/plugin/usageReporting/defaultSendOperationsAsTrace.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAAiC;AAEjC,+DAAyD;AACzD,iEAA2D;AAE3D,SAAgB,4BAA4B;IAU1C,MAAM,KAAK,GAAG,IAAI,mBAAQ,CAAe;QAWvC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QACxB,eAAe,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;YAC7B,OAAO,CAAC,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,CAAC,KAAY,EAAE,cAAsB,EAAW,EAAE;QACvD,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC;QAC9C,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,KAAK,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAED,MAAM,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;YAC9B,cAAc;YACd,wCAAiB,CAAC,gBAAgB,CAAC,KAAK,CAAC,UAAU,CAAC;YAEpD,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE,CAAC;YAG/B,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;SAChD,CAAC,CAAC;QAGH,IAAI,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AApDD,oEAoDC;AAID,SAAS,cAAc,CAAC,KAAY;IAClC,IAAI,SAAS,GAAG,KAAK,CAAC;IAEtB,SAAS,cAAc,CAAC,IAAiB;QACvC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;YAClC,SAAS,GAAG,IAAI,CAAC;QACnB,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAA,sCAAgB,EAAC,KAAK,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;IAC/C,OAAO,SAAS,CAAC;AACnB,CAAC"}