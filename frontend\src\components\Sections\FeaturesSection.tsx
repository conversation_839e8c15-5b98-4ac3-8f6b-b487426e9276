// frontend/src/components/sections/FeaturesSection.tsx
import { 
  BookOpen, 
  Brain, 
  Users, 
  TrendingUp, 
  Calculator, 
  Award,
  Smartphone,
  Globe
} from 'lucide-react'

export function FeaturesSection() {
  const features = [
    {
      icon: BookOpen,
      title: 'محتوى تعليمي شامل',
      description: 'دروس مفصلة تغطي جميع جوانب المحاسبة والمالية حسب البرنامج الجزائري',
      color: 'blue'
    },
    {
      icon: Brain,
      title: 'تعلم ذكي وتكيفي',
      description: 'نظام يتكيف مع مستواك ويقدم محتوى مخصص لاحتياجاتك التعليمية',
      color: 'purple'
    },
    {
      icon: Calculator,
      title: 'محاكيات مالية تفاعلية',
      description: 'تطبيق عملي للمفاهيم من خلال محاكيات واقعية للعمليات المحاسبية',
      color: 'green'
    },
    {
      icon: TrendingUp,
      title: 'تحليل مالي متقدم',
      description: 'أدوات تحليل مالي احترافية مع أمثلة من السوق الجزائري',
      color: 'orange'
    },
    {
      icon: Users,
      title: 'مجتمع تعليمي نشط',
      description: 'تواصل مع زملائك والخبراء وشارك في النقاشات التعليمية',
      color: 'pink'
    },
    {
      icon: Award,
      title: 'شهادات معتمدة',
      description: 'احصل على شهادات إتمام معتمدة تعزز سيرتك الذاتية',
      color: 'indigo'
    },
    {
      icon: Smartphone,
      title: 'تعلم في أي وقت',
      description: 'منصة متجاوبة تعمل على جميع الأجهزة للتعلم في أي مكان',
      color: 'teal'
    },
    {
      icon: Globe,
      title: 'محتوى محلي',
      description: 'محتوى مصمم خصيصاً للسوق الجزائري والقوانين المحلية',
      color: 'red'
    }
  ]

  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    purple: 'bg-purple-100 text-purple-600',
    green: 'bg-green-100 text-green-600',
    orange: 'bg-orange-100 text-orange-600',
    pink: 'bg-pink-100 text-pink-600',
    indigo: 'bg-indigo-100 text-indigo-600',
    teal: 'bg-teal-100 text-teal-600',
    red: 'bg-red-100 text-red-600',
  }

  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900">
            مميزات منصة منارة
          </h2>
          <p className="mt-4 text-xl text-gray-600">
            كل ما تحتاجه لإتقان المحاسبة والمالية في مكان واحد
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const Icon = feature.icon
            const colorClass = colorClasses[feature.color as keyof typeof colorClasses]
            
            return (
              <div 
                key={index}
                className="bg-white rounded-xl p-6 shadow-sm hover:shadow-lg transition-shadow"
              >
                <div className={`w-12 h-12 rounded-lg ${colorClass} flex items-center justify-center mb-4`}>
                  <Icon className="w-6 h-6" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {feature.description}
                </p>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
