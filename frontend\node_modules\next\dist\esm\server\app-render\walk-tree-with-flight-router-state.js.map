{"version": 3, "sources": ["../../../src/server/app-render/walk-tree-with-flight-router-state.tsx"], "names": ["React", "canSegmentBeOverridden", "matchSegment", "getLinkAndScriptTags", "getPreloadableFonts", "addSearchParamsIfPageSegment", "createFlightRouterStateFromLoaderTree", "parseLoaderTree", "getLayerAssets", "hasLoadingComponentInTree", "createComponentTree", "walkTreeWithFlightRouterState", "createSegmentPath", "loaderTreeToFilter", "parentParams", "<PERSON><PERSON><PERSON><PERSON>", "flightRouterState", "parentRendered", "rscPayloadHead", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "asNotFound", "metadataOutlet", "ctx", "renderOpts", "nextFontManifest", "query", "isPrefetch", "getDynamicParamFromSegment", "componentMod", "tree", "loaderTree", "segment", "parallelRoutes", "components", "parallelRoutesKeys", "Object", "keys", "layout", "isLayout", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "renderComponentsOnThisLevel", "length", "shouldSkipComponentTree", "Boolean", "loading", "overriddenSegment", "createElement", "Component", "firstItem", "layoutOrPagePath", "layerAssets", "Set", "<PERSON><PERSON><PERSON>", "injectedCSSWithCurrentLayout", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "clientReferenceManifest", "paths", "Promise", "all", "map", "parallelRouteKey", "parallelRoute", "currentSegmentPath", "path", "child", "item", "filter", "flat"], "mappings": "AAMA,OAAOA,WAAW,QAAO;AACzB,SACEC,sBAAsB,EACtBC,YAAY,QACP,yCAAwC;AAE/C,SAASC,oBAAoB,QAAQ,8BAA6B;AAClE,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SACEC,4BAA4B,EAC5BC,qCAAqC,QAChC,gDAA+C;AACtD,SAASC,eAAe,QAAQ,sBAAqB;AAErD,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,mBAAmB,QAAQ,0BAAyB;AAE7D;;;CAGC,GACD,OAAO,eAAeC,8BAA8B,EAClDC,iBAAiB,EACjBC,kBAAkB,EAClBC,YAAY,EACZC,OAAO,EACPC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,EACdC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,kBAAkB,EAClBC,UAAU,EACVC,cAAc,EACdC,GAAG,EAgBJ;IACC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAE,EAChCC,KAAK,EACLC,UAAU,EACVC,0BAA0B,EAC1BC,cAAc,EAAEC,MAAMC,UAAU,EAAE,EACnC,GAAGR;IAEJ,MAAM,CAACS,SAASC,gBAAgBC,WAAW,GAAGvB;IAE9C,MAAMwB,qBAAqBC,OAAOC,IAAI,CAACJ;IAEvC,MAAM,EAAEK,MAAM,EAAE,GAAGJ;IACnB,MAAMK,WAAW,OAAOD,WAAW;IAEnC;;GAEC,GACD,MAAME,wBAAwBD,YAAY,CAACnB;IAC3C;;GAEC,GACD,MAAMqB,uCACJrB,sBAAsBoB;IAExB,8JAA8J;IAC9J,MAAME,eAAed,2BAA2BI;IAChD,MAAMW,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAGhC,YAAY;QACf,CAAC8B,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IACAhC;IACN,MAAMkC,gBAAyB3C,6BAC7BuC,eAAeA,aAAaK,WAAW,GAAGf,SAC1CN;IAGF;;GAEC,GACD,MAAMsB,8BACJ,oCAAoC;IACpC,CAAClC,qBACD,yDAAyD;IACzD,CAACd,aAAa8C,eAAehC,iBAAiB,CAAC,EAAE,KACjD,wBAAwB;IACxBqB,mBAAmBc,MAAM,KAAK,KAC9B,mBAAmB;IACnBnC,iBAAiB,CAAC,EAAE,KAAK;IAE3B,MAAMoC,0BACJvB,cACA,CAACwB,QAAQjB,WAAWkB,OAAO,KAC1BtC,CAAAA,qBACC,0HAA0H;IAC1H,CAACP,0BAA0BwB,WAAU;IAEzC,IAAI,CAAChB,kBAAkBiC,6BAA6B;QAClD,MAAMK,oBACJvC,qBACAf,uBAAuB+C,eAAehC,iBAAiB,CAAC,EAAE,IACtDA,iBAAiB,CAAC,EAAE,GACpB;QAEN,OAAO;YACL;gBACEuC,qBAAqBP;gBACrB1C,sCACE,wDAAwD;gBACxDO,oBACAiB,4BACAF;gBAEFwB,0BACI,qBAGApD,MAAMwD,aAAa,CAAC;oBAClB,MAAM,EAAEC,SAAS,EAAE,GAAG,MAAM/C,oBAC1B,mEAAmE;oBACnE;wBACEe;wBACAb;wBACAqB,YAAYpB;wBACZC,cAAc+B;wBACda,WAAW3C;wBACXI;wBACAC;wBACAC;wBACA,wKAAwK;wBACxKC;wBACAC;wBACAC;oBACF;oBAGF,qBAAO,oBAACiC;gBACV;gBACJL,0BACI,OACA,AAAC,CAAA;oBACC,MAAM,EAAEO,gBAAgB,EAAE,GAAGpD,gBAAgBM;oBAE7C,MAAM+C,cAAcpD,eAAe;wBACjCiB;wBACAkC;wBACAxC,aAAa,IAAI0C,IAAI1C;wBACrBC,YAAY,IAAIyC,IAAIzC;wBACpBC,yBAAyB,IAAIwC,IAAIxC;oBACnC;oBAEA,qBACE,0CACGuC,aACA1C;gBAGP,CAAA;aACL;SACF;IACH;IAEA,wEAAwE;IACxE,yEAAyE;IACzE,yBAAyB;IACzB,MAAM4C,aAAatB,0BAAAA,MAAQ,CAAC,EAAE;IAC9B,MAAMuB,+BAA+B,IAAIF,IAAI1C;IAC7C,MAAM6C,8BAA8B,IAAIH,IAAIzC;IAC5C,MAAM6C,2CAA2C,IAAIJ,IACnDxC;IAEF,IAAIyC,YAAY;QACd3D,qBACEsB,IAAIyC,uBAAuB,EAC3BJ,YACAC,8BACAC,6BACA;QAEF5D,oBACEuB,kBACAmC,YACAG;IAEJ;IAEA,oCAAoC;IACpC,MAAME,QAA0B,AAC9B,CAAA,MAAMC,QAAQC,GAAG,CACfhC,mBAAmBiC,GAAG,CAAC,OAAOC;QAC5B,uDAAuD;QACvD,MAAMC,gBAAgBrC,cAAc,CAACoC,iBAAiB;QAEtD,MAAME,qBAAwC1D,UAC1C;YAACwD;SAAiB,GAClB;YAACvB;YAAeuB;SAAiB;QAErC,MAAMG,OAAO,MAAM/D,8BAA8B;YAC/Cc;YACAb,mBAAmB,CAAC+D;gBAClB,OAAO/D,kBAAkB;uBAAI6D;uBAAuBE;iBAAM;YAC5D;YACA9D,oBAAoB2D;YACpB1D,cAAc+B;YACd7B,mBACEA,qBAAqBA,iBAAiB,CAAC,EAAE,CAACuD,iBAAiB;YAC7DtD,gBAAgBA,kBAAkBiC;YAClCnC,SAAS;YACTG;YACAC,aAAa4C;YACb3C,YAAY4C;YACZ3C,yBAAyB4C;YACzB3C,oBAAoBqB;YACpBpB;YACAC;QACF;QAEA,OAAOkD,KACJJ,GAAG,CAAC,CAACM;YACJ,+DAA+D;YAC/D,uEAAuE;YACvE,IACEA,IAAI,CAAC,EAAE,KAAK,iBACZ5D,qBACA,CAAC,CAACA,iBAAiB,CAAC,EAAE,CAACuD,iBAAiB,CAAC,EAAE,IAC3CvD,iBAAiB,CAAC,EAAE,CAACuD,iBAAiB,CAAC,EAAE,KAAK,WAC9C;gBACA,OAAO;YACT;YACA,OAAO;gBAACvB;gBAAeuB;mBAAqBK;aAAK;QACnD,GACCC,MAAM,CAACxB;IACZ,GACF,EACAyB,IAAI;IAEN,OAAOX;AACT"}