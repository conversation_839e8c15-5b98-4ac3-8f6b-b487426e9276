{"version": 3, "sources": ["../../src/server/request-meta.ts"], "names": ["NEXT_REQUEST_META", "getRequestMeta", "setRequestMeta", "addRequestMeta", "removeRequestMeta", "getNextInternalQuery", "Symbol", "for", "req", "key", "meta", "request", "value", "query", "keysToInclude", "nextInternalQuery"], "mappings": "AAAA,+BAA+B;;;;;;;;;;;;;;;;;;;IAUlBA,iBAAiB;eAAjBA;;IA2FGC,cAAc;eAAdA;;IAeAC,cAAc;eAAdA;;IAaAC,cAAc;eAAdA;;IAiBAC,iBAAiB;eAAjBA;;IA8CAC,oBAAoB;eAApBA;;;AAtLT,MAAML,oBAAoBM,OAAOC,GAAG,CAAC;AA2FrC,SAASN,eACdO,GAAwB,EACxBC,GAAO;IAEP,MAAMC,OAAOF,GAAG,CAACR,kBAAkB,IAAI,CAAC;IACxC,OAAO,OAAOS,QAAQ,WAAWC,IAAI,CAACD,IAAI,GAAGC;AAC/C;AASO,SAASR,eAAeM,GAAwB,EAAEE,IAAiB;IACxEF,GAAG,CAACR,kBAAkB,GAAGU;IACzB,OAAOA;AACT;AAUO,SAASP,eACdQ,OAA4B,EAC5BF,GAAM,EACNG,KAAqB;IAErB,MAAMF,OAAOT,eAAeU;IAC5BD,IAAI,CAACD,IAAI,GAAGG;IACZ,OAAOV,eAAeS,SAASD;AACjC;AASO,SAASN,kBACdO,OAA4B,EAC5BF,GAAM;IAEN,MAAMC,OAAOT,eAAeU;IAC5B,OAAOD,IAAI,CAACD,IAAI;IAChB,OAAOP,eAAeS,SAASD;AACjC;AAuCO,SAASL,qBACdQ,KAAyB;IAEzB,MAAMC,gBAA6C;QACjD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAMC,oBAAuC,CAAC;IAE9C,KAAK,MAAMN,OAAOK,cAAe;QAC/B,IAAIL,OAAOI,OAAO;YAChB,2CAA2C;YAC3CE,iBAAiB,CAACN,IAAI,GAAGI,KAAK,CAACJ,IAAI;QACrC;IACF;IAEA,OAAOM;AACT"}