{"version": 3, "sources": ["../../../src/lib/metadata/resolve-metadata.ts"], "names": ["collectMetadata", "resolveMetadataItems", "accumulateMetadata", "accumulateViewport", "resolveMetadata", "hasIconsProperty", "icons", "prop", "URL", "Array", "isArray", "mergeStaticMetadata", "source", "target", "staticFilesMetadata", "metadataContext", "titleTemplates", "icon", "apple", "openGraph", "twitter", "manifest", "hasOwnProperty", "resolvedTwitter", "resolveTwitter", "images", "metadataBase", "resolvedOpenGraph", "resolveOpenGraph", "mergeMetadata", "key_", "key", "title", "resolveTitle", "alternates", "resolveAlternates", "verification", "resolveVerification", "resolveIcons", "appleWebApp", "resolveAppleWebApp", "appLinks", "resolveAppLinks", "robots", "resolveRobots", "resolveAsArrayOrUndefined", "authors", "resolveItunes", "itunes", "other", "Object", "assign", "Log", "warn", "mergeViewport", "themeColor", "resolveThemeColor", "colorScheme", "getDefinedViewport", "mod", "props", "tracingProps", "isClientReference", "generateViewport", "route", "parent", "getTracer", "trace", "ResolveMetadataSpan", "spanName", "attributes", "viewport", "getDefinedMetadata", "generateMetadata", "metadata", "collectStaticImagesFiles", "type", "undefined", "iconPromises", "map", "imageModule", "interopDefault", "length", "Promise", "all", "flat", "resolveStaticMetadata", "components", "staticMetadata", "tree", "metadataItems", "errorMetadataItem", "errorConvention", "modType", "hasErrorConventionComponent", "Boolean", "getComponentTypeModule", "getLayoutOrPageModule", "metadataExport", "viewportExport", "push", "errorMod", "errorViewportExport", "errorMetadataExport", "parentParams", "treePrefix", "getDynamicParamFromSegment", "searchParams", "segment", "parallelRoutes", "page", "currentTreePrefix", "isPage", "segmentParam", "currentParams", "value", "param", "layerProps", "params", "filter", "s", "PAGE_SEGMENT_KEY", "join", "childTree", "keys", "commonOgKeys", "postProcessMetadata", "autoFillProps", "hasTwTitle", "absolute", "hasTwDescription", "description", "hasTwImages", "partialTwitter", "collectMetadataExportPreloading", "results", "dynamicMetadataExportFn", "resolvers", "resolve", "getMetadataFromExport", "getPreloadMetadataExport", "dynamicMetadataResolveState", "currentIndex", "resolvedMetadata", "metadataResults", "dynamicMetadataResolvers", "j", "preloadMetadataExport", "resolveParent", "resolvingIndex", "metadataResult", "currentResolvedMetadata", "process", "env", "NODE_ENV", "freeze", "require", "cloneMetadata", "createDefaultMetadata", "i", "metadataItem", "template", "resolvedViewport", "createDefaultViewport", "viewportResults", "resolvedMetadataItems", "error", "err"], "mappings": ";;;;;;;;;;;;;;;;;;IA+XsBA,eAAe;eAAfA;;IAyDAC,oBAAoB;eAApBA;;IAwMAC,kBAAkB;eAAlBA;;IAqDAC,kBAAkB;eAAlBA;;IA4BAC,eAAe;eAAfA;;;iCA/rBf;kCAC0C;8BACpB;uBACa;iCACR;8BAI3B;gCACwB;+BASxB;8BACsB;wBACH;2BACU;4BACH;6DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBrB,SAASC,iBACPC,KAAwB,EACxBC,IAAsB;IAEtB,IAAI,CAACD,OAAO,OAAO;IACnB,IAAIC,SAAS,QAAQ;QACnB,0GAA0G;QAC1G,OAAO,CAAC,CACN,CAAA,OAAOD,UAAU,YACjBA,iBAAiBE,OACjBC,MAAMC,OAAO,CAACJ,UACbC,QAAQD,SAASA,KAAK,CAACC,KAAK;IAEjC,OAAO;QACL,4FAA4F;QAC5F,OAAO,CAAC,CAAE,CAAA,OAAOD,UAAU,YAAYC,QAAQD,SAASA,KAAK,CAACC,KAAK,AAAD;IACpE;AACF;AAEA,SAASI,oBACPC,MAAuB,EACvBC,MAAwB,EACxBC,mBAAmC,EACnCC,eAAgC,EAChCC,cAA8B;QAedJ,iBAUEA;IAvBlB,IAAI,CAACE,qBAAqB;IAC1B,MAAM,EAAEG,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE,GAAGP;IACtD,qFAAqF;IACrF,IACE,AAACG,QAAQ,CAACZ,iBAAiBO,0BAAAA,OAAQN,KAAK,EAAE,WACzCY,SAAS,CAACb,iBAAiBO,0BAAAA,OAAQN,KAAK,EAAE,UAC3C;QACAO,OAAOP,KAAK,GAAG;YACbW,MAAMA,QAAQ,EAAE;YAChBC,OAAOA,SAAS,EAAE;QACpB;IACF;IACA,8FAA8F;IAC9F,IAAIE,WAAW,EAACR,2BAAAA,kBAAAA,OAAQQ,OAAO,qBAAfR,gBAAiBU,cAAc,CAAC,YAAW;QACzD,MAAMC,kBAAkBC,IAAAA,gCAAc,EACpC;YAAE,GAAGX,OAAOO,OAAO;YAAEK,QAAQL;QAAQ,GACrCP,OAAOa,YAAY,EACnBV,eAAeI,OAAO;QAExBP,OAAOO,OAAO,GAAGG;IACnB;IAEA,gGAAgG;IAChG,IAAIJ,aAAa,EAACP,2BAAAA,oBAAAA,OAAQO,SAAS,qBAAjBP,kBAAmBU,cAAc,CAAC,YAAW;QAC7D,MAAMK,oBAAoBC,IAAAA,kCAAgB,EACxC;YAAE,GAAGf,OAAOM,SAAS;YAAEM,QAAQN;QAAU,GACzCN,OAAOa,YAAY,EACnBX,iBACAC,eAAeG,SAAS;QAE1BN,OAAOM,SAAS,GAAGQ;IACrB;IACA,IAAIN,UAAU;QACZR,OAAOQ,QAAQ,GAAGA;IACpB;IAEA,OAAOR;AACT;AAEA,+DAA+D;AAC/D,SAASgB,cAAc,EACrBjB,MAAM,EACNC,MAAM,EACNC,mBAAmB,EACnBE,cAAc,EACdD,eAAe,EAOhB;IACC,sFAAsF;IACtF,MAAMW,eACJ,QAAOd,0BAAAA,OAAQc,YAAY,MAAK,cAC5Bd,OAAOc,YAAY,GACnBb,OAAOa,YAAY;IACzB,IAAK,MAAMI,QAAQlB,OAAQ;QACzB,MAAMmB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAS;oBACZlB,OAAOmB,KAAK,GAAGC,IAAAA,0BAAY,EAACrB,OAAOoB,KAAK,EAAEhB,eAAegB,KAAK;oBAC9D;gBACF;YACA,KAAK;gBAAc;oBACjBnB,OAAOqB,UAAU,GAAGC,IAAAA,gCAAiB,EACnCvB,OAAOsB,UAAU,EACjBR,cACAX;oBAEF;gBACF;YACA,KAAK;gBAAa;oBAChBF,OAAOM,SAAS,GAAGS,IAAAA,kCAAgB,EACjChB,OAAOO,SAAS,EAChBO,cACAX,iBACAC,eAAeG,SAAS;oBAE1B;gBACF;YACA,KAAK;gBAAW;oBACdN,OAAOO,OAAO,GAAGI,IAAAA,gCAAc,EAC7BZ,OAAOQ,OAAO,EACdM,cACAV,eAAeI,OAAO;oBAExB;gBACF;YACA,KAAK;gBACHP,OAAOuB,YAAY,GAAGC,IAAAA,kCAAmB,EAACzB,OAAOwB,YAAY;gBAC7D;YAEF,KAAK;gBAAS;oBACZvB,OAAOP,KAAK,GAAGgC,IAAAA,0BAAY,EAAC1B,OAAON,KAAK;oBACxC;gBACF;YACA,KAAK;gBACHO,OAAO0B,WAAW,GAAGC,IAAAA,iCAAkB,EAAC5B,OAAO2B,WAAW;gBAC1D;YACF,KAAK;gBACH1B,OAAO4B,QAAQ,GAAGC,IAAAA,8BAAe,EAAC9B,OAAO6B,QAAQ;gBACjD;YACF,KAAK;gBAAU;oBACb5B,OAAO8B,MAAM,GAAGC,IAAAA,4BAAa,EAAChC,OAAO+B,MAAM;oBAC3C;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY;oBACf9B,MAAM,CAACkB,IAAI,GAAGc,IAAAA,gCAAyB,EAACjC,MAAM,CAACmB,IAAI;oBACnD;gBACF;YACA,KAAK;gBAAW;oBACdlB,MAAM,CAACkB,IAAI,GAAGc,IAAAA,gCAAyB,EAACjC,OAAOkC,OAAO;oBACtD;gBACF;YACA,KAAK;gBAAU;oBACbjC,MAAM,CAACkB,IAAI,GAAGgB,IAAAA,4BAAa,EACzBnC,OAAOoC,MAAM,EACbtB,cACAX;oBAEF;gBACF;YACA,+CAA+C;YAC/C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qCAAqC;gBACrCF,MAAM,CAACkB,IAAI,GAAGnB,MAAM,CAACmB,IAAI,IAAI;gBAC7B;YACF,KAAK;gBACHlB,OAAOoC,KAAK,GAAGC,OAAOC,MAAM,CAAC,CAAC,GAAGtC,OAAOoC,KAAK,EAAErC,OAAOqC,KAAK;gBAC3D;YACF,KAAK;gBACHpC,OAAOa,YAAY,GAAGA;gBACtB;YAEF;gBAAS;oBACP,IACEK,QAAQ,cACRA,QAAQ,gBACRA,QAAQ,eACR;wBACAqB,KAAIC,IAAI,CACN,CAAC,qBAAqB,EAAEtB,IAAI,6EAA6E,CAAC;oBAE9G;oBACA;gBACF;QACF;IACF;IACApB,oBACEC,QACAC,QACAC,qBACAC,iBACAC;AAEJ;AAEA,SAASsC,cAAc,EACrBzC,MAAM,EACND,MAAM,EAIP;IACC,IAAI,CAACA,QAAQ;IACb,IAAK,MAAMkB,QAAQlB,OAAQ;QACzB,MAAMmB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAc;oBACjBlB,OAAO0C,UAAU,GAAGC,IAAAA,gCAAiB,EAAC5C,OAAO2C,UAAU;oBACvD;gBACF;YACA,KAAK;gBACH1C,OAAO4C,WAAW,GAAG7C,OAAO6C,WAAW,IAAI;gBAC3C;YACF;gBACE,IAAI,OAAO7C,MAAM,CAACmB,IAAI,KAAK,aAAa;oBACtC,iCAAiC;oBACjClB,MAAM,CAACkB,IAAI,GAAGnB,MAAM,CAACmB,IAAI;gBAC3B;gBACA;QACJ;IACF;AACF;AAEA,eAAe2B,mBACbC,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,IAAIC,IAAAA,kCAAiB,EAACH,MAAM;QAC1B,OAAO;IACT;IACA,IAAI,OAAOA,IAAII,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAEC,KAAK,EAAE,GAAGH;QAClB,OAAO,CAACI,SACNC,IAAAA,iBAAS,IAAGC,KAAK,CACfC,8BAAmB,CAACL,gBAAgB,EACpC;gBACEM,UAAU,CAAC,iBAAiB,EAAEL,MAAM,CAAC;gBACrCM,YAAY;oBACV,aAAaN;gBACf;YACF,GACA,IAAML,IAAII,gBAAgB,CAACH,OAAOK;IAExC;IACA,OAAON,IAAIY,QAAQ,IAAI;AACzB;AAEA,eAAeC,mBACbb,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,iFAAiF;IACjF,0EAA0E;IAC1E,IAAIC,IAAAA,kCAAiB,EAACH,MAAM;QAC1B,OAAO;IACT;IACA,IAAI,OAAOA,IAAIc,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAET,KAAK,EAAE,GAAGH;QAClB,OAAO,CAACI,SACNC,IAAAA,iBAAS,IAAGC,KAAK,CACfC,8BAAmB,CAACK,gBAAgB,EACpC;gBACEJ,UAAU,CAAC,iBAAiB,EAAEL,MAAM,CAAC;gBACrCM,YAAY;oBACV,aAAaN;gBACf;YACF,GACA,IAAML,IAAIc,gBAAgB,CAACb,OAAOK;IAExC;IACA,OAAON,IAAIe,QAAQ,IAAI;AACzB;AAEA,eAAeC,yBACbD,QAAoC,EACpCd,KAAU,EACVgB,IAAmD;QAU9C;IARL,IAAI,EAACF,4BAAAA,QAAU,CAACE,KAAK,GAAE,OAAOC;IAE9B,MAAMC,eAAeJ,QAAQ,CAACE,KAAyB,CAACG,GAAG,CACzD,OAAOC,cACLC,IAAAA,8BAAc,EAAC,MAAMD,YAAYpB;IAGrC,OAAOkB,CAAAA,gCAAAA,aAAcI,MAAM,IAAG,KACzB,QAAA,MAAMC,QAAQC,GAAG,CAACN,kCAAnB,AAAC,MAAkCO,IAAI,KACvCR;AACN;AAEA,eAAeS,sBAAsBC,UAA0B,EAAE3B,KAAU;IACzE,MAAM,EAAEc,QAAQ,EAAE,GAAGa;IACrB,IAAI,CAACb,UAAU,OAAO;IAEtB,MAAM,CAACzD,MAAMC,OAAOC,WAAWC,QAAQ,GAAG,MAAM+D,QAAQC,GAAG,CAAC;QAC1DT,yBAAyBD,UAAUd,OAAO;QAC1Ce,yBAAyBD,UAAUd,OAAO;QAC1Ce,yBAAyBD,UAAUd,OAAO;QAC1Ce,yBAAyBD,UAAUd,OAAO;KAC3C;IAED,MAAM4B,iBAAiB;QACrBvE;QACAC;QACAC;QACAC;QACAC,UAAUqD,SAASrD,QAAQ;IAC7B;IAEA,OAAOmE;AACT;AAGO,eAAexF,gBAAgB,EACpCyF,IAAI,EACJC,aAAa,EACbC,iBAAiB,EACjB/B,KAAK,EACLI,KAAK,EACL4B,eAAe,EAQhB;IACC,IAAIjC;IACJ,IAAIkC;IACJ,MAAMC,8BAA8BC,QAClCH,mBAAmBH,IAAI,CAAC,EAAE,CAACG,gBAAgB;IAE7C,IAAIA,iBAAiB;QACnBjC,MAAM,MAAMqC,IAAAA,oCAAsB,EAACP,MAAM;QACzCI,UAAUD;IACZ,OAAO;QACJ,CAACjC,KAAKkC,QAAQ,GAAG,MAAMI,IAAAA,mCAAqB,EAACR;IAChD;IAEA,IAAII,SAAS;QACX7B,SAAS,CAAC,CAAC,EAAE6B,QAAQ,CAAC;IACxB;IAEA,MAAM/E,sBAAsB,MAAMwE,sBAAsBG,IAAI,CAAC,EAAE,EAAE7B;IACjE,MAAMsC,iBAAiBvC,MACnB,MAAMa,mBAAmBb,KAAKC,OAAO;QAAEI;IAAM,KAC7C;IAEJ,MAAMmC,iBAAiBxC,MACnB,MAAMD,mBAAmBC,KAAKC,OAAO;QAAEI;IAAM,KAC7C;IAEJ0B,cAAcU,IAAI,CAAC;QAACF;QAAgBpF;QAAqBqF;KAAe;IAExE,IAAIL,+BAA+BF,iBAAiB;QAClD,MAAMS,WAAW,MAAML,IAAAA,oCAAsB,EAACP,MAAMG;QACpD,MAAMU,sBAAsBD,WACxB,MAAM3C,mBAAmB2C,UAAUzC,OAAO;YAAEI;QAAM,KAClD;QACJ,MAAMuC,sBAAsBF,WACxB,MAAM7B,mBAAmB6B,UAAUzC,OAAO;YAAEI;QAAM,KAClD;QAEJ2B,iBAAiB,CAAC,EAAE,GAAGY;QACvBZ,iBAAiB,CAAC,EAAE,GAAG7E;QACvB6E,iBAAiB,CAAC,EAAE,GAAGW;IACzB;AACF;AAEO,eAAerG,qBAAqB,EACzCwF,IAAI,EACJe,YAAY,EACZd,aAAa,EACbC,iBAAiB,EACjBc,aAAa,EAAE,EACfC,0BAA0B,EAC1BC,YAAY,EACZf,eAAe,EAWhB;IACC,MAAM,CAACgB,SAASC,gBAAgB,EAAEC,IAAI,EAAE,CAAC,GAAGrB;IAC5C,MAAMsB,oBAAoB;WAAIN;QAAYG;KAAQ;IAClD,MAAMI,SAAS,OAAOF,SAAS;IAE/B,iCAAiC;IACjC,MAAMG,eAAeP,2BAA2BE;IAChD;;GAEC,GACD,MAAMM,gBACJ,mDAAmD;IACnDD,gBAAgBA,aAAaE,KAAK,KAAK,OACnC;QACE,GAAGX,YAAY;QACf,CAACS,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;IAC1C,IAEAX;IAEN,MAAMa,aAAa;QACjBC,QAAQJ;QACR,GAAIF,UAAU;YAAEL;QAAa,CAAC;IAChC;IAEA,MAAM3G,gBAAgB;QACpByF;QACAC;QACAC;QACAC;QACAhC,OAAOyD;QACPrD,OAAO+C,iBACL,yCAAyC;SACxCQ,MAAM,CAAC,CAACC,IAAMA,MAAMC,4BAAgB,EACpCC,IAAI,CAAC;IACV;IAEA,IAAK,MAAM3F,OAAO8E,eAAgB;QAChC,MAAMc,YAAYd,cAAc,CAAC9E,IAAI;QACrC,MAAM9B,qBAAqB;YACzBwF,MAAMkC;YACNjC;YACAC;YACAa,cAAcU;YACdT,YAAYM;YACZJ;YACAD;YACAd;QACF;IACF;IAEA,IAAI1C,OAAO0E,IAAI,CAACf,gBAAgB3B,MAAM,KAAK,KAAKU,iBAAiB;QAC/D,0EAA0E;QAC1E,qCAAqC;QACrCF,cAAcU,IAAI,CAACT;IACrB;IAEA,OAAOD;AACT;AAEA,MAAMmC,eAAe;IAAC;IAAS;IAAe;CAAS;AACvD,SAASC,oBACPpD,QAA0B,EAC1B1D,cAA8B;IAE9B,MAAM,EAAEG,SAAS,EAAEC,OAAO,EAAE,GAAGsD;IAC/B,IAAIvD,WAAW;QACb,IAAI4G,gBAIC,CAAC;QACN,MAAMC,aAAa5G,2BAAAA,QAASY,KAAK,CAACiG,QAAQ;QAC1C,MAAMC,mBAAmB9G,2BAAAA,QAAS+G,WAAW;QAC7C,MAAMC,cAAcrC,QAClB3E,CAAAA,2BAAAA,QAASE,cAAc,CAAC,cAAaF,QAAQK,MAAM;QAErD,IAAI,CAACuG,YAAYD,cAAc/F,KAAK,GAAGb,UAAUa,KAAK;QACtD,IAAI,CAACkG,kBAAkBH,cAAcI,WAAW,GAAGhH,UAAUgH,WAAW;QACxE,IAAI,CAACC,aAAaL,cAActG,MAAM,GAAGN,UAAUM,MAAM;QAEzD,IAAIyB,OAAO0E,IAAI,CAACG,eAAe7C,MAAM,GAAG,GAAG;YACzC,MAAMmD,iBAAiB7G,IAAAA,gCAAc,EACnCuG,eACArD,SAAShD,YAAY,EACrBV,eAAeI,OAAO;YAExB,IAAIsD,SAAStD,OAAO,EAAE;gBACpBsD,SAAStD,OAAO,GAAG8B,OAAOC,MAAM,CAAC,CAAC,GAAGuB,SAAStD,OAAO,EAAE;oBACrD,GAAI,CAAC4G,cAAc;wBAAEhG,KAAK,EAAEqG,kCAAAA,eAAgBrG,KAAK;oBAAC,CAAC;oBACnD,GAAI,CAACkG,oBAAoB;wBACvBC,WAAW,EAAEE,kCAAAA,eAAgBF,WAAW;oBAC1C,CAAC;oBACD,GAAI,CAACC,eAAe;wBAAE3G,MAAM,EAAE4G,kCAAAA,eAAgB5G,MAAM;oBAAC,CAAC;gBACxD;YACF,OAAO;gBACLiD,SAAStD,OAAO,GAAGiH;YACrB;QACF;IACF;IACA,OAAO3D;AACT;AAMA,SAAS4D,gCACPC,OAAiC,EACjCC,uBAAyD,EACzDC,SAA4C;IAE5CF,QAAQnC,IAAI,CACVoC,wBACE,IAAIrD,QAAa,CAACuD;QAChBD,UAAUrC,IAAI,CAACsC;IACjB;AAGN;AAEA,eAAeC,sBACbC,wBAEmD,EACnDC,2BAGC,EACDnD,aAA4B,EAC5BoD,YAAoB,EACpBC,gBAA8B,EAC9BC,eAAyC;IAEzC,MAAM9C,iBAAiB0C,yBAAyBlD,aAAa,CAACoD,aAAa;IAC3E,MAAMG,2BAA2BJ,4BAA4BJ,SAAS;IACtE,IAAI/D,WAAwB;IAC5B,IAAI,OAAOwB,mBAAmB,YAAY;QACxC,wDAAwD;QACxD,IAAI,CAAC+C,yBAAyB/D,MAAM,EAAE;YACpC,IAAK,IAAIgE,IAAIJ,cAAcI,IAAIxD,cAAcR,MAAM,EAAEgE,IAAK;gBACxD,MAAMC,wBAAwBP,yBAAyBlD,aAAa,CAACwD,EAAE,EAAE,sBAAsB;;gBAC/F,6EAA6E;gBAC7E,IAAI,OAAOC,0BAA0B,YAAY;oBAC/Cb,gCACEU,iBACAG,uBACAF;gBAEJ;YACF;QACF;QAEA,MAAMG,gBACJH,wBAAwB,CAACJ,4BAA4BQ,cAAc,CAAC;QACtE,MAAMC,iBACJN,eAAe,CAACH,4BAA4BQ,cAAc,GAAG;QAE/D,uFAAuF;QACvF,qEAAqE;QACrE,MAAME,0BACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACrBxG,OAAOyG,MAAM,CACXC,QAAQ,oBAAoBC,aAAa,CAACd,qBAE5CA;QAEN,qFAAqF;QACrF,8FAA8F;QAC9F,mGAAmG;QACnGK,cAAcG;QACd7E,WACE4E,0BAA0BnE,UAAU,MAAMmE,iBAAiBA;IAC/D,OAAO,IAAIpD,mBAAmB,QAAQ,OAAOA,mBAAmB,UAAU;QACxE,yCAAyC;QACzCxB,WAAWwB;IACb;IAEA,OAAOxB;AACT;AAEO,eAAexE,mBACpBwF,aAA4B,EAC5B3E,eAAgC;IAEhC,MAAMgI,mBAAmBe,IAAAA,sCAAqB;IAC9C,MAAMd,kBAAoD,EAAE;IAE5D,IAAIhI,iBAAiC;QACnCgB,OAAO;QACPZ,SAAS;QACTD,WAAW;IACb;IAEA,uFAAuF;IACvF,yGAAyG;IACzG,MAAM8H,2BAA2B;QAC/BR,WAAW,EAAE;QACbY,gBAAgB;IAClB;IACA,IAAK,IAAIU,IAAI,GAAGA,IAAIrE,cAAcR,MAAM,EAAE6E,IAAK;QAC7C,MAAMjJ,sBAAsB4E,aAAa,CAACqE,EAAE,CAAC,EAAE;QAE/C,MAAMrF,WAAW,MAAMiE,sBACrB,CAACqB,eAAiBA,YAAY,CAAC,EAAE,EACjCf,0BACAvD,eACAqE,GACAhB,kBACAC;QAGFnH,cAAc;YACZhB,QAAQkI;YACRnI,QAAQ8D;YACR3D;YACAD;YACAE;QACF;QAEA,gFAAgF;QAChF,kDAAkD;QAClD,IAAI+I,IAAIrE,cAAcR,MAAM,GAAG,GAAG;gBAEvB6D,yBACIA,6BACFA;YAHX/H,iBAAiB;gBACfgB,OAAO+G,EAAAA,0BAAAA,iBAAiB/G,KAAK,qBAAtB+G,wBAAwBkB,QAAQ,KAAI;gBAC3C9I,WAAW4H,EAAAA,8BAAAA,iBAAiB5H,SAAS,qBAA1B4H,4BAA4B/G,KAAK,CAACiI,QAAQ,KAAI;gBACzD7I,SAAS2H,EAAAA,4BAAAA,iBAAiB3H,OAAO,qBAAxB2H,0BAA0B/G,KAAK,CAACiI,QAAQ,KAAI;YACvD;QACF;IACF;IAEA,OAAOnC,oBAAoBiB,kBAAkB/H;AAC/C;AAEO,eAAeb,mBACpBuF,aAA4B;IAE5B,MAAMwE,mBAAqCC,IAAAA,sCAAqB;IAEhE,MAAMC,kBAAoD,EAAE;IAC5D,MAAMnB,2BAA2B;QAC/BR,WAAW,EAAE;QACbY,gBAAgB;IAClB;IACA,IAAK,IAAIU,IAAI,GAAGA,IAAIrE,cAAcR,MAAM,EAAE6E,IAAK;QAC7C,MAAMxF,WAAW,MAAMoE,sBACrB,CAACqB,eAAiBA,YAAY,CAAC,EAAE,EACjCf,0BACAvD,eACAqE,GACAG,kBACAE;QAGF9G,cAAc;YACZzC,QAAQqJ;YACRtJ,QAAQ2D;QACV;IACF;IACA,OAAO2F;AACT;AAEO,eAAe9J,gBAAgB,EACpCqF,IAAI,EACJe,YAAY,EACZd,aAAa,EACbC,iBAAiB,EACjBe,0BAA0B,EAC1BC,YAAY,EACZf,eAAe,EACf7E,eAAe,EAYhB;IACC,MAAMsJ,wBAAwB,MAAMpK,qBAAqB;QACvDwF;QACAe;QACAd;QACAC;QACAe;QACAC;QACAf;IACF;IACA,IAAI0E;IACJ,IAAI5F,WAA6BoF,IAAAA,sCAAqB;IACtD,IAAIvF,WAA6B4F,IAAAA,sCAAqB;IACtD,IAAI;QACF5F,WAAW,MAAMpE,mBAAmBkK;QACpC3F,WAAW,MAAMxE,mBAAmBmK,uBAAuBtJ;IAC7D,EAAE,OAAOwJ,KAAU;QACjBD,QAAQC;IACV;IACA,OAAO;QAACD;QAAO5F;QAAUH;KAAS;AACpC"}