<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منارة المحاسبة والمالية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        body {
            font-family: 'Cairo', sans-serif;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-16">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                منارة المحاسبة والمالية
            </h1>
            <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                منصة تعليمية ذكية متكاملة للتمويل والمحاسبة في الجزائر، مصممة وفقاً لنظام LMD 
                مع تقنيات الذكاء الاصطناعي للتعلم التكيفي
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <button class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg">
                    ابدأ التعلم الآن
                </button>
                <button class="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                    استكشف الدورات
                </button>
            </div>
            
            <!-- Status indicators -->
            <div class="bg-white rounded-lg p-6 shadow-lg mb-8 max-w-2xl mx-auto">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">حالة النظام</h3>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <div class="flex items-center gap-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">الخادم الخلفي: متصل</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">GraphQL API: جاهز</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">قاعدة البيانات: Prisma</span>
                    </div>
                </div>
                <div class="mt-4 text-sm text-gray-500">
                    <p>Backend API: <a href="http://localhost:4000/graphql" target="_blank" class="text-blue-600 hover:underline">http://localhost:4000/graphql</a></p>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <span class="text-2xl">🧠</span>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">التعلم الذكي</h3>
                <p class="text-gray-600 leading-relaxed">نظام ذكي يتكيف مع مستواك ووتيرة تعلمك لتحقيق أفضل النتائج</p>
            </div>
            
            <div class="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                    <span class="text-2xl">📚</span>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">محتوى شامل</h3>
                <p class="text-gray-600 leading-relaxed">مناهج متكاملة تغطي جميع مستويات LMD مع التركيز على النظام الجزائري</p>
            </div>
            
            <div class="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <span class="text-2xl">🎯</span>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">محاكيات عملية</h3>
                <p class="text-gray-600 leading-relaxed">تطبيق عملي للمفاهيم من خلال محاكيات واقعية للسوق الجزائري</p>
            </div>
        </div>

        <div class="mt-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white text-center">
            <h3 class="text-2xl font-bold mb-4">
                ابدأ رحلتك التعليمية اليوم
            </h3>
            <p class="text-blue-100 mb-6 max-w-2xl mx-auto leading-relaxed">
                انضم إلى آلاف الطلاب الذين يطورون مهاراتهم في المحاسبة والمالية 
                مع منصة منارة المتطورة
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                    إنشاء حساب مجاني
                </button>
                <button class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                    استكشف الدورات
                </button>
            </div>
        </div>
    </div>

    <script>
        // Test GraphQL connection
        fetch('http://localhost:4000/graphql', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                query: `
                    query {
                        __schema {
                            types {
                                name
                            }
                        }
                    }
                `
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('GraphQL API is working:', data);
        })
        .catch(error => {
            console.error('GraphQL API error:', error);
        });
    </script>
</body>
</html>
