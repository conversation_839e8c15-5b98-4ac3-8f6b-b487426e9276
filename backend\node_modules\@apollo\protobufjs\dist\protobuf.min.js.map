{"version": 3, "sources": ["lib/prelude.js", "../node_modules/@protobufjs/aspromise/index.js", "../node_modules/@protobufjs/base64/index.js", "../node_modules/@protobufjs/codegen/index.js", "../node_modules/@protobufjs/eventemitter/index.js", "../node_modules/@protobufjs/fetch/index.js", "../node_modules/@protobufjs/float/index.js", "../node_modules/@protobufjs/inquire/index.js", "../node_modules/@protobufjs/path/index.js", "../node_modules/@protobufjs/pool/index.js", "../node_modules/@protobufjs/utf8/index.js", "../src/common.js", "../src/converter.js", "../src/decoder.js", "../src/encoder.js", "../src/enum.js", "../src/field.js", "../src/index-light.js", "../src/index-minimal.js", "../src/index", "../src/mapfield.js", "../src/message.js", "../src/method.js", "../src/namespace.js", "../src/object.js", "../src/oneof.js", "../src/parse.js", "../src/reader.js", "../src/reader_buffer.js", "../src/root.js", "../src/roots.js", "../src/rpc.js", "../src/rpc/service.js", "../src/service.js", "../src/tokenize.js", "../src/type.js", "../src/types.js", "../src/util.js", "../src/util/longbits.js", "../src/util/minimal.js", "../src/verifier.js", "../src/wrappers.js", "../src/writer.js", "../src/writer_buffer.js"], "names": ["undefined", "modules", "cache", "entries", "protobuf", "1", "require", "module", "exports", "fn", "ctx", "params", "Array", "arguments", "length", "offset", "index", "pending", "Promise", "resolve", "reject", "err", "apply", "base64", "string", "p", "n", "char<PERSON>t", "Math", "ceil", "b64", "s64", "i", "encode", "buffer", "start", "end", "t", "parts", "chunk", "j", "b", "push", "String", "fromCharCode", "slice", "join", "invalidEncoding", "decode", "c", "charCodeAt", "Error", "test", "codegen", "functionParams", "functionName", "body", "Codegen", "formatStringOrScope", "source", "toString", "verbose", "console", "log", "scopeKeys", "Object", "keys", "scopeParams", "scopeValues", "scopeOffset", "Function", "formatParams", "formatOffset", "replace", "$0", "$1", "value", "floor", "JSON", "stringify", "functionNameOverride", "EventEmitter", "this", "_listeners", "prototype", "on", "evt", "off", "listeners", "splice", "emit", "args", "fetch", "<PERSON><PERSON><PERSON><PERSON>", "fs", "inquire", "filename", "options", "callback", "xhr", "readFile", "contents", "XMLHttpRequest", "binary", "onreadystatechange", "readyState", "status", "response", "responseText", "Uint8Array", "overrideMimeType", "responseType", "open", "send", "factory", "Float32Array", "f32", "f8b", "le", "writeFloat_f32_cpy", "val", "buf", "pos", "writeFloat_f32_rev", "readFloat_f32_cpy", "readFloat_f32_rev", "writeFloatLE", "writeFloatBE", "readFloatLE", "readFloatBE", "writeFloat_ieee754", "writeUint", "sign", "isNaN", "round", "exponent", "LN2", "pow", "readFloat_ieee754", "readUint", "uint", "mantissa", "NaN", "Infinity", "bind", "writeUintLE", "writeUintBE", "readUintLE", "readUintBE", "Float64Array", "f64", "writeDouble_f64_cpy", "writeDouble_f64_rev", "readDouble_f64_cpy", "readDouble_f64_rev", "writeDoubleLE", "writeDoubleBE", "readDoubleLE", "readDoubleBE", "writeDouble_ieee754", "off0", "off1", "readDouble_ieee754", "lo", "hi", "moduleName", "mod", "eval", "e", "path", "isAbsolute", "normalize", "split", "absolute", "prefix", "shift", "originPath", "include<PERSON>ath", "alreadyNormalized", "alloc", "size", "SIZE", "MAX", "slab", "call", "utf8", "len", "read", "write", "c1", "c2", "common", "timeType", "commonRe", "name", "json", "nested", "google", "Any", "fields", "type_url", "type", "id", "Duration", "seconds", "nanos", "Timestamp", "Empty", "Struct", "keyType", "Value", "oneofs", "kind", "oneof", "nullValue", "numberValue", "stringValue", "boolValue", "structValue", "listValue", "Null<PERSON><PERSON>ue", "values", "NULL_VALUE", "ListValue", "rule", "DoubleValue", "FloatValue", "Int64Value", "UInt64Value", "Int32Value", "UInt32Value", "BoolValue", "StringValue", "BytesValue", "FieldMask", "paths", "get", "file", "converter", "Enum", "util", "genValuePartial_fromObject", "gen", "field", "fieldIndex", "prop", "ref", "resolvedType", "repeated", "typeDefault", "fullName", "isUnsigned", "genValuePartial_toObject", "fromObject", "mtype", "fieldsArray", "safeProp", "map", "arrayRef", "useToArray", "toObject", "sort", "compareFieldsById", "repeatedFields", "mapFields", "normalFields", "partOf", "valuesById", "long", "low", "high", "unsigned", "toNumber", "bytes", "arrayDefault", "hasKs2", "_fieldsArray", "indexOf", "filter", "group", "types", "basic", "packed", "rfield", "required", "wireType", "mapKey", "genTypePartial", "optional", "key", "preEncoded", "ReflectionObject", "create", "constructor", "className", "Namespace", "comment", "comments", "TypeError", "reserved", "fromJSON", "enm", "toJSON", "toJSONOptions", "keepComments", "add", "isString", "isInteger", "isReservedId", "isReservedName", "allow_alias", "remove", "Field", "Type", "ruleRe", "extend", "isObject", "toLowerCase", "message", "defaultValue", "<PERSON>", "extensionField", "declaringField", "_packed", "defineProperty", "getOption", "setOption", "ifNotSet", "resolved", "defaults", "parent", "lookupTypeOrEnum", "fromNumber", "freeze", "new<PERSON>uffer", "emptyObject", "emptyArray", "ctor", "d", "fieldId", "fieldType", "fieldRule", "decorateType", "decorateEnum", "fieldName", "default", "_configure", "Type_", "build", "load", "root", "Root", "loadSync", "encoder", "decoder", "verifier", "OneOf", "MapField", "Service", "Method", "Message", "wrappers", "configure", "Reader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Writer", "BufferWriter", "rpc", "roots", "tokenize", "parse", "resolvedKeyType", "fieldKeyType", "fieldValueType", "properties", "$type", "writer", "encodeDelimited", "reader", "decodeDelimited", "verify", "object", "requestType", "requestStream", "responseStream", "resolvedRequestType", "resolvedResponseType", "lookupType", "arrayToJSON", "array", "obj", "_nested<PERSON><PERSON>y", "clearCache", "namespace", "addJSON", "toArray", "nested<PERSON><PERSON><PERSON>", "nested<PERSON><PERSON>", "names", "methods", "getEnum", "prev", "setOptions", "onAdd", "onRemove", "define", "isArray", "ptr", "part", "resolveAll", "lookup", "filterTypes", "parentAlreadyChecked", "found", "lookupEnum", "lookupService", "Service_", "Enum_", "defineProperties", "unshift", "_handleAdd", "_handleRemove", "Root_", "fieldNames", "addFieldsToParent", "oneofName", "oneOfGetter", "set", "oneOfSetter", "keepCase", "base10Re", "base10NegRe", "base16Re", "base16NegRe", "base8Re", "base8NegRe", "numberRe", "nameRe", "typeRefRe", "fqTypeRefRe", "pkg", "imports", "weakImports", "syntax", "token", "tn", "alternateCommentMode", "next", "peek", "skip", "cmnt", "head", "isProto3", "applyCase", "camelCase", "illegal", "insideTryCatch", "line", "readString", "readValue", "acceptTypeRef", "substring", "parseInt", "parseFloat", "parseNumber", "readRanges", "target", "acceptStrings", "parseId", "acceptNegative", "parsePackage", "parseImport", "whichImports", "parseSyntax", "parse<PERSON><PERSON><PERSON>", "parseOption", "ifBlock", "valueType", "parseInlineOptions", "parseMapField", "parseField", "parseOneOf", "extensions", "parseType", "dummy", "parseEnumValue", "parseEnum", "service", "commentText", "method", "parseMethod", "parseService", "reference", "parseExtension", "fnIf", "fnElse", "trailingLine", "lcFirst", "ucFirst", "parseGroup", "isCustom", "parseOptionValue", "package", "LongBits", "indexOutOfRange", "write<PERSON><PERSON>th", "RangeError", "create_array", "readLongVarint", "bits", "readFixed32_end", "readFixed64", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_slice", "subarray", "uint32", "int32", "sint32", "bool", "fixed32", "sfixed32", "float", "double", "skipType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_", "merge", "int64", "uint64", "sint64", "zzDecode", "fixed64", "sfixed64", "utf8Slice", "min", "deferred", "files", "SYNC", "<PERSON><PERSON><PERSON>", "self", "sync", "finish", "cb", "getBundledFileName", "idx", "lastIndexOf", "altname", "process", "parsed", "queued", "weak", "setTimeout", "readFileSync", "isNode", "exposeRe", "tryHandleExtension", "extendedType", "sisterField", "parse_", "common_", "rpcImpl", "requestDelimited", "responseDelimited", "rpcCall", "requestCtor", "responseCtor", "request", "endedByRPC", "_methodsArray", "inherited", "methodsArray", "rpcService", "methodName", "isReserved", "m", "q", "s", "delimRe", "stringDoubleRe", "stringSingleRe", "setCommentRe", "setCommentAltRe", "setCommentSplitRe", "whitespaceRe", "unescapeRe", "unescapeMap", "0", "r", "unescape", "str", "commentType", "commentLine", "commentLineEmpty", "stack", "<PERSON><PERSON><PERSON><PERSON>", "subject", "setComment", "commentOffset", "lines", "trim", "isDoubleSlashCommentLine", "startOffset", "endOffset", "findEndOfLine", "lineText", "cursor", "re", "lastIndex", "match", "exec", "repeat", "curr", "isDoc", "expected", "actual", "ret", "_fieldsById", "_oneofsArray", "_ctor", "fieldsById", "oneofsArray", "generateConstructor", "ctorProperties", "setup", "wrapper", "originalThis", "fork", "l<PERSON>im", "typeName", "bake", "o", "safePropBackslashRe", "safePropQuoteRe", "toUpperCase", "camelCaseRe", "a", "decorateRoot", "enumerable", "decorateEnumIndex", "zero", "zzEncode", "zeroHash", "from", "fromString", "toLong", "fromHash", "hash", "toHash", "mask", "part0", "part1", "part2", "dst", "src", "newError", "CustomError", "captureStackTrace", "pool", "global", "window", "versions", "node", "Number", "isFinite", "isset", "isSet", "hasOwnProperty", "utf8Write", "_B<PERSON>er_from", "_Buffer_allocUnsafe", "sizeOrArray", "env", "ENABLE_LONG", "dcodeIO", "key2Re", "key32Re", "key64Re", "longToHash", "longFromHash", "fromBits", "ProtocolError", "fieldMap", "longs", "enums", "encoding", "allocUnsafe", "seenFirstField", "invalid", "genVerifyKey", "genVerifyValue", "oneofProp", "substr", "Op", "noop", "State", "tail", "states", "writeByte", "VarintOp", "writeVarint64", "writeFixed32", "_push", "writeBytes", "reset", "BufferWriter_", "writeBytesBuffer", "copy", "writeStringBuffer", "byteLength", "$require", "$module", "amd", "isLong"], "mappings": ";;;;;CAAA,SAAAA,IAAA,aAAA,IAAAC,EAAAC,EAAAC,EAcAC,EAdAH,EAiCA,CAAAI,EAAA,CAAA,SAAAC,EAAAC,GChCAA,EAAAC,QAmBA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAC,EAAA,EACAC,GAAA,EACA,KAAAD,EAAAH,UAAAC,QACAH,EAAAI,KAAAF,UAAAG,KACA,OAAA,IAAAE,QAAA,SAAAC,EAAAC,GACAT,EAAAI,GAAA,SAAAM,GACA,GAAAJ,EAEA,GADAA,GAAA,EACAI,EACAD,EAAAC,OACA,CAGA,IAFA,IAAAV,EAAAC,MAAAC,UAAAC,OAAA,GACAC,EAAA,EACAA,EAAAJ,EAAAG,QACAH,EAAAI,KAAAF,UAAAE,GACAI,EAAAG,MAAA,KAAAX,KAIA,IACAF,EAAAa,MAAAZ,GAAA,KAAAC,GACA,MAAAU,GACAJ,IACAA,GAAA,EACAG,EAAAC,gCCxCA,IAAAE,EAAAf,EAOAe,EAAAT,OAAA,SAAAU,GACA,IAAAC,EAAAD,EAAAV,OACA,IAAAW,EACA,OAAA,EAEA,IADA,IAAAC,EAAA,EACA,IAAAD,EAAA,GAAA,MAAAD,EAAAG,OAAAF,MACAC,EACA,OAAAE,KAAAC,KAAA,EAAAL,EAAAV,QAAA,EAAAY,GAUA,IANA,IAAAI,EAAAlB,MAAA,IAGAmB,EAAAnB,MAAA,KAGAoB,EAAA,EAAAA,EAAA,IACAD,EAAAD,EAAAE,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,EAAAA,EAAA,GAAA,IAAAA,IASAT,EAAAU,OAAA,SAAAC,EAAAC,EAAAC,GAMA,IALA,IAIAC,EAJAC,EAAA,KACAC,EAAA,GACAP,EAAA,EACAQ,EAAA,EAEAL,EAAAC,GAAA,CACA,IAAAK,EAAAP,EAAAC,KACA,OAAAK,GACA,KAAA,EACAD,EAAAP,KAAAF,EAAAW,GAAA,GACAJ,GAAA,EAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAJ,GAAA,GAAAI,IAAA,EACAD,EAAA,EACA,MACA,KAAA,EACAD,EAAAP,KAAAF,EAAAO,EAAAI,GAAA,GACAF,EAAAP,KAAAF,EAAA,GAAAW,GACAD,EAAA,EAGA,KAAAR,KACAM,IAAAA,EAAA,KAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,IACAP,EAAA,GASA,OANAQ,IACAD,EAAAP,KAAAF,EAAAO,GACAE,EAAAP,KAAA,GACA,IAAAQ,IACAD,EAAAP,KAAA,KAEAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KAGA,IAAAe,EAAA,mBAUAxB,EAAAyB,OAAA,SAAAxB,EAAAU,EAAAnB,GAIA,IAHA,IAEAsB,EAFAF,EAAApB,EACAyB,EAAA,EAEAR,EAAA,EAAAA,EAAAR,EAAAV,QAAA,CACA,IAAAmC,EAAAzB,EAAA0B,WAAAlB,KACA,GAAA,KAAAiB,GAAA,EAAAT,EACA,MACA,IAAAS,EAAAlB,EAAAkB,MAAAjD,GACA,MAAAmD,MAAAJ,GACA,OAAAP,GACA,KAAA,EACAH,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,KAAAsB,GAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,MAAA,GAAAsB,IAAA,GAAA,GAAAY,IAAA,EACAZ,EAAAY,EACAT,EAAA,EACA,MACA,KAAA,EACAN,EAAAnB,MAAA,EAAAsB,IAAA,EAAAY,EACAT,EAAA,GAIA,GAAA,IAAAA,EACA,MAAAW,MAAAJ,GACA,OAAAhC,EAAAoB,GAQAZ,EAAA6B,KAAA,SAAA5B,GACA,MAAA,mEAAA4B,KAAA5B,0BC/HA,SAAA6B,EAAAC,EAAAC,GAGA,iBAAAD,IACAC,EAAAD,EACAA,EAAAtD,IAGA,IAAAwD,EAAA,GAYA,SAAAC,EAAAC,GAIA,GAAA,iBAAAA,EAAA,CACA,IAAAC,EAAAC,IAIA,GAHAP,EAAAQ,SACAC,QAAAC,IAAA,YAAAJ,GACAA,EAAA,UAAAA,EACAD,EAAA,CAKA,IAJA,IAAAM,EAAAC,OAAAC,KAAAR,GACAS,EAAAvD,MAAAoD,EAAAlD,OAAA,GACAsD,EAAAxD,MAAAoD,EAAAlD,QACAuD,EAAA,EACAA,EAAAL,EAAAlD,QACAqD,EAAAE,GAAAL,EAAAK,GACAD,EAAAC,GAAAX,EAAAM,EAAAK,MAGA,OADAF,EAAAE,GAAAV,EACAW,SAAAhD,MAAA,KAAA6C,GAAA7C,MAAA,KAAA8C,GAEA,OAAAE,SAAAX,EAAAW,GAMA,IAFA,IAAAC,EAAA3D,MAAAC,UAAAC,OAAA,GACA0D,EAAA,EACAA,EAAAD,EAAAzD,QACAyD,EAAAC,GAAA3D,YAAA2D,GAYA,GAXAA,EAAA,EACAd,EAAAA,EAAAe,QAAA,eAAA,SAAAC,EAAAC,GACA,IAAAC,EAAAL,EAAAC,KACA,OAAAG,GACA,IAAA,IAAA,IAAA,IAAA,OAAAC,EAAAjC,GACA,IAAA,IAAA,OAAAf,KAAAiD,MAAAD,GAAAjC,GACA,IAAA,IAAA,OAAAmC,KAAAC,UAAAH,GACA,IAAA,IAAA,OAAAA,EAAAjC,GAEA,MAAA,MAEA6B,IAAAD,EAAAzD,OACA,MAAAqC,MAAA,4BAEA,OADAK,EAAAd,KAAAgB,GACAD,EAGA,SAAAG,EAAAoB,GACA,MAAA,aAAAA,GAAAzB,GAAA,IAAA,KAAAD,GAAAA,EAAAR,KAAA,MAAA,IAAA,SAAAU,EAAAV,KAAA,QAAA,MAIA,OADAW,EAAAG,SAAAA,EACAH,GAhFAlD,EAAAC,QAAA6C,GAiGAQ,SAAA,wBCzFA,SAAAoB,IAOAC,KAAAC,EAAA,IAfA5E,EAAAC,QAAAyE,GAyBAG,UAAAC,GAAA,SAAAC,EAAA7E,EAAAC,GAKA,OAJAwE,KAAAC,EAAAG,KAAAJ,KAAAC,EAAAG,GAAA,KAAA5C,KAAA,CACAjC,GAAAA,EACAC,IAAAA,GAAAwE,OAEAA,MASAD,EAAAG,UAAAG,IAAA,SAAAD,EAAA7E,GACA,GAAA6E,IAAAtF,GACAkF,KAAAC,EAAA,QAEA,GAAA1E,IAAAT,GACAkF,KAAAC,EAAAG,GAAA,QAGA,IADA,IAAAE,EAAAN,KAAAC,EAAAG,GACAtD,EAAA,EAAAA,EAAAwD,EAAA1E,QACA0E,EAAAxD,GAAAvB,KAAAA,EACA+E,EAAAC,OAAAzD,EAAA,KAEAA,EAGA,OAAAkD,MASAD,EAAAG,UAAAM,KAAA,SAAAJ,GACA,IAAAE,EAAAN,KAAAC,EAAAG,GACA,GAAAE,EAAA,CAGA,IAFA,IAAAG,EAAA,GACA3D,EAAA,EACAA,EAAAnB,UAAAC,QACA6E,EAAAjD,KAAA7B,UAAAmB,MACA,IAAAA,EAAA,EAAAA,EAAAwD,EAAA1E,QACA0E,EAAAxD,GAAAvB,GAAAa,MAAAkE,EAAAxD,KAAAtB,IAAAiF,GAEA,OAAAT,4BCzEA3E,EAAAC,QAAAoF,EAEA,IAAAC,EAAAvF,EAAA,GAGAwF,EAFAxF,EAAA,EAEAyF,CAAA,MA2BA,SAAAH,EAAAI,EAAAC,EAAAC,GAOA,MANA,mBAAAD,GACAC,EAAAD,EACAA,EAAA,IACAA,IACAA,EAAA,IAEAC,GAIAD,EAAAE,KAAAL,GAAAA,EAAAM,SACAN,EAAAM,SAAAJ,EAAA,SAAA3E,EAAAgF,GACA,OAAAhF,GAAA,oBAAAiF,eACAV,EAAAO,IAAAH,EAAAC,EAAAC,GACA7E,EACA6E,EAAA7E,GACA6E,EAAA,KAAAD,EAAAM,OAAAF,EAAAA,EAAAzC,SAAA,WAIAgC,EAAAO,IAAAH,EAAAC,EAAAC,GAbAL,EAAAD,EAAAV,KAAAc,EAAAC,GAqCAL,EAAAO,IAAA,SAAAH,EAAAC,EAAAC,GACA,IAAAC,EAAA,IAAAG,eACAH,EAAAK,mBAAA,WAEA,GAAA,IAAAL,EAAAM,WACA,OAAAzG,GAKA,GAAA,IAAAmG,EAAAO,QAAA,MAAAP,EAAAO,OACA,OAAAR,EAAA/C,MAAA,UAAAgD,EAAAO,SAIA,GAAAT,EAAAM,OAAA,CACA,IAAArE,EAAAiE,EAAAQ,SACA,IAAAzE,EAAA,CACAA,EAAA,GACA,IAAA,IAAAF,EAAA,EAAAA,EAAAmE,EAAAS,aAAA9F,SAAAkB,EACAE,EAAAQ,KAAA,IAAAyD,EAAAS,aAAA1D,WAAAlB,IAEA,OAAAkE,EAAA,KAAA,oBAAAW,WAAA,IAAAA,WAAA3E,GAAAA,GAEA,OAAAgE,EAAA,KAAAC,EAAAS,eAGAX,EAAAM,SAEA,qBAAAJ,GACAA,EAAAW,iBAAA,sCACAX,EAAAY,aAAA,eAGAZ,EAAAa,KAAA,MAAAhB,GACAG,EAAAc,qCC1BA,SAAAC,EAAA1G,GAwNA,MArNA,oBAAA2G,aAAA,WAEA,IAAAC,EAAA,IAAAD,aAAA,EAAA,IACAE,EAAA,IAAAR,WAAAO,EAAAlF,QACAoF,EAAA,MAAAD,EAAA,GAEA,SAAAE,EAAAC,EAAAC,EAAAC,GACAN,EAAA,GAAAI,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAGA,SAAAM,EAAAH,EAAAC,EAAAC,GACAN,EAAA,GAAAI,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAQA,SAAAO,EAAAH,EAAAC,GAKA,OAJAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAN,EAAA,GAGA,SAAAS,EAAAJ,EAAAC,GAKA,OAJAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAN,EAAA,GAjBA5G,EAAAsH,aAAAR,EAAAC,EAAAI,EAEAnH,EAAAuH,aAAAT,EAAAK,EAAAJ,EAmBA/G,EAAAwH,YAAAV,EAAAM,EAAAC,EAEArH,EAAAyH,YAAAX,EAAAO,EAAAD,EA9CA,GAiDA,WAEA,SAAAM,EAAAC,EAAAX,EAAAC,EAAAC,GACA,IAAAU,EAAAZ,EAAA,EAAA,EAAA,EAGA,GAFAY,IACAZ,GAAAA,GACA,IAAAA,EACAW,EAAA,EAAA,EAAAX,EAAA,EAAA,WAAAC,EAAAC,QACA,GAAAW,MAAAb,GACAW,EAAA,WAAAV,EAAAC,QACA,GAAA,qBAAAF,EACAW,GAAAC,GAAA,GAAA,cAAA,EAAAX,EAAAC,QACA,GAAAF,EAAA,sBACAW,GAAAC,GAAA,GAAAxG,KAAA0G,MAAAd,EAAA,yBAAA,EAAAC,EAAAC,OACA,CACA,IAAAa,EAAA3G,KAAAiD,MAAAjD,KAAAmC,IAAAyD,GAAA5F,KAAA4G,KAEAL,GAAAC,GAAA,GAAAG,EAAA,KAAA,GADA,QAAA3G,KAAA0G,MAAAd,EAAA5F,KAAA6G,IAAA,GAAAF,GAAA,YACA,EAAAd,EAAAC,IAOA,SAAAgB,EAAAC,EAAAlB,EAAAC,GACA,IAAAkB,EAAAD,EAAAlB,EAAAC,GACAU,EAAA,GAAAQ,GAAA,IAAA,EACAL,EAAAK,IAAA,GAAA,IACAC,EAAA,QAAAD,EACA,OAAA,MAAAL,EACAM,EACAC,IACAV,GAAAW,EAAAA,GACA,IAAAR,EACA,qBAAAH,EAAAS,EACAT,EAAAxG,KAAA6G,IAAA,EAAAF,EAAA,MAAAM,EAAA,SAdArI,EAAAsH,aAAAI,EAAAc,KAAA,KAAAC,GACAzI,EAAAuH,aAAAG,EAAAc,KAAA,KAAAE,GAgBA1I,EAAAwH,YAAAU,EAAAM,KAAA,KAAAG,GACA3I,EAAAyH,YAAAS,EAAAM,KAAA,KAAAI,GAvCA,GA4CA,oBAAAC,aAAA,WAEA,IAAAC,EAAA,IAAAD,aAAA,EAAA,IACAhC,EAAA,IAAAR,WAAAyC,EAAApH,QACAoF,EAAA,MAAAD,EAAA,GAEA,SAAAkC,EAAA/B,EAAAC,EAAAC,GACA4B,EAAA,GAAA9B,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAGA,SAAAmC,EAAAhC,EAAAC,EAAAC,GACA4B,EAAA,GAAA9B,EACAC,EAAAC,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GACAI,EAAAC,EAAA,GAAAL,EAAA,GAQA,SAAAoC,EAAAhC,EAAAC,GASA,OARAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACA4B,EAAA,GAGA,SAAAI,EAAAjC,EAAAC,GASA,OARAL,EAAA,GAAAI,EAAAC,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACAL,EAAA,GAAAI,EAAAC,EAAA,GACA4B,EAAA,GAzBA9I,EAAAmJ,cAAArC,EAAAiC,EAAAC,EAEAhJ,EAAAoJ,cAAAtC,EAAAkC,EAAAD,EA2BA/I,EAAAqJ,aAAAvC,EAAAmC,EAAAC,EAEAlJ,EAAAsJ,aAAAxC,EAAAoC,EAAAD,EA9DA,GAiEA,WAEA,SAAAM,EAAA5B,EAAA6B,EAAAC,EAAAzC,EAAAC,EAAAC,GACA,IAAAU,EAAAZ,EAAA,EAAA,EAAA,EAGA,GAFAY,IACAZ,GAAAA,GACA,IAAAA,EACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,EAAA,EAAA,EAAAX,EAAA,EAAA,WAAAC,EAAAC,EAAAuC,QACA,GAAA5B,MAAAb,GACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,EAAA,WAAAV,EAAAC,EAAAuC,QACA,GAAA,sBAAAzC,EACAW,EAAA,EAAAV,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAA,cAAA,EAAAX,EAAAC,EAAAuC,OACA,CACA,IAAApB,EACA,GAAArB,EAAA,uBAEAW,GADAU,EAAArB,EAAA,UACA,EAAAC,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAAS,EAAA,cAAA,EAAApB,EAAAC,EAAAuC,OACA,CACA,IAAA1B,EAAA3G,KAAAiD,MAAAjD,KAAAmC,IAAAyD,GAAA5F,KAAA4G,KACA,OAAAD,IACAA,EAAA,MAEAJ,EAAA,kBADAU,EAAArB,EAAA5F,KAAA6G,IAAA,GAAAF,MACA,EAAAd,EAAAC,EAAAsC,GACA7B,GAAAC,GAAA,GAAAG,EAAA,MAAA,GAAA,QAAAM,EAAA,WAAA,EAAApB,EAAAC,EAAAuC,KAQA,SAAAC,EAAAvB,EAAAqB,EAAAC,EAAAxC,EAAAC,GACA,IAAAyC,EAAAxB,EAAAlB,EAAAC,EAAAsC,GACAI,EAAAzB,EAAAlB,EAAAC,EAAAuC,GACA7B,EAAA,GAAAgC,GAAA,IAAA,EACA7B,EAAA6B,IAAA,GAAA,KACAvB,EAAA,YAAA,QAAAuB,GAAAD,EACA,OAAA,OAAA5B,EACAM,EACAC,IACAV,GAAAW,EAAAA,GACA,IAAAR,EACA,OAAAH,EAAAS,EACAT,EAAAxG,KAAA6G,IAAA,EAAAF,EAAA,OAAAM,EAAA,kBAfArI,EAAAmJ,cAAAI,EAAAf,KAAA,KAAAC,EAAA,EAAA,GACAzI,EAAAoJ,cAAAG,EAAAf,KAAA,KAAAE,EAAA,EAAA,GAiBA1I,EAAAqJ,aAAAK,EAAAlB,KAAA,KAAAG,EAAA,EAAA,GACA3I,EAAAsJ,aAAAI,EAAAlB,KAAA,KAAAI,EAAA,EAAA,GAnDA,GAuDA5I,EAKA,SAAAyI,EAAAzB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAGA,SAAA0B,EAAA1B,EAAAC,EAAAC,GACAD,EAAAC,GAAAF,IAAA,GACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAA,IAAAF,EAGA,SAAA2B,EAAA1B,EAAAC,GACA,OAAAD,EAAAC,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,MAAA,EAGA,SAAA0B,EAAA3B,EAAAC,GACA,OAAAD,EAAAC,IAAA,GACAD,EAAAC,EAAA,IAAA,GACAD,EAAAC,EAAA,IAAA,EACAD,EAAAC,EAAA,MAAA,EA3UAnH,EAAAC,QAAA0G,EAAAA,2BCOA,SAAAnB,EAAAsE,GACA,IACA,IAAAC,EAAAC,KAAA,UAAAA,CAAAF,GACA,GAAAC,IAAAA,EAAAxJ,QAAAmD,OAAAC,KAAAoG,GAAAxJ,QACA,OAAAwJ,EACA,MAAAE,IACA,OAAA,KAdAjK,EAAAC,QAAAuF,0BCMA,IAAA0E,EAAAjK,EAEAkK,EAMAD,EAAAC,WAAA,SAAAD,GACA,MAAA,eAAArH,KAAAqH,IAGAE,EAMAF,EAAAE,UAAA,SAAAF,GAGA,IAAAnI,GAFAmI,EAAAA,EAAAhG,QAAA,MAAA,KACAA,QAAA,UAAA,MACAmG,MAAA,KACAC,EAAAH,EAAAD,GACAK,EAAA,GACAD,IACAC,EAAAxI,EAAAyI,QAAA,KACA,IAAA,IAAA/I,EAAA,EAAAA,EAAAM,EAAAxB,QACA,OAAAwB,EAAAN,GACA,EAAAA,GAAA,OAAAM,EAAAN,EAAA,GACAM,EAAAmD,SAAAzD,EAAA,GACA6I,EACAvI,EAAAmD,OAAAzD,EAAA,KAEAA,EACA,MAAAM,EAAAN,GACAM,EAAAmD,OAAAzD,EAAA,KAEAA,EAEA,OAAA8I,EAAAxI,EAAAQ,KAAA,MAUA2H,EAAAtJ,QAAA,SAAA6J,EAAAC,EAAAC,GAGA,OAFAA,IACAD,EAAAN,EAAAM,IACAP,EAAAO,GACAA,GACAC,IACAF,EAAAL,EAAAK,KACAA,EAAAA,EAAAvG,QAAA,iBAAA,KAAA3D,OAAA6J,EAAAK,EAAA,IAAAC,GAAAA,0BC9DA1K,EAAAC,QA6BA,SAAA2K,EAAAtI,EAAAuI,GACA,IAAAC,EAAAD,GAAA,KACAE,EAAAD,IAAA,EACAE,EAAA,KACAxK,EAAAsK,EACA,OAAA,SAAAD,GACA,GAAAA,EAAA,GAAAE,EAAAF,EACA,OAAAD,EAAAC,GACAC,EAAAtK,EAAAqK,IACAG,EAAAJ,EAAAE,GACAtK,EAAA,GAEA,IAAA0G,EAAA5E,EAAA2I,KAAAD,EAAAxK,EAAAA,GAAAqK,GAGA,OAFA,EAAArK,IACAA,EAAA,GAAA,EAAAA,IACA0G,6BCtCA,IAAAgE,EAAAjL,EAOAiL,EAAA3K,OAAA,SAAAU,GAGA,IAFA,IAAAkK,EAAA,EACAzI,EAAA,EACAjB,EAAA,EAAAA,EAAAR,EAAAV,SAAAkB,GACAiB,EAAAzB,EAAA0B,WAAAlB,IACA,IACA0J,GAAA,EACAzI,EAAA,KACAyI,GAAA,EACA,QAAA,MAAAzI,IAAA,QAAA,MAAAzB,EAAA0B,WAAAlB,EAAA,OACAA,EACA0J,GAAA,GAEAA,GAAA,EAEA,OAAAA,GAUAD,EAAAE,KAAA,SAAAzJ,EAAAC,EAAAC,GAEA,GADAA,EAAAD,EACA,EACA,MAAA,GAKA,IAJA,IAGAE,EAHAC,EAAA,KACAC,EAAA,GACAP,EAAA,EAEAG,EAAAC,IACAC,EAAAH,EAAAC,MACA,IACAI,EAAAP,KAAAK,EACA,IAAAA,GAAAA,EAAA,IACAE,EAAAP,MAAA,GAAAK,IAAA,EAAA,GAAAH,EAAAC,KACA,IAAAE,GAAAA,EAAA,KACAA,IAAA,EAAAA,IAAA,IAAA,GAAAH,EAAAC,OAAA,IAAA,GAAAD,EAAAC,OAAA,EAAA,GAAAD,EAAAC,MAAA,MACAI,EAAAP,KAAA,OAAAK,GAAA,IACAE,EAAAP,KAAA,OAAA,KAAAK,IAEAE,EAAAP,MAAA,GAAAK,IAAA,IAAA,GAAAH,EAAAC,OAAA,EAAA,GAAAD,EAAAC,KACA,KAAAH,KACAM,IAAAA,EAAA,KAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,IACAP,EAAA,GAGA,OAAAM,GACAN,GACAM,EAAAI,KAAAC,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KACAM,EAAAQ,KAAA,KAEAH,OAAAC,aAAAtB,MAAAqB,OAAAJ,EAAAM,MAAA,EAAAb,KAUAyJ,EAAAG,MAAA,SAAApK,EAAAU,EAAAnB,GAIA,IAHA,IACA8K,EACAC,EAFA3J,EAAApB,EAGAiB,EAAA,EAAAA,EAAAR,EAAAV,SAAAkB,GACA6J,EAAArK,EAAA0B,WAAAlB,IACA,IACAE,EAAAnB,KAAA8K,GACAA,EAAA,KACA3J,EAAAnB,KAAA8K,GAAA,EAAA,KAEA,QAAA,MAAAA,IAAA,QAAA,OAAAC,EAAAtK,EAAA0B,WAAAlB,EAAA,MACA6J,EAAA,QAAA,KAAAA,IAAA,KAAA,KAAAC,KACA9J,EACAE,EAAAnB,KAAA8K,GAAA,GAAA,IACA3J,EAAAnB,KAAA8K,GAAA,GAAA,GAAA,KAIA3J,EAAAnB,KAAA8K,GAAA,GAAA,IAHA3J,EAAAnB,KAAA8K,GAAA,EAAA,GAAA,KANA3J,EAAAnB,KAAA,GAAA8K,EAAA,KAcA,OAAA9K,EAAAoB,0BCtGA5B,EAAAC,QAAAuL,EAEA,IA+DAC,EA/DAC,EAAA,QAsBA,SAAAF,EAAAG,EAAAC,GACAF,EAAA7I,KAAA8I,KACAA,EAAA,mBAAAA,EAAA,SACAC,EAAA,CAAAC,OAAA,CAAAC,OAAA,CAAAD,OAAA,CAAAhM,SAAA,CAAAgM,OAAAD,QAEAJ,EAAAG,GAAAC,EAYAJ,EAAA,MAAA,CAUAO,IAAA,CACAC,OAAA,CACAC,SAAA,CACAC,KAAA,SACAC,GAAA,GAEA9H,MAAA,CACA6H,KAAA,QACAC,GAAA,OAQAX,EAAA,WAAA,CAUAY,SAAAX,EAAA,CACAO,OAAA,CACAK,QAAA,CACAH,KAAA,QACAC,GAAA,GAEAG,MAAA,CACAJ,KAAA,QACAC,GAAA,OAMAX,EAAA,YAAA,CAUAe,UAAAd,IAGAD,EAAA,QAAA,CAOAgB,MAAA,CACAR,OAAA,MAIAR,EAAA,SAAA,CASAiB,OAAA,CACAT,OAAA,CACAA,OAAA,CACAU,QAAA,SACAR,KAAA,QACAC,GAAA,KAkBAQ,MAAA,CACAC,OAAA,CACAC,KAAA,CACAC,MAAA,CACA,YACA,cACA,cACA,YACA,cACA,eAIAd,OAAA,CACAe,UAAA,CACAb,KAAA,YACAC,GAAA,GAEAa,YAAA,CACAd,KAAA,SACAC,GAAA,GAEAc,YAAA,CACAf,KAAA,SACAC,GAAA,GAEAe,UAAA,CACAhB,KAAA,OACAC,GAAA,GAEAgB,YAAA,CACAjB,KAAA,SACAC,GAAA,GAEAiB,UAAA,CACAlB,KAAA,YACAC,GAAA,KAKAkB,UAAA,CACAC,OAAA,CACAC,WAAA,IAWAC,UAAA,CACAxB,OAAA,CACAsB,OAAA,CACAG,KAAA,WACAvB,KAAA,QACAC,GAAA,OAMAX,EAAA,WAAA,CASAkC,YAAA,CACA1B,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYAwB,WAAA,CACA3B,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,KAYAyB,WAAA,CACA5B,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,KAYA0B,YAAA,CACA7B,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYA2B,WAAA,CACA9B,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,KAYA4B,YAAA,CACA/B,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYA6B,UAAA,CACAhC,OAAA,CACA3H,MAAA,CACA6H,KAAA,OACAC,GAAA,KAYA8B,YAAA,CACAjC,OAAA,CACA3H,MAAA,CACA6H,KAAA,SACAC,GAAA,KAYA+B,WAAA,CACAlC,OAAA,CACA3H,MAAA,CACA6H,KAAA,QACAC,GAAA,OAMAX,EAAA,aAAA,CASA2C,UAAA,CACAnC,OAAA,CACAoC,MAAA,CACAX,KAAA,WACAvB,KAAA,SACAC,GAAA,OAqBAX,EAAA6C,IAAA,SAAAC,GACA,OAAA9C,EAAA8C,IAAA,+BCxYA,IAAAC,EAAAtO,EAEAuO,EAAAzO,EAAA,IACA0O,EAAA1O,EAAA,IAWA,SAAA2O,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAKA,GAHAA,IAAAtP,KACAsP,EAAA,IAAAD,GAEAF,EAAAI,aACA,GAAAJ,EAAAI,wBAAAR,EAAA,CAAAG,EACA,cAAAI,GACA,IAAA,IAAAzB,EAAAsB,EAAAI,aAAA1B,OAAA3J,EAAAD,OAAAC,KAAA2J,GAAA7L,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAmN,EAAAK,UAAA3B,EAAA3J,EAAAlC,MAAAmN,EAAAM,aAAAP,EACA,YACAA,EACA,UAAAhL,EAAAlC,GADAkN,CAEA,WAAArB,EAAA3J,EAAAlC,IAFAkN,CAGA,SAAAG,EAAAxB,EAAA3J,EAAAlC,IAHAkN,CAIA,SACAA,EACA,UACAA,EACA,2BAAAI,EADAJ,CAEA,sBAAAC,EAAAO,SAAA,oBAFAR,CAGA,+BAAAG,EAAAD,EAAAE,OACA,CACA,IAAAK,GAAA,EACA,OAAAR,EAAA1C,MACA,IAAA,SACA,IAAA,QAAAyC,EACA,iBAAAG,EAAAC,GACA,MACA,IAAA,SACA,IAAA,UAAAJ,EACA,aAAAG,EAAAC,GACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,WAAAJ,EACA,WAAAG,EAAAC,GACA,MACA,IAAA,SACAK,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAT,EACA,gBADAA,CAEA,4CAAAG,EAAAC,EAAAK,EAFAT,CAGA,gCAAAI,EAHAJ,CAIA,sBAAAG,EAAAC,EAJAJ,CAKA,gCAAAI,EALAJ,CAMA,SAAAG,EAAAC,EANAJ,CAOA,gCAAAI,EAPAJ,CAQA,6DAAAG,EAAAC,EAAAA,EAAAK,EAAA,OAAA,IACA,MACA,IAAA,QAAAT,EACA,2BAAAI,EADAJ,CAEA,sEAAAI,EAAAD,EAAAC,EAFAJ,CAGA,qBAAAI,EAHAJ,CAIA,SAAAG,EAAAC,GACA,MACA,IAAA,SAAAJ,EACA,iBAAAG,EAAAC,GACA,MACA,IAAA,OAAAJ,EACA,kBAAAG,EAAAC,IAOA,OAAAJ,EA2EA,SAAAU,EAAAV,EAAAC,EAAAC,EAAAC,GAEA,GAAAF,EAAAI,aACAJ,EAAAI,wBAAAR,EAAAG,EACA,iDAAAG,EAAAD,EAAAC,EAAAA,GACAH,EACA,gCAAAG,EAAAD,EAAAC,OACA,CACA,IAAAM,GAAA,EACA,OAAAR,EAAA1C,MACA,IAAA,SACA,IAAA,QAAAyC,EACA,6CAAAG,EAAAA,EAAAA,EAAAA,GACA,MACA,IAAA,SACAM,GAAA,EAEA,IAAA,QACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAT,EACA,4BAAAG,EADAH,CAEA,uCAAAG,EAAAA,EAAAA,EAFAH,CAGA,OAHAA,CAIA,4IAAAG,EAAAA,EAAAA,EAAAA,EAAAM,EAAA,OAAA,GAAAN,GACA,MACA,IAAA,QAAAH,EACA,gHAAAG,EAAAA,EAAAA,EAAAA,EAAAA,GACA,MACA,QAAAH,EACA,UAAAG,EAAAA,IAIA,OAAAH,EApGAJ,EAAAe,WAAA,SAAAC,GAEA,IAAAvD,EAAAuD,EAAAC,YACAb,EAAAF,EAAA3L,QAAA,CAAA,KAAAyM,EAAA5D,KAAA,cAAA8C,CACA,6BADAA,CAEA,YACA,IAAAzC,EAAAzL,OAAA,OAAAoO,EACA,wBACAA,EACA,uBACA,IAAA,IAAAlN,EAAA,EAAAA,EAAAuK,EAAAzL,SAAAkB,EAAA,CACA,IAAAmN,EAAA5C,EAAAvK,GAAAb,UACAkO,EAAAL,EAAAgB,SAAAb,EAAAjD,MAGA,GAAAiD,EAAAc,IAAAf,EACA,WAAAG,EADAH,CAEA,4BAAAG,EAFAH,CAGA,sBAAAC,EAAAO,SAAA,oBAHAR,CAIA,SAAAG,EAJAH,CAKA,oDAAAG,GACAJ,EAAAC,EAAAC,EAAAnN,EAAAqN,EAAA,UAAAJ,CACA,IADAA,CAEA,UAGA,GAAAE,EAAAK,SAAA,CACAN,EAAA,WAAAG,GACA,IAAAa,EAAA,IAAAb,EACAF,EAAAgB,eAEAjB,EAAA,SADAgB,EAAA,QAAAf,EAAAzC,IAEAwC,EAAA,uEACAG,EAAAA,EAAAa,EAAAb,EAAAa,EAAAb,IAEAH,EACA,yBAAAgB,EADAhB,CAEA,sBAAAC,EAAAO,SAAA,mBAFAR,CAGA,SAAAG,EAHAH,CAIA,gCAAAgB,GACAjB,EAAAC,EAAAC,EAAAnN,EAAAqN,EAAA,MAAAa,EAAA,MAAAjB,CACA,IADAA,CAEA,UAIAE,EAAAI,wBAAAR,GAAAG,EACA,iBAAAG,GACAJ,EAAAC,EAAAC,EAAAnN,EAAAqN,GACAF,EAAAI,wBAAAR,GAAAG,EACA,KAEA,OAAAA,EACA,aAwDAJ,EAAAsB,SAAA,SAAAN,GAEA,IAAAvD,EAAAuD,EAAAC,YAAAlN,QAAAwN,KAAArB,EAAAsB,mBACA,IAAA/D,EAAAzL,OACA,OAAAkO,EAAA3L,SAAA2L,CAAA,aAUA,IATA,IAAAE,EAAAF,EAAA3L,QAAA,CAAA,IAAA,KAAAyM,EAAA5D,KAAA,YAAA8C,CACA,SADAA,CAEA,OAFAA,CAGA,YAEAuB,EAAA,GACAC,EAAA,GACAC,EAAA,GACAzO,EAAA,EACAA,EAAAuK,EAAAzL,SAAAkB,EACAuK,EAAAvK,GAAA0O,SACAnE,EAAAvK,GAAAb,UAAAqO,SAAAe,EACAhE,EAAAvK,GAAAiO,IAAAO,EACAC,GAAA/N,KAAA6J,EAAAvK,IAEA,GAAAuO,EAAAzP,OAAA,CAEA,IAFAoO,EACA,6BACAlN,EAAA,EAAAA,EAAAuO,EAAAzP,SAAAkB,EAAAkN,EACA,SAAAF,EAAAgB,SAAAO,EAAAvO,GAAAkK,OACAgD,EACA,KAGA,GAAAsB,EAAA1P,OAAA,CAEA,IAFAoO,EACA,8BACAlN,EAAA,EAAAA,EAAAwO,EAAA1P,SAAAkB,EAAAkN,EACA,SAAAF,EAAAgB,SAAAQ,EAAAxO,GAAAkK,OACAgD,EACA,KAGA,GAAAuB,EAAA3P,OAAA,CAEA,IAFAoO,EACA,mBACAlN,EAAA,EAAAA,EAAAyO,EAAA3P,SAAAkB,EAAA,CACA,IAAAmN,EAAAsB,EAAAzO,GACAqN,EAAAL,EAAAgB,SAAAb,EAAAjD,MACA,GAAAiD,EAAAI,wBAAAR,EAAAG,EACA,6BAAAG,EAAAF,EAAAI,aAAAoB,WAAAxB,EAAAM,aAAAN,EAAAM,kBACA,GAAAN,EAAAyB,KAAA1B,EACA,iBADAA,CAEA,gCAAAC,EAAAM,YAAAoB,IAAA1B,EAAAM,YAAAqB,KAAA3B,EAAAM,YAAAsB,SAFA7B,CAGA,oEAAAG,EAHAH,CAIA,QAJAA,CAKA,6BAAAG,EAAAF,EAAAM,YAAA7L,WAAAuL,EAAAM,YAAAuB,iBACA,GAAA7B,EAAA8B,MAAA,CACA,IAAAC,EAAA,IAAAtQ,MAAAwE,UAAAvC,MAAA2I,KAAA2D,EAAAM,aAAA3M,KAAA,KAAA,IACAoM,EACA,6BAAAG,EAAA1M,OAAAC,aAAAtB,MAAAqB,OAAAwM,EAAAM,aADAP,CAEA,QAFAA,CAGA,SAAAG,EAAA6B,EAHAhC,CAIA,6CAAAG,EAAAA,EAJAH,CAKA,UACAA,EACA,SAAAG,EAAAF,EAAAM,aACAP,EACA,KAEA,IAAAiC,GAAA,EACA,IAAAnP,EAAA,EAAAA,EAAAuK,EAAAzL,SAAAkB,EAAA,CACAmN,EAAA5C,EAAAvK,GAAA,IACAhB,EAAA8O,EAAAsB,EAAAC,QAAAlC,GACAE,EAAAL,EAAAgB,SAAAb,EAAAjD,MACAiD,EAAAc,KACAkB,IAAAA,GAAA,EAAAjC,EACA,YACAA,EACA,0CAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,kCACAU,EAAAV,EAAAC,EAAAnO,EAAAqO,EAAA,WAAAO,CACA,MACAT,EAAAK,UAAAN,EACA,uBAAAG,EAAAA,EADAH,CAEA,SAAAG,EAFAH,CAGA,iCAAAG,GACAO,EAAAV,EAAAC,EAAAnO,EAAAqO,EAAA,MAAAO,CACA,OACAV,EACA,uCAAAG,EAAAF,EAAAjD,MACA0D,EAAAV,EAAAC,EAAAnO,EAAAqO,GACAF,EAAAuB,QAAAxB,EACA,eADAA,CAEA,SAAAF,EAAAgB,SAAAb,EAAAuB,OAAAxE,MAAAiD,EAAAjD,OAEAgD,EACA,KAEA,OAAAA,EACA,+CC5SA3O,EAAAC,QAeA,SAAAsP,GAEA,IAAAZ,EAAAF,EAAA3L,QAAA,CAAA,IAAA,KAAAyM,EAAA5D,KAAA,UAAA8C,CACA,6BADAA,CAEA,qBAFAA,CAGA,qDAAAc,EAAAC,YAAAuB,OAAA,SAAAnC,GAAA,OAAAA,EAAAc,MAAAnP,OAAA,KAAA,IAHAkO,CAIA,kBAJAA,CAKA,oBACAc,EAAAyB,OAAArC,EACA,gBADAA,CAEA,SACAA,EACA,kBAGA,IADA,IAAAlN,EAAA,EACAA,EAAA8N,EAAAC,YAAAjP,SAAAkB,EAAA,CACA,IAAAmN,EAAAW,EAAAsB,EAAApP,GAAAb,UACAsL,EAAA0C,EAAAI,wBAAAR,EAAA,QAAAI,EAAA1C,KACA6C,EAAA,IAAAN,EAAAgB,SAAAb,EAAAjD,MAAAgD,EACA,WAAAC,EAAAzC,IAGAyC,EAAAc,KAAAf,EACA,iBADAA,CAEA,4BAAAI,EAFAJ,CAGA,QAAAI,EAHAJ,CAIA,WAAAC,EAAAlC,QAJAiC,CAKA,WACAsC,EAAAZ,KAAAzB,EAAAlC,WAAAjN,GACAwR,EAAAC,MAAAhF,KAAAzM,GAAAkP,EACA,8EAAAI,EAAAtN,GACAkN,EACA,sDAAAI,EAAA7C,GAEA+E,EAAAC,MAAAhF,KAAAzM,GAAAkP,EACA,uCAAAI,EAAAtN,GACAkN,EACA,eAAAI,EAAA7C,IAIA0C,EAAAK,UAAAN,EAEA,uBAAAI,EAAAA,EAFAJ,CAGA,QAAAI,GAGAkC,EAAAE,OAAAjF,KAAAzM,IAAAkP,EACA,iBADAA,CAEA,0BAFAA,CAGA,kBAHAA,CAIA,kBAAAI,EAAA7C,EAJAyC,CAKA,SAGAsC,EAAAC,MAAAhF,KAAAzM,GAAAkP,EAAAC,EAAAI,aAAAgC,MACA,+BACA,0CAAAjC,EAAAtN,GACAkN,EACA,kBAAAI,EAAA7C,IAGA+E,EAAAC,MAAAhF,KAAAzM,GAAAkP,EAAAC,EAAAI,aAAAgC,MACA,yBACA,oCAAAjC,EAAAtN,GACAkN,EACA,YAAAI,EAAA7C,GACAyC,EACA,SAWA,IATAA,EACA,WADAA,CAEA,kBAFAA,CAGA,QAHAA,CAKA,IALAA,CAMA,KAGAlN,EAAA,EAAAA,EAAA8N,EAAAsB,EAAAtQ,SAAAkB,EAAA,CACA,IAAA2P,EAAA7B,EAAAsB,EAAApP,GACA2P,EAAAC,UAAA1C,EACA,4BAAAyC,EAAAzF,KADAgD,CAEA,4CA3FA,qBA2FAyC,EA3FAzF,KAAA,KA8FA,OAAAgD,EACA,aApGA,IAAAH,EAAAzO,EAAA,IACAkR,EAAAlR,EAAA,IACA0O,EAAA1O,EAAA,4CCJAC,EAAAC,QAuCA,SAAAsP,GAWA,IATA,IAIAR,EAJAJ,EAAAF,EAAA3L,QAAA,CAAA,IAAA,KAAAyM,EAAA5D,KAAA,UAAA8C,CACA,SADAA,CAEA,qBAKAzC,EAAAuD,EAAAC,YAAAlN,QAAAwN,KAAArB,EAAAsB,mBAEAtO,EAAA,EAAAA,EAAAuK,EAAAzL,SAAAkB,EAAA,CACA,IAAAmN,EAAA5C,EAAAvK,GAAAb,UACAH,EAAA8O,EAAAsB,EAAAC,QAAAlC,GACA1C,EAAA0C,EAAAI,wBAAAR,EAAA,QAAAI,EAAA1C,KACAoF,EAAAL,EAAAC,MAAAhF,GAIA,GAHA6C,EAAA,IAAAN,EAAAgB,SAAAb,EAAAjD,MAGAiD,EAAAc,IACAf,EACA,kDAAAI,EAAAH,EAAAjD,KADAgD,CAEA,mDAAAI,EAFAJ,CAGA,4CAAAC,EAAAzC,IAAA,EAAA,KAAA,EAAA,EAAA8E,EAAAM,OAAA3C,EAAAlC,SAAAkC,EAAAlC,SACA4E,IAAA7R,GAAAkP,EACA,oEAAAlO,EAAAsO,GACAJ,EACA,qCAAA,GAAA2C,EAAApF,EAAA6C,GACAJ,EACA,IADAA,CAEA,UAGA,GAAAC,EAAAK,SAAA,CACA,IAAAU,EAAAZ,EACAH,EAAAgB,eACAD,EAAA,QAAAf,EAAAzC,GACAwC,EAAA,SAAAgB,GACAhB,EAAA,mEACAI,EAAAA,EAAAY,EAAAZ,EAAAY,EAAAZ,IAEAJ,EAAA,2BAAAgB,EAAAA,GAEAf,EAAAuC,QAAAF,EAAAE,OAAAjF,KAAAzM,GAAAkP,EAEA,uBAAAC,EAAAzC,IAAA,EAAA,KAAA,EAFAwC,CAGA,+BAAAgB,EAHAhB,CAIA,cAAAzC,EAAAyD,EAJAhB,CAKA,eAGAA,EAEA,+BAAAgB,GACA2B,IAAA7R,GACA+R,EAAA7C,EAAAC,EAAAnO,EAAAkP,EAAA,OACAhB,EACA,0BAAAC,EAAAzC,IAAA,EAAAmF,KAAA,EAAApF,EAAAyD,IAEAhB,EACA,UAIAC,EAAA6C,UAAA9C,EACA,iDAAAI,EAAAH,EAAAjD,MAEA2F,IAAA7R,GACA+R,EAAA7C,EAAAC,EAAAnO,EAAAsO,GACAJ,EACA,uBAAAC,EAAAzC,IAAA,EAAAmF,KAAA,EAAApF,EAAA6C,GAKA,OAAAJ,EACA,aAjHA,IAAAH,EAAAzO,EAAA,IACAkR,EAAAlR,EAAA,IACA0O,EAAA1O,EAAA,IAWA,SAAAyR,EAAA7C,EAAAC,EAAAC,EAAAE,GAEA,GAAAH,EAAAI,aAAAgC,MACArC,EAAA,+CAAAE,EAAAE,GAAAH,EAAAzC,IAAA,EAAA,KAAA,GAAAyC,EAAAzC,IAAA,EAAA,KAAA,OADA,CAIA,IAAAuF,GAAA9C,EAAAzC,IAAA,EAAA,KAAA,EACAyC,EAAA+C,cACAhD,EAAA,kCAAAI,EAAAJ,CACA,eAAA+C,EADA/C,CAEA,cAAAI,EAFAJ,CAGA,YAEAA,EAAA,oDAAAE,EAAAE,EAAA2C,GACA9C,EAAA+C,cACAhD,EAAA,+CC9BA3O,EAAAC,QAAAuO,EAGA,IAAAoD,EAAA7R,EAAA,MACAyO,EAAA3J,UAAAnB,OAAAmO,OAAAD,EAAA/M,YAAAiN,YAAAtD,GAAAuD,UAAA,OAEA,IAAAC,EAAAjS,EAAA,IACA0O,EAAA1O,EAAA,IAaA,SAAAyO,EAAA7C,EAAA2B,EAAA5H,EAAAuM,EAAAC,GAGA,GAFAN,EAAA3G,KAAAtG,KAAAgH,EAAAjG,GAEA4H,GAAA,iBAAAA,EACA,MAAA6E,UAAA,4BAoCA,GA9BAxN,KAAAyL,WAAA,GAMAzL,KAAA2I,OAAA5J,OAAAmO,OAAAlN,KAAAyL,YAMAzL,KAAAsN,QAAAA,EAMAtN,KAAAuN,SAAAA,GAAA,GAMAvN,KAAAyN,SAAA3S,GAMA6N,EACA,IAAA,IAAA3J,EAAAD,OAAAC,KAAA2J,GAAA7L,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACA,iBAAA6L,EAAA3J,EAAAlC,MACAkD,KAAAyL,WAAAzL,KAAA2I,OAAA3J,EAAAlC,IAAA6L,EAAA3J,EAAAlC,KAAAkC,EAAAlC,IAiBA+M,EAAA6D,SAAA,SAAA1G,EAAAC,GACA,IAAA0G,EAAA,IAAA9D,EAAA7C,EAAAC,EAAA0B,OAAA1B,EAAAlG,QAAAkG,EAAAqG,QAAArG,EAAAsG,UAEA,OADAI,EAAAF,SAAAxG,EAAAwG,SACAE,GAQA9D,EAAA3J,UAAA0N,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAhE,EAAAoB,SAAA,CACA,UAAAlL,KAAAe,QACA,SAAAf,KAAA2I,OACA,WAAA3I,KAAAyN,UAAAzN,KAAAyN,SAAA7R,OAAAoE,KAAAyN,SAAA3S,GACA,UAAAgT,EAAA9N,KAAAsN,QAAAxS,GACA,WAAAgT,EAAA9N,KAAAuN,SAAAzS,MAaA+O,EAAA3J,UAAA6N,IAAA,SAAA/G,EAAAQ,EAAA8F,GAGA,IAAAxD,EAAAkE,SAAAhH,GACA,MAAAwG,UAAA,yBAEA,IAAA1D,EAAAmE,UAAAzG,GACA,MAAAgG,UAAA,yBAEA,GAAAxN,KAAA2I,OAAA3B,KAAAlM,GACA,MAAAmD,MAAA,mBAAA+I,EAAA,QAAAhH,MAEA,GAAAA,KAAAkO,aAAA1G,GACA,MAAAvJ,MAAA,MAAAuJ,EAAA,mBAAAxH,MAEA,GAAAA,KAAAmO,eAAAnH,GACA,MAAA/I,MAAA,SAAA+I,EAAA,oBAAAhH,MAEA,GAAAA,KAAAyL,WAAAjE,KAAA1M,GAAA,CACA,IAAAkF,KAAAe,UAAAf,KAAAe,QAAAqN,YACA,MAAAnQ,MAAA,gBAAAuJ,EAAA,OAAAxH,MACAA,KAAA2I,OAAA3B,GAAAQ,OAEAxH,KAAAyL,WAAAzL,KAAA2I,OAAA3B,GAAAQ,GAAAR,EAGA,OADAhH,KAAAuN,SAAAvG,GAAAsG,GAAA,KACAtN,MAUA6J,EAAA3J,UAAAmO,OAAA,SAAArH,GAEA,IAAA8C,EAAAkE,SAAAhH,GACA,MAAAwG,UAAA,yBAEA,IAAAlL,EAAAtC,KAAA2I,OAAA3B,GACA,GAAA,MAAA1E,EACA,MAAArE,MAAA,SAAA+I,EAAA,uBAAAhH,MAMA,cAJAA,KAAAyL,WAAAnJ,UACAtC,KAAA2I,OAAA3B,UACAhH,KAAAuN,SAAAvG,GAEAhH,MAQA6J,EAAA3J,UAAAgO,aAAA,SAAA1G,GACA,OAAA6F,EAAAa,aAAAlO,KAAAyN,SAAAjG,IAQAqC,EAAA3J,UAAAiO,eAAA,SAAAnH,GACA,OAAAqG,EAAAc,eAAAnO,KAAAyN,SAAAzG,4CClLA3L,EAAAC,QAAAgT,EAGA,IAAArB,EAAA7R,EAAA,MACAkT,EAAApO,UAAAnB,OAAAmO,OAAAD,EAAA/M,YAAAiN,YAAAmB,GAAAlB,UAAA,QAEA,IAIAmB,EAJA1E,EAAAzO,EAAA,IACAkR,EAAAlR,EAAA,IACA0O,EAAA1O,EAAA,IAIAoT,EAAA,+BAyCA,SAAAF,EAAAtH,EAAAQ,EAAAD,EAAAuB,EAAA2F,EAAA1N,EAAAuM,GAcA,GAZAxD,EAAA4E,SAAA5F,IACAwE,EAAAmB,EACA1N,EAAA+H,EACAA,EAAA2F,EAAA3T,IACAgP,EAAA4E,SAAAD,KACAnB,EAAAvM,EACAA,EAAA0N,EACAA,EAAA3T,IAGAmS,EAAA3G,KAAAtG,KAAAgH,EAAAjG,IAEA+I,EAAAmE,UAAAzG,IAAAA,EAAA,EACA,MAAAgG,UAAA,qCAEA,IAAA1D,EAAAkE,SAAAzG,GACA,MAAAiG,UAAA,yBAEA,GAAA1E,IAAAhO,KAAA0T,EAAAtQ,KAAA4K,EAAAA,EAAApK,WAAAiQ,eACA,MAAAnB,UAAA,8BAEA,GAAAiB,IAAA3T,KAAAgP,EAAAkE,SAAAS,GACA,MAAAjB,UAAA,2BAMAxN,KAAA8I,KAAAA,GAAA,aAAAA,EAAAA,EAAAhO,GAMAkF,KAAAuH,KAAAA,EAMAvH,KAAAwH,GAAAA,EAMAxH,KAAAyO,OAAAA,GAAA3T,GAMAkF,KAAA0M,SAAA,aAAA5D,EAMA9I,KAAA8M,UAAA9M,KAAA0M,SAMA1M,KAAAsK,SAAA,aAAAxB,EAMA9I,KAAA+K,KAAA,EAMA/K,KAAA4O,QAAA,KAMA5O,KAAAwL,OAAA,KAMAxL,KAAAuK,YAAA,KAMAvK,KAAA6O,aAAA,KAMA7O,KAAA0L,OAAA5B,EAAAgF,MAAAxC,EAAAZ,KAAAnE,KAAAzM,GAMAkF,KAAA+L,MAAA,UAAAxE,EAMAvH,KAAAqK,aAAA,KAMArK,KAAA+O,eAAA,KAMA/O,KAAAgP,eAAA,KAOAhP,KAAAiP,EAAA,KAMAjP,KAAAsN,QAAAA,EA7JAgB,EAAAZ,SAAA,SAAA1G,EAAAC,GACA,OAAA,IAAAqH,EAAAtH,EAAAC,EAAAO,GAAAP,EAAAM,KAAAN,EAAA6B,KAAA7B,EAAAwH,OAAAxH,EAAAlG,QAAAkG,EAAAqG,UAqKAvO,OAAAmQ,eAAAZ,EAAApO,UAAA,SAAA,CACAwJ,IAAA,WAIA,OAFA,OAAA1J,KAAAiP,IACAjP,KAAAiP,GAAA,IAAAjP,KAAAmP,UAAA,WACAnP,KAAAiP,KAOAX,EAAApO,UAAAkP,UAAA,SAAApI,EAAAtH,EAAA2P,GAGA,MAFA,WAAArI,IACAhH,KAAAiP,EAAA,MACAhC,EAAA/M,UAAAkP,UAAA9I,KAAAtG,KAAAgH,EAAAtH,EAAA2P,IAwBAf,EAAApO,UAAA0N,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAhE,EAAAoB,SAAA,CACA,OAAA,aAAAlL,KAAA8I,MAAA9I,KAAA8I,MAAAhO,GACA,OAAAkF,KAAAuH,KACA,KAAAvH,KAAAwH,GACA,SAAAxH,KAAAyO,OACA,UAAAzO,KAAAe,QACA,UAAA+M,EAAA9N,KAAAsN,QAAAxS,MASAwT,EAAApO,UAAAjE,QAAA,WAEA,GAAA+D,KAAAsP,SACA,OAAAtP,KA0BA,IAxBAA,KAAAuK,YAAA+B,EAAAiD,SAAAvP,KAAAuH,SAAAzM,KACAkF,KAAAqK,cAAArK,KAAAgP,eAAAhP,KAAAgP,eAAAQ,OAAAxP,KAAAwP,QAAAC,iBAAAzP,KAAAuH,MACAvH,KAAAqK,wBAAAkE,EACAvO,KAAAuK,YAAA,KAEAvK,KAAAuK,YAAAvK,KAAAqK,aAAA1B,OAAA5J,OAAAC,KAAAgB,KAAAqK,aAAA1B,QAAA,KAIA3I,KAAAe,SAAA,MAAAf,KAAAe,QAAA,UACAf,KAAAuK,YAAAvK,KAAAe,QAAA,QACAf,KAAAqK,wBAAAR,GAAA,iBAAA7J,KAAAuK,cACAvK,KAAAuK,YAAAvK,KAAAqK,aAAA1B,OAAA3I,KAAAuK,eAIAvK,KAAAe,WACA,IAAAf,KAAAe,QAAAyL,SAAAxM,KAAAe,QAAAyL,SAAA1R,KAAAkF,KAAAqK,cAAArK,KAAAqK,wBAAAR,WACA7J,KAAAe,QAAAyL,OACAzN,OAAAC,KAAAgB,KAAAe,SAAAnF,SACAoE,KAAAe,QAAAjG,KAIAkF,KAAA0L,KACA1L,KAAAuK,YAAAT,EAAAgF,KAAAY,WAAA1P,KAAAuK,YAAA,MAAAvK,KAAAuH,KAAA9K,OAAA,IAGAsC,OAAA4Q,QACA5Q,OAAA4Q,OAAA3P,KAAAuK,kBAEA,GAAAvK,KAAA+L,OAAA,iBAAA/L,KAAAuK,YAAA,CACA,IAAAhI,EACAuH,EAAAzN,OAAA6B,KAAA8B,KAAAuK,aACAT,EAAAzN,OAAAyB,OAAAkC,KAAAuK,YAAAhI,EAAAuH,EAAA8F,UAAA9F,EAAAzN,OAAAT,OAAAoE,KAAAuK,cAAA,GAEAT,EAAAvD,KAAAG,MAAA1G,KAAAuK,YAAAhI,EAAAuH,EAAA8F,UAAA9F,EAAAvD,KAAA3K,OAAAoE,KAAAuK,cAAA,GACAvK,KAAAuK,YAAAhI,EAeA,OAXAvC,KAAA+K,IACA/K,KAAA6O,aAAA/E,EAAA+F,YACA7P,KAAAsK,SACAtK,KAAA6O,aAAA/E,EAAAgG,WAEA9P,KAAA6O,aAAA7O,KAAAuK,YAGAvK,KAAAwP,kBAAAjB,IACAvO,KAAAwP,OAAAO,KAAA7P,UAAAF,KAAAgH,MAAAhH,KAAA6O,cAEA5B,EAAA/M,UAAAjE,QAAAqK,KAAAtG,OAGAsO,EAAApO,UAAA+K,WAAA,WACA,QAAAjL,KAAAmP,UAAA,qBAGAb,EAAApO,UAAA8M,WAAA,WACA,QAAAhN,KAAAmP,UAAA,oBAuBAb,EAAA0B,EAAA,SAAAC,EAAAC,EAAAC,EAAAtB,GAUA,MAPA,mBAAAqB,EACAA,EAAApG,EAAAsG,aAAAF,GAAAlJ,KAGAkJ,GAAA,iBAAAA,IACAA,EAAApG,EAAAuG,aAAAH,GAAAlJ,MAEA,SAAA9G,EAAAoQ,GACAxG,EAAAsG,aAAAlQ,EAAAiN,aACAY,IAAA,IAAAO,EAAAgC,EAAAL,EAAAC,EAAAC,EAAA,CAAAI,QAAA1B,OAkBAP,EAAAkC,EAAA,SAAAC,GACAlC,EAAAkC,iDCxXA,IAAAvV,EAAAG,EAAAC,QAAAF,EAAA,IAEAF,EAAAwV,MAAA,QAoDAxV,EAAAyV,KAjCA,SAAA7P,EAAA8P,EAAA5P,GAMA,MALA,mBAAA4P,GACA5P,EAAA4P,EACAA,EAAA,IAAA1V,EAAA2V,MACAD,IACAA,EAAA,IAAA1V,EAAA2V,MACAD,EAAAD,KAAA7P,EAAAE,IA2CA9F,EAAA4V,SANA,SAAAhQ,EAAA8P,GAGA,OAFAA,IACAA,EAAA,IAAA1V,EAAA2V,MACAD,EAAAE,SAAAhQ,IAMA5F,EAAA6V,QAAA3V,EAAA,IACAF,EAAA8V,QAAA5V,EAAA,IACAF,EAAA+V,SAAA7V,EAAA,IACAF,EAAA0O,UAAAxO,EAAA,IAGAF,EAAA+R,iBAAA7R,EAAA,IACAF,EAAAmS,UAAAjS,EAAA,IACAF,EAAA2V,KAAAzV,EAAA,IACAF,EAAA2O,KAAAzO,EAAA,IACAF,EAAAqT,KAAAnT,EAAA,IACAF,EAAAoT,MAAAlT,EAAA,IACAF,EAAAgW,MAAA9V,EAAA,IACAF,EAAAiW,SAAA/V,EAAA,IACAF,EAAAkW,QAAAhW,EAAA,IACAF,EAAAmW,OAAAjW,EAAA,IAGAF,EAAAoW,QAAAlW,EAAA,IACAF,EAAAqW,SAAAnW,EAAA,IAGAF,EAAAoR,MAAAlR,EAAA,IACAF,EAAA4O,KAAA1O,EAAA,IAGAF,EAAA+R,iBAAAuD,EAAAtV,EAAA2V,MACA3V,EAAAmS,UAAAmD,EAAAtV,EAAAqT,KAAArT,EAAAkW,QAAAlW,EAAA2O,MACA3O,EAAA2V,KAAAL,EAAAtV,EAAAqT,MACArT,EAAAoT,MAAAkC,EAAAtV,EAAAqT,gJCtGA,IAAArT,EAAAI,EA2BA,SAAAkW,IACAtW,EAAAuW,OAAAjB,EAAAtV,EAAAwW,cACAxW,EAAA4O,KAAA0G,IArBAtV,EAAAwV,MAAA,UAGAxV,EAAAyW,OAAAvW,EAAA,IACAF,EAAA0W,aAAAxW,EAAA,IACAF,EAAAuW,OAAArW,EAAA,IACAF,EAAAwW,aAAAtW,EAAA,IAGAF,EAAA4O,KAAA1O,EAAA,IACAF,EAAA2W,IAAAzW,EAAA,IACAF,EAAA4W,MAAA1W,EAAA,IACAF,EAAAsW,UAAAA,EAaAtW,EAAAyW,OAAAnB,EAAAtV,EAAA0W,cACAJ,oEClCA,IAAAtW,EAAAG,EAAAC,QAAAF,EAAA,IAEAF,EAAAwV,MAAA,OAGAxV,EAAA6W,SAAA3W,EAAA,IACAF,EAAA8W,MAAA5W,EAAA,IACAF,EAAA2L,OAAAzL,EAAA,IAGAF,EAAA2V,KAAAL,EAAAtV,EAAAqT,KAAArT,EAAA8W,MAAA9W,EAAA2L,sDCVAxL,EAAAC,QAAA6V,EAGA,IAAA7C,EAAAlT,EAAA,MACA+V,EAAAjR,UAAAnB,OAAAmO,OAAAoB,EAAApO,YAAAiN,YAAAgE,GAAA/D,UAAA,WAEA,IAAAd,EAAAlR,EAAA,IACA0O,EAAA1O,EAAA,IAcA,SAAA+V,EAAAnK,EAAAQ,EAAAO,EAAAR,EAAAxG,EAAAuM,GAIA,GAHAgB,EAAAhI,KAAAtG,KAAAgH,EAAAQ,EAAAD,EAAAzM,GAAAA,GAAAiG,EAAAuM,IAGAxD,EAAAkE,SAAAjG,GACA,MAAAyF,UAAA,4BAMAxN,KAAA+H,QAAAA,EAMA/H,KAAAiS,gBAAA,KAGAjS,KAAA+K,KAAA,EAwBAoG,EAAAzD,SAAA,SAAA1G,EAAAC,GACA,OAAA,IAAAkK,EAAAnK,EAAAC,EAAAO,GAAAP,EAAAc,QAAAd,EAAAM,KAAAN,EAAAlG,QAAAkG,EAAAqG,UAQA6D,EAAAjR,UAAA0N,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAhE,EAAAoB,SAAA,CACA,UAAAlL,KAAA+H,QACA,OAAA/H,KAAAuH,KACA,KAAAvH,KAAAwH,GACA,SAAAxH,KAAAyO,OACA,UAAAzO,KAAAe,QACA,UAAA+M,EAAA9N,KAAAsN,QAAAxS,MAOAqW,EAAAjR,UAAAjE,QAAA,WACA,GAAA+D,KAAAsP,SACA,OAAAtP,KAGA,GAAAsM,EAAAM,OAAA5M,KAAA+H,WAAAjN,GACA,MAAAmD,MAAA,qBAAA+B,KAAA+H,SAEA,OAAAuG,EAAApO,UAAAjE,QAAAqK,KAAAtG,OAaAmR,EAAAnB,EAAA,SAAAC,EAAAiC,EAAAC,GAUA,MAPA,mBAAAA,EACAA,EAAArI,EAAAsG,aAAA+B,GAAAnL,KAGAmL,GAAA,iBAAAA,IACAA,EAAArI,EAAAuG,aAAA8B,GAAAnL,MAEA,SAAA9G,EAAAoQ,GACAxG,EAAAsG,aAAAlQ,EAAAiN,aACAY,IAAA,IAAAoD,EAAAb,EAAAL,EAAAiC,EAAAC,8CC1HA9W,EAAAC,QAAAgW,EAEA,IAAAxH,EAAA1O,EAAA,IASA,SAAAkW,EAAAc,GAEA,GAAAA,EACA,IAAA,IAAApT,EAAAD,OAAAC,KAAAoT,GAAAtV,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAkD,KAAAhB,EAAAlC,IAAAsV,EAAApT,EAAAlC,IA0BAwU,EAAApE,OAAA,SAAAkF,GACA,OAAApS,KAAAqS,MAAAnF,OAAAkF,IAWAd,EAAAvU,OAAA,SAAA6R,EAAA0D,GACA,OAAAtS,KAAAqS,MAAAtV,OAAA6R,EAAA0D,IAWAhB,EAAAiB,gBAAA,SAAA3D,EAAA0D,GACA,OAAAtS,KAAAqS,MAAAE,gBAAA3D,EAAA0D,IAYAhB,EAAAxT,OAAA,SAAA0U,GACA,OAAAxS,KAAAqS,MAAAvU,OAAA0U,IAYAlB,EAAAmB,gBAAA,SAAAD,GACA,OAAAxS,KAAAqS,MAAAI,gBAAAD,IAUAlB,EAAAoB,OAAA,SAAA9D,GACA,OAAA5O,KAAAqS,MAAAK,OAAA9D,IAUA0C,EAAA3G,WAAA,SAAAgI,GACA,OAAA3S,KAAAqS,MAAA1H,WAAAgI,IAWArB,EAAApG,SAAA,SAAA0D,EAAA7N,GACA,OAAAf,KAAAqS,MAAAnH,SAAA0D,EAAA7N,IAOAuQ,EAAApR,UAAA0N,OAAA,WACA,OAAA5N,KAAAqS,MAAAnH,SAAAlL,KAAA8J,EAAA+D,4CCtIAxS,EAAAC,QAAA+V,EAGA,IAAApE,EAAA7R,EAAA,MACAiW,EAAAnR,UAAAnB,OAAAmO,OAAAD,EAAA/M,YAAAiN,YAAAkE,GAAAjE,UAAA,SAEA,IAAAtD,EAAA1O,EAAA,IAgBA,SAAAiW,EAAArK,EAAAO,EAAAqL,EAAA/Q,EAAAgR,EAAAC,EAAA/R,EAAAuM,GAYA,GATAxD,EAAA4E,SAAAmE,IACA9R,EAAA8R,EACAA,EAAAC,EAAAhY,IACAgP,EAAA4E,SAAAoE,KACA/R,EAAA+R,EACAA,EAAAhY,IAIAyM,IAAAzM,KAAAgP,EAAAkE,SAAAzG,GACA,MAAAiG,UAAA,yBAGA,IAAA1D,EAAAkE,SAAA4E,GACA,MAAApF,UAAA,gCAGA,IAAA1D,EAAAkE,SAAAnM,GACA,MAAA2L,UAAA,iCAEAP,EAAA3G,KAAAtG,KAAAgH,EAAAjG,GAMAf,KAAAuH,KAAAA,GAAA,MAMAvH,KAAA4S,YAAAA,EAMA5S,KAAA6S,gBAAAA,GAAA/X,GAMAkF,KAAA6B,aAAAA,EAMA7B,KAAA8S,iBAAAA,GAAAhY,GAMAkF,KAAA+S,oBAAA,KAMA/S,KAAAgT,qBAAA,KAMAhT,KAAAsN,QAAAA,EAqBA+D,EAAA3D,SAAA,SAAA1G,EAAAC,GACA,OAAA,IAAAoK,EAAArK,EAAAC,EAAAM,KAAAN,EAAA2L,YAAA3L,EAAApF,aAAAoF,EAAA4L,cAAA5L,EAAA6L,eAAA7L,EAAAlG,QAAAkG,EAAAqG,UAQA+D,EAAAnR,UAAA0N,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAhE,EAAAoB,SAAA,CACA,OAAA,QAAAlL,KAAAuH,MAAAvH,KAAAuH,MAAAzM,GACA,cAAAkF,KAAA4S,YACA,gBAAA5S,KAAA6S,cACA,eAAA7S,KAAA6B,aACA,iBAAA7B,KAAA8S,eACA,UAAA9S,KAAAe,QACA,UAAA+M,EAAA9N,KAAAsN,QAAAxS,MAOAuW,EAAAnR,UAAAjE,QAAA,WAGA,OAAA+D,KAAAsP,SACAtP,MAEAA,KAAA+S,oBAAA/S,KAAAwP,OAAAyD,WAAAjT,KAAA4S,aACA5S,KAAAgT,qBAAAhT,KAAAwP,OAAAyD,WAAAjT,KAAA6B,cAEAoL,EAAA/M,UAAAjE,QAAAqK,KAAAtG,0CCpJA3E,EAAAC,QAAA+R,EAGA,IAAAJ,EAAA7R,EAAA,MACAiS,EAAAnN,UAAAnB,OAAAmO,OAAAD,EAAA/M,YAAAiN,YAAAE,GAAAD,UAAA,YAEA,IAGAmB,EACA6C,EACAvH,EALAyE,EAAAlT,EAAA,IACA0O,EAAA1O,EAAA,IAoCA,SAAA8X,EAAAC,EAAAtF,GACA,IAAAsF,IAAAA,EAAAvX,OACA,OAAAd,GAEA,IADA,IAAAsY,EAAA,GACAtW,EAAA,EAAAA,EAAAqW,EAAAvX,SAAAkB,EACAsW,EAAAD,EAAArW,GAAAkK,MAAAmM,EAAArW,GAAA8Q,OAAAC,GACA,OAAAuF,EA4CA,SAAA/F,EAAArG,EAAAjG,GACAkM,EAAA3G,KAAAtG,KAAAgH,EAAAjG,GAMAf,KAAAkH,OAAApM,GAOAkF,KAAAqT,EAAA,KAGA,SAAAC,EAAAC,GAEA,OADAA,EAAAF,EAAA,KACAE,EAhFAlG,EAAAK,SAAA,SAAA1G,EAAAC,GACA,OAAA,IAAAoG,EAAArG,EAAAC,EAAAlG,SAAAyS,QAAAvM,EAAAC,SAmBAmG,EAAA6F,YAAAA,EAQA7F,EAAAa,aAAA,SAAAT,EAAAjG,GACA,GAAAiG,EACA,IAAA,IAAA3Q,EAAA,EAAAA,EAAA2Q,EAAA7R,SAAAkB,EACA,GAAA,iBAAA2Q,EAAA3Q,IAAA2Q,EAAA3Q,GAAA,IAAA0K,GAAAiG,EAAA3Q,GAAA,GAAA0K,EACA,OAAA,EACA,OAAA,GASA6F,EAAAc,eAAA,SAAAV,EAAAzG,GACA,GAAAyG,EACA,IAAA,IAAA3Q,EAAA,EAAAA,EAAA2Q,EAAA7R,SAAAkB,EACA,GAAA2Q,EAAA3Q,KAAAkK,EACA,OAAA,EACA,OAAA,GA0CAjI,OAAAmQ,eAAA7B,EAAAnN,UAAA,cAAA,CACAwJ,IAAA,WACA,OAAA1J,KAAAqT,IAAArT,KAAAqT,EAAAvJ,EAAA2J,QAAAzT,KAAAkH,YA6BAmG,EAAAnN,UAAA0N,OAAA,SAAAC,GACA,OAAA/D,EAAAoB,SAAA,CACA,UAAAlL,KAAAe,QACA,SAAAmS,EAAAlT,KAAA0T,YAAA7F,MASAR,EAAAnN,UAAAsT,QAAA,SAAAG,GAGA,GAAAA,EACA,IAAA,IAAAzM,EAAA0M,EAAA7U,OAAAC,KAAA2U,GAAA7W,EAAA,EAAAA,EAAA8W,EAAAhY,SAAAkB,EACAoK,EAAAyM,EAAAC,EAAA9W,IAJAkD,KAKA+N,KACA7G,EAAAG,SAAAvM,GACAyT,EAAAb,SACAxG,EAAAyB,SAAA7N,GACA+O,EAAA6D,SACAxG,EAAA2M,UAAA/Y,GACAsW,EAAA1D,SACAxG,EAAAM,KAAA1M,GACAwT,EAAAZ,SACAL,EAAAK,UAAAkG,EAAA9W,GAAAoK,IAIA,OAAAlH,MAQAqN,EAAAnN,UAAAwJ,IAAA,SAAA1C,GACA,OAAAhH,KAAAkH,QAAAlH,KAAAkH,OAAAF,IACA,MAUAqG,EAAAnN,UAAA4T,QAAA,SAAA9M,GACA,GAAAhH,KAAAkH,QAAAlH,KAAAkH,OAAAF,aAAA6C,EACA,OAAA7J,KAAAkH,OAAAF,GAAA2B,OACA,MAAA1K,MAAA,iBAAA+I,IAUAqG,EAAAnN,UAAA6N,IAAA,SAAA4E,GAEA,KAAAA,aAAArE,GAAAqE,EAAAlE,SAAA3T,IAAA6X,aAAApE,GAAAoE,aAAA9I,GAAA8I,aAAAvB,GAAAuB,aAAAtF,GACA,MAAAG,UAAA,wCAEA,GAAAxN,KAAAkH,OAEA,CACA,IAAA6M,EAAA/T,KAAA0J,IAAAiJ,EAAA3L,MACA,GAAA+M,EAAA,CACA,KAAAA,aAAA1G,GAAAsF,aAAAtF,IAAA0G,aAAAxF,GAAAwF,aAAA3C,EAWA,MAAAnT,MAAA,mBAAA0U,EAAA3L,KAAA,QAAAhH,MARA,IADA,IAAAkH,EAAA6M,EAAAL,YACA5W,EAAA,EAAAA,EAAAoK,EAAAtL,SAAAkB,EACA6V,EAAA5E,IAAA7G,EAAApK,IACAkD,KAAAqO,OAAA0F,GACA/T,KAAAkH,SACAlH,KAAAkH,OAAA,IACAyL,EAAAqB,WAAAD,EAAAhT,SAAA,SAZAf,KAAAkH,OAAA,GAoBA,OAFAlH,KAAAkH,OAAAyL,EAAA3L,MAAA2L,GACAsB,MAAAjU,MACAsT,EAAAtT,OAUAqN,EAAAnN,UAAAmO,OAAA,SAAAsE,GAEA,KAAAA,aAAA1F,GACA,MAAAO,UAAA,qCACA,GAAAmF,EAAAnD,SAAAxP,KACA,MAAA/B,MAAA0U,EAAA,uBAAA3S,MAOA,cALAA,KAAAkH,OAAAyL,EAAA3L,MACAjI,OAAAC,KAAAgB,KAAAkH,QAAAtL,SACAoE,KAAAkH,OAAApM,IAEA6X,EAAAuB,SAAAlU,MACAsT,EAAAtT,OASAqN,EAAAnN,UAAAiU,OAAA,SAAA5O,EAAA0B,GAEA,GAAA6C,EAAAkE,SAAAzI,GACAA,EAAAA,EAAAG,MAAA,UACA,IAAAhK,MAAA0Y,QAAA7O,GACA,MAAAiI,UAAA,gBACA,GAAAjI,GAAAA,EAAA3J,QAAA,KAAA2J,EAAA,GACA,MAAAtH,MAAA,yBAGA,IADA,IAAAoW,EAAArU,KACA,EAAAuF,EAAA3J,QAAA,CACA,IAAA0Y,EAAA/O,EAAAM,QACA,GAAAwO,EAAAnN,QAAAmN,EAAAnN,OAAAoN,IAEA,MADAD,EAAAA,EAAAnN,OAAAoN,cACAjH,GACA,MAAApP,MAAA,kDAEAoW,EAAAtG,IAAAsG,EAAA,IAAAhH,EAAAiH,IAIA,OAFArN,GACAoN,EAAAb,QAAAvM,GACAoN,GAOAhH,EAAAnN,UAAAqU,WAAA,WAEA,IADA,IAAArN,EAAAlH,KAAA0T,YAAA5W,EAAA,EACAA,EAAAoK,EAAAtL,QACAsL,EAAApK,aAAAuQ,EACAnG,EAAApK,KAAAyX,aAEArN,EAAApK,KAAAb,UACA,OAAA+D,KAAA/D,WAUAoR,EAAAnN,UAAAsU,OAAA,SAAAjP,EAAAkP,EAAAC,GASA,GANA,kBAAAD,GACAC,EAAAD,EACAA,EAAA3Z,IACA2Z,IAAA/Y,MAAA0Y,QAAAK,KACAA,EAAA,CAAAA,IAEA3K,EAAAkE,SAAAzI,IAAAA,EAAA3J,OAAA,CACA,GAAA,MAAA2J,EACA,OAAAvF,KAAA4Q,KACArL,EAAAA,EAAAG,MAAA,UACA,IAAAH,EAAA3J,OACA,OAAAoE,KAGA,GAAA,KAAAuF,EAAA,GACA,OAAAvF,KAAA4Q,KAAA4D,OAAAjP,EAAA5H,MAAA,GAAA8W,GAGA,IAAAE,EAAA3U,KAAA0J,IAAAnE,EAAA,IACA,GAAAoP,GACA,GAAA,IAAApP,EAAA3J,QACA,IAAA6Y,IAAA,EAAAA,EAAAtI,QAAAwI,EAAAxH,aACA,OAAAwH,OACA,GAAAA,aAAAtH,IAAAsH,EAAAA,EAAAH,OAAAjP,EAAA5H,MAAA,GAAA8W,GAAA,IACA,OAAAE,OAIA,IAAA,IAAA7X,EAAA,EAAAA,EAAAkD,KAAA0T,YAAA9X,SAAAkB,EACA,GAAAkD,KAAAqT,EAAAvW,aAAAuQ,IAAAsH,EAAA3U,KAAAqT,EAAAvW,GAAA0X,OAAAjP,EAAAkP,GAAA,IACA,OAAAE,EAGA,OAAA,OAAA3U,KAAAwP,QAAAkF,EACA,KACA1U,KAAAwP,OAAAgF,OAAAjP,EAAAkP,IAqBApH,EAAAnN,UAAA+S,WAAA,SAAA1N,GACA,IAAAoP,EAAA3U,KAAAwU,OAAAjP,EAAA,CAAAgJ,IACA,IAAAoG,EACA,MAAA1W,MAAA,iBAAAsH,GACA,OAAAoP,GAUAtH,EAAAnN,UAAA0U,WAAA,SAAArP,GACA,IAAAoP,EAAA3U,KAAAwU,OAAAjP,EAAA,CAAAsE,IACA,IAAA8K,EACA,MAAA1W,MAAA,iBAAAsH,EAAA,QAAAvF,MACA,OAAA2U,GAUAtH,EAAAnN,UAAAuP,iBAAA,SAAAlK,GACA,IAAAoP,EAAA3U,KAAAwU,OAAAjP,EAAA,CAAAgJ,EAAA1E,IACA,IAAA8K,EACA,MAAA1W,MAAA,yBAAAsH,EAAA,QAAAvF,MACA,OAAA2U,GAUAtH,EAAAnN,UAAA2U,cAAA,SAAAtP,GACA,IAAAoP,EAAA3U,KAAAwU,OAAAjP,EAAA,CAAA6L,IACA,IAAAuD,EACA,MAAA1W,MAAA,oBAAAsH,EAAA,QAAAvF,MACA,OAAA2U,GAIAtH,EAAAmD,EAAA,SAAAC,EAAAqE,EAAAC,GACAxG,EAAAkC,EACAW,EAAA0D,EACAjL,EAAAkL,4CC9aA1Z,EAAAC,QAAA2R,GAEAG,UAAA,mBAEA,IAEAyD,EAFA/G,EAAA1O,EAAA,IAYA,SAAA6R,EAAAjG,EAAAjG,GAEA,IAAA+I,EAAAkE,SAAAhH,GACA,MAAAwG,UAAA,yBAEA,GAAAzM,IAAA+I,EAAA4E,SAAA3N,GACA,MAAAyM,UAAA,6BAMAxN,KAAAe,QAAAA,EAMAf,KAAAgH,KAAAA,EAMAhH,KAAAwP,OAAA,KAMAxP,KAAAsP,UAAA,EAMAtP,KAAAsN,QAAA,KAMAtN,KAAAc,SAAA,KAGA/B,OAAAiW,iBAAA/H,EAAA/M,UAAA,CAQA0Q,KAAA,CACAlH,IAAA,WAEA,IADA,IAAA2K,EAAArU,KACA,OAAAqU,EAAA7E,QACA6E,EAAAA,EAAA7E,OACA,OAAA6E,IAUA7J,SAAA,CACAd,IAAA,WAGA,IAFA,IAAAnE,EAAA,CAAAvF,KAAAgH,MACAqN,EAAArU,KAAAwP,OACA6E,GACA9O,EAAA0P,QAAAZ,EAAArN,MACAqN,EAAAA,EAAA7E,OAEA,OAAAjK,EAAA3H,KAAA,SAUAqP,EAAA/M,UAAA0N,OAAA,WACA,MAAA3P,SAQAgP,EAAA/M,UAAA+T,MAAA,SAAAzE,GACAxP,KAAAwP,QAAAxP,KAAAwP,SAAAA,GACAxP,KAAAwP,OAAAnB,OAAArO,MACAA,KAAAwP,OAAAA,EACAxP,KAAAsP,UAAA,EACA,IAAAsB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAsE,EAAAlV,OAQAiN,EAAA/M,UAAAgU,SAAA,SAAA1E,GACA,IAAAoB,EAAApB,EAAAoB,KACAA,aAAAC,GACAD,EAAAuE,EAAAnV,MACAA,KAAAwP,OAAA,KACAxP,KAAAsP,UAAA,GAOArC,EAAA/M,UAAAjE,QAAA,WACA,OAAA+D,KAAAsP,UAEAtP,KAAA4Q,gBAAAC,IACA7Q,KAAAsP,UAAA,GAFAtP,MAWAiN,EAAA/M,UAAAiP,UAAA,SAAAnI,GACA,OAAAhH,KAAAe,QACAf,KAAAe,QAAAiG,GACAlM,IAUAmS,EAAA/M,UAAAkP,UAAA,SAAApI,EAAAtH,EAAA2P,GAGA,OAFAA,GAAArP,KAAAe,SAAAf,KAAAe,QAAAiG,KAAAlM,MACAkF,KAAAe,UAAAf,KAAAe,QAAA,KAAAiG,GAAAtH,GACAM,MASAiN,EAAA/M,UAAA8T,WAAA,SAAAjT,EAAAsO,GACA,GAAAtO,EACA,IAAA,IAAA/B,EAAAD,OAAAC,KAAA+B,GAAAjE,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACAkD,KAAAoP,UAAApQ,EAAAlC,GAAAiE,EAAA/B,EAAAlC,IAAAuS,GACA,OAAArP,MAOAiN,EAAA/M,UAAAxB,SAAA,WACA,IAAA0O,EAAApN,KAAAmN,YAAAC,UACA5C,EAAAxK,KAAAwK,SACA,OAAAA,EAAA5O,OACAwR,EAAA,IAAA5C,EACA4C,GAIAH,EAAAuD,EAAA,SAAA4E,GACAvE,EAAAuE,+BCrMA/Z,EAAAC,QAAA4V,EAGA,IAAAjE,EAAA7R,EAAA,MACA8V,EAAAhR,UAAAnB,OAAAmO,OAAAD,EAAA/M,YAAAiN,YAAA+D,GAAA9D,UAAA,QAEA,IAAAkB,EAAAlT,EAAA,IACA0O,EAAA1O,EAAA,IAYA,SAAA8V,EAAAlK,EAAAqO,EAAAtU,EAAAuM,GAQA,GAPA5R,MAAA0Y,QAAAiB,KACAtU,EAAAsU,EACAA,EAAAva,IAEAmS,EAAA3G,KAAAtG,KAAAgH,EAAAjG,GAGAsU,IAAAva,KAAAY,MAAA0Y,QAAAiB,GACA,MAAA7H,UAAA,+BAMAxN,KAAAmI,MAAAkN,GAAA,GAOArV,KAAA6K,YAAA,GAMA7K,KAAAsN,QAAAA,EA0CA,SAAAgI,EAAAnN,GACA,GAAAA,EAAAqH,OACA,IAAA,IAAA1S,EAAA,EAAAA,EAAAqL,EAAA0C,YAAAjP,SAAAkB,EACAqL,EAAA0C,YAAA/N,GAAA0S,QACArH,EAAAqH,OAAAzB,IAAA5F,EAAA0C,YAAA/N,IA7BAoU,EAAAxD,SAAA,SAAA1G,EAAAC,GACA,OAAA,IAAAiK,EAAAlK,EAAAC,EAAAkB,MAAAlB,EAAAlG,QAAAkG,EAAAqG,UAQA4D,EAAAhR,UAAA0N,OAAA,SAAAC,GACA,IAAAC,IAAAD,KAAAA,EAAAC,aACA,OAAAhE,EAAAoB,SAAA,CACA,UAAAlL,KAAAe,QACA,QAAAf,KAAAmI,MACA,UAAA2F,EAAA9N,KAAAsN,QAAAxS,MAuBAoW,EAAAhR,UAAA6N,IAAA,SAAA9D,GAGA,KAAAA,aAAAqE,GACA,MAAAd,UAAA,yBAQA,OANAvD,EAAAuF,QAAAvF,EAAAuF,SAAAxP,KAAAwP,QACAvF,EAAAuF,OAAAnB,OAAApE,GACAjK,KAAAmI,MAAA3K,KAAAyM,EAAAjD,MACAhH,KAAA6K,YAAArN,KAAAyM,GAEAqL,EADArL,EAAAuB,OAAAxL,MAEAA,MAQAkR,EAAAhR,UAAAmO,OAAA,SAAApE,GAGA,KAAAA,aAAAqE,GACA,MAAAd,UAAA,yBAEA,IAAA1R,EAAAkE,KAAA6K,YAAAsB,QAAAlC,GAGA,GAAAnO,EAAA,EACA,MAAAmC,MAAAgM,EAAA,uBAAAjK,MAUA,OARAA,KAAA6K,YAAAtK,OAAAzE,EAAA,IAIA,GAHAA,EAAAkE,KAAAmI,MAAAgE,QAAAlC,EAAAjD,QAIAhH,KAAAmI,MAAA5H,OAAAzE,EAAA,GAEAmO,EAAAuB,OAAA,KACAxL,MAMAkR,EAAAhR,UAAA+T,MAAA,SAAAzE,GACAvC,EAAA/M,UAAA+T,MAAA3N,KAAAtG,KAAAwP,GAGA,IAFA,IAEA1S,EAAA,EAAAA,EAAAkD,KAAAmI,MAAAvM,SAAAkB,EAAA,CACA,IAAAmN,EAAAuF,EAAA9F,IAAA1J,KAAAmI,MAAArL,IACAmN,IAAAA,EAAAuB,SACAvB,EAAAuB,OALAxL,MAMA6K,YAAArN,KAAAyM,GAIAqL,EAAAtV,OAMAkR,EAAAhR,UAAAgU,SAAA,SAAA1E,GACA,IAAA,IAAAvF,EAAAnN,EAAA,EAAAA,EAAAkD,KAAA6K,YAAAjP,SAAAkB,GACAmN,EAAAjK,KAAA6K,YAAA/N,IAAA0S,QACAvF,EAAAuF,OAAAnB,OAAApE,GACAgD,EAAA/M,UAAAgU,SAAA5N,KAAAtG,KAAAwP,IAmBA0B,EAAAlB,EAAA,WAGA,IAFA,IAAAqF,EAAA3Z,MAAAC,UAAAC,QACAE,EAAA,EACAA,EAAAH,UAAAC,QACAyZ,EAAAvZ,GAAAH,UAAAG,KACA,OAAA,SAAAoE,EAAAqV,GACAzL,EAAAsG,aAAAlQ,EAAAiN,aACAY,IAAA,IAAAmD,EAAAqE,EAAAF,IACAtW,OAAAmQ,eAAAhP,EAAAqV,EAAA,CACA7L,IAAAI,EAAA0L,YAAAH,GACAI,IAAA3L,EAAA4L,YAAAL,gDCtMAha,EAAAC,QAAA0W,GAEAlR,SAAA,KACAkR,EAAAzC,SAAA,CAAAoG,UAAA,GAEA,IAAA5D,EAAA3W,EAAA,IACAyV,EAAAzV,EAAA,IACAmT,EAAAnT,EAAA,IACAkT,EAAAlT,EAAA,IACA+V,EAAA/V,EAAA,IACA8V,EAAA9V,EAAA,IACAyO,EAAAzO,EAAA,IACAgW,EAAAhW,EAAA,IACAiW,EAAAjW,EAAA,IACAkR,EAAAlR,EAAA,IACA0O,EAAA1O,EAAA,IAEAwa,EAAA,gBACAC,EAAA,kBACAC,EAAA,qBACAC,EAAA,uBACAC,EAAA,YACAC,EAAA,cACAC,EAAA,oDACAC,EAAA,2BACAC,EAAA,+DACAC,EAAA,kCAkCA,SAAArE,EAAAvT,EAAAmS,EAAA7P,GAEA6P,aAAAC,IACA9P,EAAA6P,EACAA,EAAA,IAAAC,GAEA9P,IACAA,EAAAiR,EAAAzC,UAEA,IAQA+G,EACAC,EACAC,EACAC,EAimBAC,EA5mBAC,EAAA5E,EAAAtT,EAAAsC,EAAA6V,uBAAA,GACAC,EAAAF,EAAAE,KACArZ,EAAAmZ,EAAAnZ,KACAsZ,EAAAH,EAAAG,KACAC,EAAAJ,EAAAI,KACAC,EAAAL,EAAAK,KAEAC,GAAA,EAKAC,GAAA,EAEA7C,EAAAzD,EAEAuG,EAAApW,EAAA4U,SAAA,SAAA3O,GAAA,OAAAA,GAAA8C,EAAAsN,UAGA,SAAAC,EAAAX,EAAA1P,EAAAsQ,GACA,IAAAxW,EAAAkR,EAAAlR,SAGA,OAFAwW,IACAtF,EAAAlR,SAAA,MACA7C,MAAA,YAAA+I,GAAA,SAAA,KAAA0P,EAAA,OAAA5V,EAAAA,EAAA,KAAA,IAAA,QAAA6V,EAAAY,KAAA,KAGA,SAAAC,IACA,IACAd,EADA/N,EAAA,GAEA,EAAA,CAEA,GAAA,OAAA+N,EAAAG,MAAA,MAAAH,EACA,MAAAW,EAAAX,GAEA/N,EAAAnL,KAAAqZ,KACAE,EAAAL,GACAA,EAAAI,UACA,MAAAJ,GAAA,MAAAA,GACA,OAAA/N,EAAA/K,KAAA,IAGA,SAAA6Z,EAAAC,GACA,IAAAhB,EAAAG,IACA,OAAAH,GACA,IAAA,IACA,IAAA,IAEA,OADAlZ,EAAAkZ,GACAc,IACA,IAAA,OAAA,IAAA,OACA,OAAA,EACA,IAAA,QAAA,IAAA,QACA,OAAA,EAEA,IACA,OAuBA,SAAAd,EAAAY,GACA,IAAApU,EAAA,EACA,MAAAwT,EAAAja,OAAA,KACAyG,GAAA,EACAwT,EAAAA,EAAAiB,UAAA,IAEA,OAAAjB,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAxT,GAAAW,EAAAA,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAAD,IACA,IAAA,IACA,OAAA,EAEA,GAAAgS,EAAA1X,KAAAwY,GACA,OAAAxT,EAAA0U,SAAAlB,EAAA,IACA,GAAAZ,EAAA5X,KAAAwY,GACA,OAAAxT,EAAA0U,SAAAlB,EAAA,IACA,GAAAV,EAAA9X,KAAAwY,GACA,OAAAxT,EAAA0U,SAAAlB,EAAA,GAGA,GAAAR,EAAAhY,KAAAwY,GACA,OAAAxT,EAAA2U,WAAAnB,GAGA,MAAAW,EAAAX,EAAA,SAAAY,GAjDAQ,CAAApB,GAAA,GACA,MAAApR,GAGA,GAAAoS,GAAAtB,EAAAlY,KAAAwY,GACA,OAAAA,EAGA,MAAAW,EAAAX,EAAA,UAIA,SAAAqB,EAAAC,EAAAC,GAEA,IADA,IAAAvB,EAAAzZ,GAEAgb,GAAA,OAAAvB,EAAAI,MAAA,MAAAJ,EAGAsB,EAAAxa,KAAA,CAAAP,EAAAib,EAAArB,KAAAE,EAAA,MAAA,GAAAmB,EAAArB,KAAA5Z,IAFA+a,EAAAxa,KAAAga,KAGAT,EAAA,KAAA,KACAA,EAAA,KAgCA,SAAAmB,EAAAxB,EAAAyB,GACA,OAAAzB,GACA,IAAA,MAAA,IAAA,MAAA,IAAA,MACA,OAAA,UACA,IAAA,IACA,OAAA,EAIA,IAAAyB,GAAA,MAAAzB,EAAAja,OAAA,GACA,MAAA4a,EAAAX,EAAA,MAEA,GAAAb,EAAA3X,KAAAwY,GACA,OAAAkB,SAAAlB,EAAA,IACA,GAAAX,EAAA7X,KAAAwY,GACA,OAAAkB,SAAAlB,EAAA,IAGA,GAAAT,EAAA/X,KAAAwY,GACA,OAAAkB,SAAAlB,EAAA,GAGA,MAAAW,EAAAX,EAAA,MAGA,SAAA0B,IAGA,GAAA9B,IAAAxb,GACA,MAAAuc,EAAA,WAKA,GAHAf,EAAAO,KAGAT,EAAAlY,KAAAoY,GACA,MAAAe,EAAAf,EAAA,QAEAjC,EAAAA,EAAAF,OAAAmC,GACAS,EAAA,KAGA,SAAAsB,IACA,IACAC,EADA5B,EAAAI,IAEA,OAAAJ,GACA,IAAA,OACA4B,EAAA9B,IAAAA,EAAA,IACAK,IACA,MACA,IAAA,SACAA,IAEA,QACAyB,EAAA/B,IAAAA,EAAA,IAGAG,EAAAc,IACAT,EAAA,KACAuB,EAAA9a,KAAAkZ,GAGA,SAAA6B,IAMA,GALAxB,EAAA,KACAN,EAAAe,MACAN,EAAA,WAAAT,IAGA,WAAAA,EACA,MAAAY,EAAAZ,EAAA,UAEAM,EAAA,KAGA,SAAAyB,EAAAhJ,EAAAkH,GACA,OAAAA,GAEA,IAAA,SAGA,OAFA+B,EAAAjJ,EAAAkH,GACAK,EAAA,MACA,EAEA,IAAA,UAEA,OAuCA,SAAAvH,EAAAkH,GAGA,IAAAP,EAAAjY,KAAAwY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,aAEA,IAAAnP,EAAA,IAAAgH,EAAAmI,GACAgC,EAAAnR,EAAA,SAAAmP,GACA,IAAA8B,EAAAjR,EAAAmP,GAGA,OAAAA,GAEA,IAAA,OAoHA,SAAAlH,GACAuH,EAAA,KACA,IAAAhP,EAAA8O,IAGA,GAAAvK,EAAAM,OAAA7E,KAAAjN,GACA,MAAAuc,EAAAtP,EAAA,QAEAgP,EAAA,KACA,IAAA4B,EAAA9B,IAGA,IAAAT,EAAAlY,KAAAya,GACA,MAAAtB,EAAAsB,EAAA,QAEA5B,EAAA,KACA,IAAA/P,EAAA6P,IAGA,IAAAV,EAAAjY,KAAA8I,GACA,MAAAqQ,EAAArQ,EAAA,QAEA+P,EAAA,KACA,IAAA9M,EAAA,IAAAkH,EAAAgG,EAAAnQ,GAAAkR,EAAArB,KAAA9O,EAAA4Q,GACAD,EAAAzO,EAAA,SAAAyM,GAGA,GAAA,WAAAA,EAIA,MAAAW,EAAAX,GAHA+B,EAAAxO,EAAAyM,GACAK,EAAA,MAIA,WACA6B,EAAA3O,KAEAuF,EAAAzB,IAAA9D,GAvJA4O,CAAAtR,GACA,MAEA,IAAA,WACA,IAAA,WACA,IAAA,WACAuR,EAAAvR,EAAAmP,GACA,MAEA,IAAA,SAiJA,SAAAlH,EAAAkH,GAGA,IAAAP,EAAAjY,KAAAwY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,QAEA,IAAAvO,EAAA,IAAA+I,EAAAiG,EAAAT,IACAgC,EAAAvQ,EAAA,SAAAuO,GACA,WAAAA,GACA+B,EAAAtQ,EAAAuO,GACAK,EAAA,OAEAvZ,EAAAkZ,GACAoC,EAAA3Q,EAAA,eAGAqH,EAAAzB,IAAA5F,GAhKA4Q,CAAAxR,EAAAmP,GACA,MAEA,IAAA,aACAqB,EAAAxQ,EAAAyR,aAAAzR,EAAAyR,WAAA,KACA,MAEA,IAAA,WACAjB,EAAAxQ,EAAAkG,WAAAlG,EAAAkG,SAAA,KAAA,GACA,MAEA,QAEA,IAAAyJ,IAAAd,EAAAlY,KAAAwY,GACA,MAAAW,EAAAX,GAEAlZ,EAAAkZ,GACAoC,EAAAvR,EAAA,eAIAiI,EAAAzB,IAAAxG,GArFA0R,CAAAzJ,EAAAkH,IACA,EAEA,IAAA,OAEA,OA8NA,SAAAlH,EAAAkH,GAGA,IAAAP,EAAAjY,KAAAwY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,QAEA,IAAA/I,EAAA,IAAA9D,EAAA6M,GACAgC,EAAA/K,EAAA,SAAA+I,GACA,OAAAA,GACA,IAAA,SACA+B,EAAA9K,EAAA+I,GACAK,EAAA,KACA,MAEA,IAAA,WACAgB,EAAApK,EAAAF,WAAAE,EAAAF,SAAA,KAAA,GACA,MAEA,SAOA,SAAA+B,EAAAkH,GAGA,IAAAP,EAAAjY,KAAAwY,GACA,MAAAW,EAAAX,EAAA,QAEAK,EAAA,KACA,IAAArX,EAAAwY,EAAArB,KAAA,GACAqC,EAAA,GACAR,EAAAQ,EAAA,SAAAxC,GAGA,GAAA,WAAAA,EAIA,MAAAW,EAAAX,GAHA+B,EAAAS,EAAAxC,GACAK,EAAA,MAIA,WACA6B,EAAAM,KAEA1J,EAAAzB,IAAA2I,EAAAhX,EAAAwZ,EAAA5L,SA3BA6L,CAAAxL,EAAA+I,MAGAlH,EAAAzB,IAAAJ,GArPAyL,CAAA5J,EAAAkH,IACA,EAEA,IAAA,UAEA,OAsUA,SAAAlH,EAAAkH,GAGA,IAAAP,EAAAjY,KAAAwY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,gBAEA,IAAA2C,EAAA,IAAAjI,EAAAsF,GACAgC,EAAAW,EAAA,SAAA3C,GACA,IAAA8B,EAAAa,EAAA3C,GAAA,CAIA,GAAA,QAAAA,EAGA,MAAAW,EAAAX,IAKA,SAAAlH,EAAAkH,GAGA,IAAA4C,EAAAtC,IAEAzP,EAAAmP,EAGA,IAAAP,EAAAjY,KAAAwY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,QAEA,IACA9D,EAAAC,EACAhR,EAAAiR,EAFA9L,EAAA0P,EAIAK,EAAA,KACAA,EAAA,UAAA,KACAlE,GAAA,GAGA,IAAAuD,EAAAlY,KAAAwY,EAAAG,KACA,MAAAQ,EAAAX,GAEA9D,EAAA8D,EACAK,EAAA,KAAAA,EAAA,WAAAA,EAAA,KACAA,EAAA,UAAA,KACAjE,GAAA,GAGA,IAAAsD,EAAAlY,KAAAwY,EAAAG,KACA,MAAAQ,EAAAX,GAEA7U,EAAA6U,EACAK,EAAA,KAEA,IAAAwC,EAAA,IAAAlI,EAAArK,EAAAO,EAAAqL,EAAA/Q,EAAAgR,EAAAC,GACAyG,EAAAjM,QAAAgM,EACAZ,EAAAa,EAAA,SAAA7C,GAGA,GAAA,WAAAA,EAIA,MAAAW,EAAAX,GAHA+B,EAAAc,EAAA7C,GACAK,EAAA,OAKAvH,EAAAzB,IAAAwL,GAtDAC,CAAAH,EAAA3C,MAIAlH,EAAAzB,IAAAsL,GAxVAI,CAAAjK,EAAAkH,IACA,EAEA,IAAA,SAEA,OAwYA,SAAAlH,EAAAkH,GAGA,IAAAN,EAAAlY,KAAAwY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,aAEA,IAAAgD,EAAAhD,EACAgC,EAAA,KAAA,SAAAhC,GACA,OAAAA,GAEA,IAAA,WACA,IAAA,WACA,IAAA,WACAoC,EAAAtJ,EAAAkH,EAAAgD,GACA,MAEA,QAEA,IAAAxC,IAAAd,EAAAlY,KAAAwY,GACA,MAAAW,EAAAX,GACAlZ,EAAAkZ,GACAoC,EAAAtJ,EAAA,WAAAkK,MA9ZAC,CAAAnK,EAAAkH,IACA,EAEA,OAAA,EAGA,SAAAgC,EAAAtF,EAAAwG,EAAAC,GACA,IAAAC,EAAAnD,EAAAY,KAOA,GANAnE,IACA,iBAAAA,EAAA9F,UACA8F,EAAA9F,QAAA0J,KAEA5D,EAAAtS,SAAAkR,EAAAlR,UAEAiW,EAAA,KAAA,GAAA,CAEA,IADA,IAAAL,EACA,OAAAA,EAAAG,MACA+C,EAAAlD,GACAK,EAAA,KAAA,QAEA8C,GACAA,IACA9C,EAAA,KACA3D,GAAA,iBAAAA,EAAA9F,UACA8F,EAAA9F,QAAA0J,EAAA8C,IAoDA,SAAAhB,EAAAtJ,EAAA1G,EAAA2F,GACA,IAAAlH,EAAAsP,IACA,GAAA,UAAAtP,EAAA,CAMA,IAAA6O,EAAAlY,KAAAqJ,GACA,MAAA8P,EAAA9P,EAAA,QAEA,IAAAP,EAAA6P,IAGA,IAAAV,EAAAjY,KAAA8I,GACA,MAAAqQ,EAAArQ,EAAA,QAEAA,EAAAmQ,EAAAnQ,GACA+P,EAAA,KAEA,IAAA9M,EAAA,IAAAqE,EAAAtH,EAAAkR,EAAArB,KAAAtP,EAAAuB,EAAA2F,GACAiK,EAAAzO,EAAA,SAAAyM,GAGA,GAAA,WAAAA,EAIA,MAAAW,EAAAX,GAHA+B,EAAAxO,EAAAyM,GACAK,EAAA,MAIA,WACA6B,EAAA3O,KAEAuF,EAAAzB,IAAA9D,GAKAiN,IAAAjN,EAAAK,UAAAgC,EAAAE,OAAAjF,KAAAzM,IAAAwR,EAAAC,MAAAhF,KAAAzM,IACAmP,EAAAmF,UAAA,UAAA,GAAA,QAGA,SAAAI,EAAA1G,GACA,IAAA9B,EAAA6P,IAGA,IAAAV,EAAAjY,KAAA8I,GACA,MAAAqQ,EAAArQ,EAAA,QAEA,IAAAsJ,EAAAxG,EAAAiQ,QAAA/S,GACAA,IAAAsJ,IACAtJ,EAAA8C,EAAAkQ,QAAAhT,IACA+P,EAAA,KACA,IAAAvP,EAAA0Q,EAAArB,KACAtP,EAAA,IAAAgH,EAAAvH,GACAO,EAAA8E,OAAA,EACA,IAAApC,EAAA,IAAAqE,EAAAgC,EAAA9I,EAAAR,EAAA8B,GACAmB,EAAAnJ,SAAAkR,EAAAlR,SACA4X,EAAAnR,EAAA,SAAAmP,GACA,OAAAA,GAEA,IAAA,SACA+B,EAAAlR,EAAAmP,GACAK,EAAA,KACA,MAEA,IAAA,WACA,IAAA,WACA,IAAA,WACA+B,EAAAvR,EAAAmP,GACA,MAGA,QACA,MAAAW,EAAAX,MAGAlH,EAAAzB,IAAAxG,GACAwG,IAAA9D,GA3EAgQ,CAAAzK,EAAA1G,GAyLA,SAAA2P,EAAAjJ,EAAAkH,GACA,IAAAwD,EAAAnD,EAAA,KAAA,GAGA,IAAAX,EAAAlY,KAAAwY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,QAEA,IAAA1P,EAAA0P,EACAwD,IACAnD,EAAA,KACA/P,EAAA,IAAAA,EAAA,IACA0P,EAAAI,IACAT,EAAAnY,KAAAwY,KACA1P,GAAA0P,EACAG,MAGAE,EAAA,KAIA,SAAAoD,EAAA3K,EAAAxI,GACA,GAAA+P,EAAA,KAAA,GACA,EAAA,CAEA,IAAAZ,EAAAjY,KAAAwY,EAAAG,KACA,MAAAQ,EAAAX,EAAA,QAEA,MAAAI,IACAqD,EAAA3K,EAAAxI,EAAA,IAAA0P,IAEAK,EAAA,KACA,MAAAD,IACAqD,EAAA3K,EAAAxI,EAAA,IAAA0P,GAEAtH,EAAAI,EAAAxI,EAAA,IAAA0P,EAAAe,GAAA,KAEAV,EAAA,KAAA,UACAA,EAAA,KAAA,SAEA3H,EAAAI,EAAAxI,EAAAyQ,GAAA,IAtBA0C,CAAA3K,EAAAxI,GA0BA,SAAAoI,EAAAI,EAAAxI,EAAAtH,GACA8P,EAAAJ,WACAI,EAAAJ,UAAApI,EAAAtH,GAGA,SAAAkZ,EAAApJ,GACA,GAAAuH,EAAA,KAAA,GAAA,CACA,KACA0B,EAAAjJ,EAAA,UACAuH,EAAA,KAAA,KACAA,EAAA,KAEA,OAAAvH,EAqGA,KAAA,QAAAkH,EAAAG,MACA,OAAAH,GAEA,IAAA,UAGA,IAAAO,EACA,MAAAI,EAAAX,GAEA0B,IACA,MAEA,IAAA,SAGA,IAAAnB,EACA,MAAAI,EAAAX,GAEA2B,IACA,MAEA,IAAA,SAGA,IAAApB,EACA,MAAAI,EAAAX,GAEA6B,IACA,MAEA,IAAA,SAEAE,EAAApE,EAAAqC,GACAK,EAAA,KACA,MAEA,QAGA,GAAAyB,EAAAnE,EAAAqC,GAAA,CACAO,GAAA,EACA,SAIA,MAAAI,EAAAX,GAKA,OADA1E,EAAAlR,SAAA,KACA,CACAsZ,QAAA9D,EACAC,QAAAA,EACAC,YAAAA,EACAC,OAAAA,EACA7F,KAAAA,4FCzuBAvV,EAAAC,QAAAmW,EAEA,IAEAC,EAFA5H,EAAA1O,EAAA,IAIAif,EAAAvQ,EAAAuQ,SACA9T,EAAAuD,EAAAvD,KAGA,SAAA+T,EAAA9H,EAAA+H,GACA,OAAAC,WAAA,uBAAAhI,EAAAhQ,IAAA,OAAA+X,GAAA,GAAA,MAAA/H,EAAAhM,KASA,SAAAiL,EAAAzU,GAMAgD,KAAAuC,IAAAvF,EAMAgD,KAAAwC,IAAA,EAMAxC,KAAAwG,IAAAxJ,EAAApB,OAGA,IAwCA8D,EAxCA+a,EAAA,oBAAA9Y,WACA,SAAA3E,GACA,GAAAA,aAAA2E,YAAAjG,MAAA0Y,QAAApX,GACA,OAAA,IAAAyU,EAAAzU,GACA,MAAAiB,MAAA,mBAGA,SAAAjB,GACA,GAAAtB,MAAA0Y,QAAApX,GACA,OAAA,IAAAyU,EAAAzU,GACA,MAAAiB,MAAA,mBAkEA,SAAAyc,IAEA,IAAAC,EAAA,IAAAN,EAAA,EAAA,GACAvd,EAAA,EACA,KAAA,EAAAkD,KAAAwG,IAAAxG,KAAAwC,KAaA,CACA,KAAA1F,EAAA,IAAAA,EAAA,CAEA,GAAAkD,KAAAwC,KAAAxC,KAAAwG,IACA,MAAA8T,EAAAta,MAGA,GADA2a,EAAA1V,IAAA0V,EAAA1V,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAAmY,EAIA,OADAA,EAAA1V,IAAA0V,EAAA1V,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,SAAA,EAAA1F,KAAA,EACA6d,EAxBA,KAAA7d,EAAA,IAAAA,EAGA,GADA6d,EAAA1V,IAAA0V,EAAA1V,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAAmY,EAKA,GAFAA,EAAA1V,IAAA0V,EAAA1V,IAAA,IAAAjF,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EACAmY,EAAAzV,IAAAyV,EAAAzV,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,KAAA,EACAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAAmY,EAgBA,GAfA7d,EAAA,EAeA,EAAAkD,KAAAwG,IAAAxG,KAAAwC,KACA,KAAA1F,EAAA,IAAAA,EAGA,GADA6d,EAAAzV,IAAAyV,EAAAzV,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,EAAA,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAAmY,OAGA,KAAA7d,EAAA,IAAAA,EAAA,CAEA,GAAAkD,KAAAwC,KAAAxC,KAAAwG,IACA,MAAA8T,EAAAta,MAGA,GADA2a,EAAAzV,IAAAyV,EAAAzV,IAAA,IAAAlF,KAAAuC,IAAAvC,KAAAwC,OAAA,EAAA1F,EAAA,KAAA,EACAkD,KAAAuC,IAAAvC,KAAAwC,OAAA,IACA,OAAAmY,EAIA,MAAA1c,MAAA,2BAkCA,SAAA2c,EAAArY,EAAArF,GACA,OAAAqF,EAAArF,EAAA,GACAqF,EAAArF,EAAA,IAAA,EACAqF,EAAArF,EAAA,IAAA,GACAqF,EAAArF,EAAA,IAAA,MAAA,EA+BA,SAAA2d,IAGA,GAAA7a,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA8T,EAAAta,KAAA,GAEA,OAAA,IAAAqa,EAAAO,EAAA5a,KAAAuC,IAAAvC,KAAAwC,KAAA,GAAAoY,EAAA5a,KAAAuC,IAAAvC,KAAAwC,KAAA,IArLAiP,EAAAvE,OAAApD,EAAAgR,OACA,SAAA9d,GACA,OAAAyU,EAAAvE,OAAA,SAAAlQ,GACA,OAAA8M,EAAAgR,OAAAC,SAAA/d,GACA,IAAA0U,EAAA1U,GAEAyd,EAAAzd,KACAA,IAGAyd,EAEAhJ,EAAAvR,UAAA8a,EAAAlR,EAAApO,MAAAwE,UAAA+a,UAAAnR,EAAApO,MAAAwE,UAAAvC,MAOA8T,EAAAvR,UAAAgb,QACAxb,EAAA,WACA,WACA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,QAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,KAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,IAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EACA,GAAAA,GAAAA,GAAA,GAAAM,KAAAuC,IAAAvC,KAAAwC,OAAA,MAAA,EAAAxC,KAAAuC,IAAAvC,KAAAwC,OAAA,IAAA,OAAA9C,EAGA,IAAAM,KAAAwC,KAAA,GAAAxC,KAAAwG,IAEA,MADAxG,KAAAwC,IAAAxC,KAAAwG,IACA8T,EAAAta,KAAA,IAEA,OAAAN,IAQA+R,EAAAvR,UAAAib,MAAA,WACA,OAAA,EAAAnb,KAAAkb,UAOAzJ,EAAAvR,UAAAkb,OAAA,WACA,IAAA1b,EAAAM,KAAAkb,SACA,OAAAxb,IAAA,IAAA,EAAAA,GAAA,GAqFA+R,EAAAvR,UAAAmb,KAAA,WACA,OAAA,IAAArb,KAAAkb,UAcAzJ,EAAAvR,UAAAob,QAAA,WAGA,GAAAtb,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA8T,EAAAta,KAAA,GAEA,OAAA4a,EAAA5a,KAAAuC,IAAAvC,KAAAwC,KAAA,IAOAiP,EAAAvR,UAAAqb,SAAA,WAGA,GAAAvb,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA8T,EAAAta,KAAA,GAEA,OAAA,EAAA4a,EAAA5a,KAAAuC,IAAAvC,KAAAwC,KAAA,IAmCAiP,EAAAvR,UAAAsb,MAAA,WAGA,GAAAxb,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA8T,EAAAta,KAAA,GAEA,IAAAN,EAAAoK,EAAA0R,MAAA1Y,YAAA9C,KAAAuC,IAAAvC,KAAAwC,KAEA,OADAxC,KAAAwC,KAAA,EACA9C,GAQA+R,EAAAvR,UAAAub,OAAA,WAGA,GAAAzb,KAAAwC,IAAA,EAAAxC,KAAAwG,IACA,MAAA8T,EAAAta,KAAA,GAEA,IAAAN,EAAAoK,EAAA0R,MAAA7W,aAAA3E,KAAAuC,IAAAvC,KAAAwC,KAEA,OADAxC,KAAAwC,KAAA,EACA9C,GAOA+R,EAAAvR,UAAA6L,MAAA,WACA,IAAAnQ,EAAAoE,KAAAkb,SACAje,EAAA+C,KAAAwC,IACAtF,EAAA8C,KAAAwC,IAAA5G,EAGA,GAAAsB,EAAA8C,KAAAwG,IACA,MAAA8T,EAAAta,KAAApE,GAGA,OADAoE,KAAAwC,KAAA5G,EACAF,MAAA0Y,QAAApU,KAAAuC,KACAvC,KAAAuC,IAAA5E,MAAAV,EAAAC,GACAD,IAAAC,EACA,IAAA8C,KAAAuC,IAAA4K,YAAA,GACAnN,KAAAgb,EAAA1U,KAAAtG,KAAAuC,IAAAtF,EAAAC,IAOAuU,EAAAvR,UAAA5D,OAAA,WACA,IAAAyP,EAAA/L,KAAA+L,QACA,OAAAxF,EAAAE,KAAAsF,EAAA,EAAAA,EAAAnQ,SAQA6V,EAAAvR,UAAA6W,KAAA,SAAAnb,GACA,GAAA,iBAAAA,EAAA,CAEA,GAAAoE,KAAAwC,IAAA5G,EAAAoE,KAAAwG,IACA,MAAA8T,EAAAta,KAAApE,GACAoE,KAAAwC,KAAA5G,OAEA,GAEA,GAAAoE,KAAAwC,KAAAxC,KAAAwG,IACA,MAAA8T,EAAAta,YACA,IAAAA,KAAAuC,IAAAvC,KAAAwC,QAEA,OAAAxC,MAQAyR,EAAAvR,UAAAwb,SAAA,SAAA/O,GACA,OAAAA,GACA,KAAA,EACA3M,KAAA+W,OACA,MACA,KAAA,EACA/W,KAAA+W,KAAA,GACA,MACA,KAAA,EACA/W,KAAA+W,KAAA/W,KAAAkb,UACA,MACA,KAAA,EACA,KAAA,IAAAvO,EAAA,EAAA3M,KAAAkb,WACAlb,KAAA0b,SAAA/O,GAEA,MACA,KAAA,EACA3M,KAAA+W,KAAA,GACA,MAGA,QACA,MAAA9Y,MAAA,qBAAA0O,EAAA,cAAA3M,KAAAwC,KAEA,OAAAxC,MAGAyR,EAAAjB,EAAA,SAAAmL,GACAjK,EAAAiK,EAEA,IAAApgB,EAAAuO,EAAAgF,KAAA,SAAA,WACAhF,EAAA8R,MAAAnK,EAAAvR,UAAA,CAEA2b,MAAA,WACA,OAAAnB,EAAApU,KAAAtG,MAAAzE,IAAA,IAGAugB,OAAA,WACA,OAAApB,EAAApU,KAAAtG,MAAAzE,IAAA,IAGAwgB,OAAA,WACA,OAAArB,EAAApU,KAAAtG,MAAAgc,WAAAzgB,IAAA,IAGA0gB,QAAA,WACA,OAAApB,EAAAvU,KAAAtG,MAAAzE,IAAA,IAGA2gB,SAAA,WACA,OAAArB,EAAAvU,KAAAtG,MAAAzE,IAAA,mCC/YAF,EAAAC,QAAAoW,EAGA,IAAAD,EAAArW,EAAA,KACAsW,EAAAxR,UAAAnB,OAAAmO,OAAAuE,EAAAvR,YAAAiN,YAAAuE,EAEA,IAAA5H,EAAA1O,EAAA,IASA,SAAAsW,EAAA1U,GACAyU,EAAAnL,KAAAtG,KAAAhD,GAUA8M,EAAAgR,SACApJ,EAAAxR,UAAA8a,EAAAlR,EAAAgR,OAAA5a,UAAAvC,OAKA+T,EAAAxR,UAAA5D,OAAA,WACA,IAAAkK,EAAAxG,KAAAkb,SACA,OAAAlb,KAAAuC,IAAA4Z,UAAAnc,KAAAwC,IAAAxC,KAAAwC,IAAA9F,KAAA0f,IAAApc,KAAAwC,IAAAgE,EAAAxG,KAAAwG,yCClCAnL,EAAAC,QAAAuV,EAGA,IAAAxD,EAAAjS,EAAA,MACAyV,EAAA3Q,UAAAnB,OAAAmO,OAAAG,EAAAnN,YAAAiN,YAAA0D,GAAAzD,UAAA,OAEA,IAKAmB,EACAyD,EACAnL,EAPAyH,EAAAlT,EAAA,IACAyO,EAAAzO,EAAA,IACA8V,EAAA9V,EAAA,IACA0O,EAAA1O,EAAA,IAaA,SAAAyV,EAAA9P,GACAsM,EAAA/G,KAAAtG,KAAA,GAAAe,GAMAf,KAAAqc,SAAA,GAMArc,KAAAsc,MAAA,GA6BA,SAAAC,KApBA1L,EAAAnD,SAAA,SAAAzG,EAAA2J,GAKA,OAJAA,IACAA,EAAA,IAAAC,GACA5J,EAAAlG,SACA6P,EAAAoD,WAAA/M,EAAAlG,SACA6P,EAAA4C,QAAAvM,EAAAC,SAWA2J,EAAA3Q,UAAAsc,YAAA1S,EAAAvE,KAAAtJ,QAaA4U,EAAA3Q,UAAAyQ,KAAA,SAAAA,EAAA7P,EAAAC,EAAAC,GACA,mBAAAD,IACAC,EAAAD,EACAA,EAAAjG,IAEA,IAAA2hB,EAAAzc,KACA,IAAAgB,EACA,OAAA8I,EAAAnJ,UAAAgQ,EAAA8L,EAAA3b,EAAAC,GAEA,IAAA2b,EAAA1b,IAAAub,EAGA,SAAAI,EAAAxgB,EAAAyU,GAEA,GAAA5P,EAAA,CAEA,IAAA4b,EAAA5b,EAEA,GADAA,EAAA,KACA0b,EACA,MAAAvgB,EACAygB,EAAAzgB,EAAAyU,IAIA,SAAAiM,EAAA/b,GACA,IAAAgc,EAAAhc,EAAAic,YAAA,oBACA,IAAA,EAAAD,EAAA,CACA,IAAAE,EAAAlc,EAAA6W,UAAAmF,GACA,GAAAE,KAAAnW,EAAA,OAAAmW,EAEA,OAAA,KAIA,SAAAC,EAAAnc,EAAArC,GACA,IAGA,GAFAqL,EAAAkE,SAAAvP,IAAA,MAAAA,EAAAhC,OAAA,KACAgC,EAAAmB,KAAAoS,MAAAvT,IACAqL,EAAAkE,SAAAvP,GAEA,CACAuT,EAAAlR,SAAAA,EACA,IACAwO,EADA4N,EAAAlL,EAAAvT,EAAAge,EAAA1b,GAEAjE,EAAA,EACA,GAAAogB,EAAA3G,QACA,KAAAzZ,EAAAogB,EAAA3G,QAAA3a,SAAAkB,GACAwS,EAAAuN,EAAAK,EAAA3G,QAAAzZ,KAAA2f,EAAAD,YAAA1b,EAAAoc,EAAA3G,QAAAzZ,MACA4D,EAAA4O,GACA,GAAA4N,EAAA1G,YACA,IAAA1Z,EAAA,EAAAA,EAAAogB,EAAA1G,YAAA5a,SAAAkB,GACAwS,EAAAuN,EAAAK,EAAA1G,YAAA1Z,KAAA2f,EAAAD,YAAA1b,EAAAoc,EAAA1G,YAAA1Z,MACA4D,EAAA4O,GAAA,QAbAmN,EAAAzI,WAAAvV,EAAAsC,SAAAyS,QAAA/U,EAAAyI,QAeA,MAAA/K,GACAwgB,EAAAxgB,GAEAugB,GAAAS,GACAR,EAAA,KAAAF,GAIA,SAAA/b,EAAAI,EAAAsc,GAGA,MAAA,EAAAX,EAAAH,MAAAnQ,QAAArL,IAKA,GAHA2b,EAAAH,MAAA9e,KAAAsD,GAGAA,KAAA+F,EACA6V,EACAO,EAAAnc,EAAA+F,EAAA/F,OAEAqc,EACAE,WAAA,aACAF,EACAF,EAAAnc,EAAA+F,EAAA/F,YAOA,GAAA4b,EAAA,CACA,IAAAje,EACA,IACAA,EAAAqL,EAAAlJ,GAAA0c,aAAAxc,GAAApC,SAAA,QACA,MAAAvC,GAGA,YAFAihB,GACAT,EAAAxgB,IAGA8gB,EAAAnc,EAAArC,SAEA0e,EACArT,EAAApJ,MAAAI,EAAA,SAAA3E,EAAAsC,KACA0e,EAEAnc,IAEA7E,EAEAihB,EAEAD,GACAR,EAAA,KAAAF,GAFAE,EAAAxgB,GAKA8gB,EAAAnc,EAAArC,MAIA,IAAA0e,EAAA,EAIArT,EAAAkE,SAAAlN,KACAA,EAAA,CAAAA,IACA,IAAA,IAAAwO,EAAAxS,EAAA,EAAAA,EAAAgE,EAAAlF,SAAAkB,GACAwS,EAAAmN,EAAAD,YAAA,GAAA1b,EAAAhE,MACA4D,EAAA4O,GAEA,OAAAoN,EACAD,GACAU,GACAR,EAAA,KAAAF,GACA3hB,KAgCA+V,EAAA3Q,UAAA4Q,SAAA,SAAAhQ,EAAAC,GACA,IAAA+I,EAAAyT,OACA,MAAAtf,MAAA,iBACA,OAAA+B,KAAA2Q,KAAA7P,EAAAC,EAAAwb,IAMA1L,EAAA3Q,UAAAqU,WAAA,WACA,GAAAvU,KAAAqc,SAAAzgB,OACA,MAAAqC,MAAA,4BAAA+B,KAAAqc,SAAAtR,IAAA,SAAAd,GACA,MAAA,WAAAA,EAAAwE,OAAA,QAAAxE,EAAAuF,OAAAhF,WACA5M,KAAA,OACA,OAAAyP,EAAAnN,UAAAqU,WAAAjO,KAAAtG,OAIA,IAAAwd,EAAA,SAUA,SAAAC,EAAA7M,EAAA3G,GACA,IAAAyT,EAAAzT,EAAAuF,OAAAgF,OAAAvK,EAAAwE,QACA,GAAAiP,EAAA,CACA,IAAAC,EAAA,IAAArP,EAAArE,EAAAO,SAAAP,EAAAzC,GAAAyC,EAAA1C,KAAA0C,EAAAnB,KAAAhO,GAAAmP,EAAAlJ,SAIA,OAHA4c,EAAA3O,eAAA/E,GACA8E,eAAA4O,EACAD,EAAA3P,IAAA4P,IACA,EAEA,OAAA,EASA9M,EAAA3Q,UAAAgV,EAAA,SAAAvC,GACA,GAAAA,aAAArE,EAEAqE,EAAAlE,SAAA3T,IAAA6X,EAAA5D,gBACA0O,EAAAzd,EAAA2S,IACA3S,KAAAqc,SAAA7e,KAAAmV,QAEA,GAAAA,aAAA9I,EAEA2T,EAAAtf,KAAAyU,EAAA3L,QACA2L,EAAAnD,OAAAmD,EAAA3L,MAAA2L,EAAAhK,aAEA,KAAAgK,aAAAzB,GAAA,CAEA,GAAAyB,aAAApE,EACA,IAAA,IAAAzR,EAAA,EAAAA,EAAAkD,KAAAqc,SAAAzgB,QACA6hB,EAAAzd,EAAAA,KAAAqc,SAAAvf,IACAkD,KAAAqc,SAAA9b,OAAAzD,EAAA,KAEAA,EACA,IAAA,IAAAQ,EAAA,EAAAA,EAAAqV,EAAAe,YAAA9X,SAAA0B,EACA0C,KAAAkV,EAAAvC,EAAAU,EAAA/V,IACAkgB,EAAAtf,KAAAyU,EAAA3L,QACA2L,EAAAnD,OAAAmD,EAAA3L,MAAA2L,KAcA9B,EAAA3Q,UAAAiV,EAAA,SAAAxC,GACA,GAAAA,aAAArE,GAEA,GAAAqE,EAAAlE,SAAA3T,GACA,GAAA6X,EAAA5D,eACA4D,EAAA5D,eAAAS,OAAAnB,OAAAsE,EAAA5D,gBACA4D,EAAA5D,eAAA,SACA,CACA,IAAAjT,EAAAkE,KAAAqc,SAAAlQ,QAAAwG,IAEA,EAAA7W,GACAkE,KAAAqc,SAAA9b,OAAAzE,EAAA,SAIA,GAAA6W,aAAA9I,EAEA2T,EAAAtf,KAAAyU,EAAA3L,cACA2L,EAAAnD,OAAAmD,EAAA3L,WAEA,GAAA2L,aAAAtF,EAAA,CAEA,IAAA,IAAAvQ,EAAA,EAAAA,EAAA6V,EAAAe,YAAA9X,SAAAkB,EACAkD,KAAAmV,EAAAxC,EAAAU,EAAAvW,IAEA0gB,EAAAtf,KAAAyU,EAAA3L,cACA2L,EAAAnD,OAAAmD,EAAA3L,QAMA6J,EAAAL,EAAA,SAAAC,EAAAmN,EAAAC,GACAtP,EAAAkC,EACAuB,EAAA4L,EACA/W,EAAAgX,uDC9VAxiB,EAAAC,QAAA,4BCKAA,EA6BA8V,QAAAhW,EAAA,gCClCAC,EAAAC,QAAA8V,EAEA,IAAAtH,EAAA1O,EAAA,IAsCA,SAAAgW,EAAA0M,EAAAC,EAAAC,GAEA,GAAA,mBAAAF,EACA,MAAAtQ,UAAA,8BAEA1D,EAAA/J,aAAAuG,KAAAtG,MAMAA,KAAA8d,QAAAA,EAMA9d,KAAA+d,mBAAAA,EAMA/d,KAAAge,oBAAAA,IA1DA5M,EAAAlR,UAAAnB,OAAAmO,OAAApD,EAAA/J,aAAAG,YAAAiN,YAAAiE,GAwEAlR,UAAA+d,QAAA,SAAAA,EAAA1E,EAAA2E,EAAAC,EAAAC,EAAApd,GAEA,IAAAod,EACA,MAAA5Q,UAAA,6BAEA,IAAAiP,EAAAzc,KACA,IAAAgB,EACA,OAAA8I,EAAAnJ,UAAAsd,EAAAxB,EAAAlD,EAAA2E,EAAAC,EAAAC,GAEA,IAAA3B,EAAAqB,QAEA,OADAT,WAAA,WAAArc,EAAA/C,MAAA,mBAAA,GACAnD,GAGA,IACA,OAAA2hB,EAAAqB,QACAvE,EACA2E,EAAAzB,EAAAsB,iBAAA,kBAAA,UAAAK,GAAAzB,SACA,SAAAxgB,EAAAsF,GAEA,GAAAtF,EAEA,OADAsgB,EAAAjc,KAAA,QAAArE,EAAAod,GACAvY,EAAA7E,GAGA,GAAA,OAAAsF,EAEA,OADAgb,EAAAvf,KAAA,GACApC,GAGA,KAAA2G,aAAA0c,GACA,IACA1c,EAAA0c,EAAA1B,EAAAuB,kBAAA,kBAAA,UAAAvc,GACA,MAAAtF,GAEA,OADAsgB,EAAAjc,KAAA,QAAArE,EAAAod,GACAvY,EAAA7E,GAKA,OADAsgB,EAAAjc,KAAA,OAAAiB,EAAA8X,GACAvY,EAAA,KAAAS,KAGA,MAAAtF,GAGA,OAFAsgB,EAAAjc,KAAA,QAAArE,EAAAod,GACA8D,WAAA,WAAArc,EAAA7E,IAAA,GACArB,KASAsW,EAAAlR,UAAAhD,IAAA,SAAAmhB,GAOA,OANAre,KAAA8d,UACAO,GACAre,KAAA8d,QAAA,KAAA,KAAA,MACA9d,KAAA8d,QAAA,KACA9d,KAAAQ,KAAA,OAAAH,OAEAL,kCC3IA3E,EAAAC,QAAA8V,EAGA,IAAA/D,EAAAjS,EAAA,MACAgW,EAAAlR,UAAAnB,OAAAmO,OAAAG,EAAAnN,YAAAiN,YAAAiE,GAAAhE,UAAA,UAEA,IAAAiE,EAAAjW,EAAA,IACA0O,EAAA1O,EAAA,IACAyW,EAAAzW,EAAA,IAWA,SAAAgW,EAAApK,EAAAjG,GACAsM,EAAA/G,KAAAtG,KAAAgH,EAAAjG,GAMAf,KAAA6T,QAAA,GAOA7T,KAAAse,EAAA,KAyDA,SAAAhL,EAAA+F,GAEA,OADAA,EAAAiF,EAAA,KACAjF,EA1CAjI,EAAA1D,SAAA,SAAA1G,EAAAC,GACA,IAAAoS,EAAA,IAAAjI,EAAApK,EAAAC,EAAAlG,SAEA,GAAAkG,EAAA4M,QACA,IAAA,IAAAD,EAAA7U,OAAAC,KAAAiI,EAAA4M,SAAA/W,EAAA,EAAAA,EAAA8W,EAAAhY,SAAAkB,EACAuc,EAAAtL,IAAAsD,EAAA3D,SAAAkG,EAAA9W,GAAAmK,EAAA4M,QAAAD,EAAA9W,MAIA,OAHAmK,EAAAC,QACAmS,EAAA7F,QAAAvM,EAAAC,QACAmS,EAAA/L,QAAArG,EAAAqG,QACA+L,GAQAjI,EAAAlR,UAAA0N,OAAA,SAAAC,GACA,IAAA0Q,EAAAlR,EAAAnN,UAAA0N,OAAAtH,KAAAtG,KAAA6N,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAhE,EAAAoB,SAAA,CACA,UAAAqT,GAAAA,EAAAxd,SAAAjG,GACA,UAAAuS,EAAA6F,YAAAlT,KAAAwe,aAAA3Q,IAAA,GACA,SAAA0Q,GAAAA,EAAArX,QAAApM,GACA,UAAAgT,EAAA9N,KAAAsN,QAAAxS,MAUAiE,OAAAmQ,eAAAkC,EAAAlR,UAAA,eAAA,CACAwJ,IAAA,WACA,OAAA1J,KAAAse,IAAAte,KAAAse,EAAAxU,EAAA2J,QAAAzT,KAAA6T,aAYAzC,EAAAlR,UAAAwJ,IAAA,SAAA1C,GACA,OAAAhH,KAAA6T,QAAA7M,IACAqG,EAAAnN,UAAAwJ,IAAApD,KAAAtG,KAAAgH,IAMAoK,EAAAlR,UAAAqU,WAAA,WAEA,IADA,IAAAV,EAAA7T,KAAAwe,aACA1hB,EAAA,EAAAA,EAAA+W,EAAAjY,SAAAkB,EACA+W,EAAA/W,GAAAb,UACA,OAAAoR,EAAAnN,UAAAjE,QAAAqK,KAAAtG,OAMAoR,EAAAlR,UAAA6N,IAAA,SAAA4E,GAGA,GAAA3S,KAAA0J,IAAAiJ,EAAA3L,MACA,MAAA/I,MAAA,mBAAA0U,EAAA3L,KAAA,QAAAhH,MAEA,OAAA2S,aAAAtB,EAGAiC,GAFAtT,KAAA6T,QAAAlB,EAAA3L,MAAA2L,GACAnD,OAAAxP,MAGAqN,EAAAnN,UAAA6N,IAAAzH,KAAAtG,KAAA2S,IAMAvB,EAAAlR,UAAAmO,OAAA,SAAAsE,GACA,GAAAA,aAAAtB,EAAA,CAGA,GAAArR,KAAA6T,QAAAlB,EAAA3L,QAAA2L,EACA,MAAA1U,MAAA0U,EAAA,uBAAA3S,MAIA,cAFAA,KAAA6T,QAAAlB,EAAA3L,MACA2L,EAAAnD,OAAA,KACA8D,EAAAtT,MAEA,OAAAqN,EAAAnN,UAAAmO,OAAA/H,KAAAtG,KAAA2S,IAUAvB,EAAAlR,UAAAgN,OAAA,SAAA4Q,EAAAC,EAAAC,GAEA,IADA,IACAzE,EADAkF,EAAA,IAAA5M,EAAAT,QAAA0M,EAAAC,EAAAC,GACAlhB,EAAA,EAAAA,EAAAkD,KAAAwe,aAAA5iB,SAAAkB,EAAA,CACA,IAAA4hB,EAAA5U,EAAAiQ,SAAAR,EAAAvZ,KAAAse,EAAAxhB,IAAAb,UAAA+K,MAAAzH,QAAA,WAAA,IACAkf,EAAAC,GAAA5U,EAAA3L,QAAA,CAAA,IAAA,KAAA2L,EAAA6U,WAAAD,GAAAA,EAAA,IAAAA,EAAA5U,CAAA,iCAAAA,CAAA,CACA8U,EAAArF,EACAsF,EAAAtF,EAAAxG,oBAAAhD,KACA+O,EAAAvF,EAAAvG,qBAAAjD,OAGA,OAAA0O,iDCpKApjB,EAAAC,QAAAyW,EAEA,IAAAgN,EAAA,uBACAC,EAAA,kCACAC,EAAA,kCAEAC,EAAA,aACAC,EAAA,aACAC,EAAA,MACAC,EAAA,KACAC,EAAA,UAEAC,EAAA,CACAC,EAAA,KACAC,EAAA,KACAjjB,EAAA,KACAW,EAAA,MAUA,SAAAuiB,EAAAC,GACA,OAAAA,EAAApgB,QAAA+f,EAAA,SAAA9f,EAAAC,GACA,OAAAA,GACA,IAAA,KACA,IAAA,GACA,OAAAA,EACA,QACA,OAAA8f,EAAA9f,IAAA,MAgEA,SAAAsS,EAAAtT,EAAAmY,GAEAnY,EAAAA,EAAAC,WAEA,IAAA7C,EAAA,EACAD,EAAA6C,EAAA7C,OACA2b,EAAA,EACAqI,EAAA,KACAtG,EAAA,KACAuG,EAAA,EACAC,GAAA,EAEAC,EAAA,GAEAC,EAAA,KASA,SAAA3I,EAAA4I,GACA,OAAAhiB,MAAA,WAAAgiB,EAAA,UAAA1I,EAAA,KA0BA,SAAA9a,EAAA+F,GACA,OAAA/D,EAAAhC,OAAA+F,GAUA,SAAA0d,EAAAjjB,EAAAC,GACA0iB,EAAAnhB,EAAAhC,OAAAQ,KACA4iB,EAAAtI,EACAuI,GAAA,EAOA,IACA/hB,EADAoiB,EAAAljB,GALA2Z,EACA,EAEA,GAIA,GACA,KAAAuJ,EAAA,GACA,QAAApiB,EAAAU,EAAAhC,OAAA0jB,IAAA,CACAL,GAAA,EACA,aAEA,MAAA/hB,GAAA,OAAAA,GAIA,IAHA,IAAAqiB,EAAA3hB,EACAkZ,UAAA1a,EAAAC,GACAwI,MAAA0Z,GACAtiB,EAAA,EAAAA,EAAAsjB,EAAAxkB,SAAAkB,EACAsjB,EAAAtjB,GAAAsjB,EAAAtjB,GACAyC,QAAAqX,EAAAuI,EAAAD,EAAA,IACAmB,OACA/G,EAAA8G,EACAxiB,KAAA,MACAyiB,OAGA,SAAAC,EAAAC,GACA,IAAAC,EAAAC,EAAAF,GAGAG,EAAAjiB,EAAAkZ,UAAA4I,EAAAC,GAIA,MADA,cAAAtiB,KAAAwiB,GAIA,SAAAD,EAAAE,GAGA,IADA,IAAAH,EAAAG,EACAH,EAAA5kB,GAAA,OAAAa,EAAA+jB,IACAA,IAEA,OAAAA,EAQA,SAAA3J,IACA,GAAA,EAAAkJ,EAAAnkB,OACA,OAAAmkB,EAAAla,QACA,GAAAma,EACA,OAzFA,WACA,IAAAY,EAAA,MAAAZ,EAAAf,EAAAD,EACA4B,EAAAC,UAAAhlB,EAAA,EACA,IAAAilB,EAAAF,EAAAG,KAAAtiB,GACA,IAAAqiB,EACA,MAAAzJ,EAAA,UAIA,OAHAxb,EAAA+kB,EAAAC,UACArjB,EAAAwiB,GACAA,EAAA,KACAN,EAAAoB,EAAA,IAgFAtJ,GACA,IAAAwJ,EACAjN,EACAkN,EACAhkB,EACAikB,EACA,EAAA,CACA,GAAArlB,IAAAD,EACA,OAAA,KAEA,IADAolB,GAAA,EACA3B,EAAAnhB,KAAA+iB,EAAAxkB,EAAAZ,KAGA,GAFA,OAAAolB,KACA1J,IACA1b,IAAAD,EACA,OAAA,KAGA,GAAA,MAAAa,EAAAZ,GAAA,CACA,KAAAA,IAAAD,EACA,MAAAyb,EAAA,WAEA,GAAA,MAAA5a,EAAAZ,GACA,GAAA+a,EAeA,CAIA,GADAsK,GAAA,EACAZ,EAFArjB,EAAApB,GAEA,CACAqlB,GAAA,EACA,EAAA,CAEA,IADArlB,EAAA4kB,EAAA5kB,MACAD,EACA,MAEAC,UACAykB,EAAAzkB,SAEAA,EAAAa,KAAA0f,IAAAxgB,EAAA6kB,EAAA5kB,GAAA,GAEAqlB,GACAhB,EAAAjjB,EAAApB,GAEA0b,IACAyJ,GAAA,MAnCA,CAIA,IAFAE,EAAA,MAAAzkB,EAAAQ,EAAApB,EAAA,GAEA,OAAAY,IAAAZ,IACA,GAAAA,IAAAD,EACA,OAAA,OAGAC,EACAqlB,GACAhB,EAAAjjB,EAAApB,EAAA,KAEA0b,EACAyJ,GAAA,MAuBA,CAAA,GAAA,OAAAC,EAAAxkB,EAAAZ,IAoBA,MAAA,IAlBAoB,EAAApB,EAAA,EACAqlB,EAAAtK,GAAA,MAAAna,EAAAQ,GACA,EAAA,CAIA,GAHA,OAAAgkB,KACA1J,IAEA1b,IAAAD,EACA,MAAAyb,EAAA,WAEAtD,EAAAkN,EACAA,EAAAxkB,EAAAZ,SACA,MAAAkY,GAAA,MAAAkN,KACAplB,EACAqlB,GACAhB,EAAAjjB,EAAApB,EAAA,GAEAmlB,GAAA,UAKAA,GAIA,IAAA9jB,EAAArB,EAGA,GAFAkjB,EAAA8B,UAAA,GACA9B,EAAA7gB,KAAAzB,EAAAS,MAEA,KAAAA,EAAAtB,IAAAmjB,EAAA7gB,KAAAzB,EAAAS,OACAA,EACA,IAAAwZ,EAAAjY,EAAAkZ,UAAA9b,EAAAA,EAAAqB,GAGA,MAFA,MAAAwZ,GAAA,MAAAA,IACAsJ,EAAAtJ,GACAA,EASA,SAAAlZ,EAAAkZ,GACAqJ,EAAAviB,KAAAkZ,GAQA,SAAAI,IACA,IAAAiJ,EAAAnkB,OAAA,CACA,IAAA8a,EAAAG,IACA,GAAA,OAAAH,EACA,OAAA,KACAlZ,EAAAkZ,GAEA,OAAAqJ,EAAA,GA+CA,OAAAhhB,OAAAmQ,eAAA,CACA2H,KAAAA,EACAC,KAAAA,EACAtZ,KAAAA,EACAuZ,KAxCA,SAAAoK,EAAArU,GACA,IAAAsU,EAAAtK,IAEA,GADAsK,IAAAD,EAGA,OADAtK,KACA,EAEA,IAAA/J,EACA,MAAAuK,EAAA,UAAA+J,EAAA,OAAAD,EAAA,cACA,OAAA,GAgCAnK,KAvBA,SAAA8C,GACA,IAAAuH,EAAA,KAcA,OAbAvH,IAAAhf,GACA+kB,IAAAtI,EAAA,IAAAX,GAAA,MAAAgJ,GAAAE,KACAuB,EAAA/H,IAIAuG,EAAA/F,GACAhD,IAEA+I,IAAA/F,GAAAgG,IAAAlJ,GAAA,MAAAgJ,IACAyB,EAAA/H,IAGA+H,IASA,OAAA,CACA3X,IAAA,WAAA,OAAA6N,KAlWAxF,EAAA2N,SAAAA,yBCtCArkB,EAAAC,QAAAiT,EAGA,IAAAlB,EAAAjS,EAAA,MACAmT,EAAArO,UAAAnB,OAAAmO,OAAAG,EAAAnN,YAAAiN,YAAAoB,GAAAnB,UAAA,OAEA,IAAAvD,EAAAzO,EAAA,IACA8V,EAAA9V,EAAA,IACAkT,EAAAlT,EAAA,IACA+V,EAAA/V,EAAA,IACAgW,EAAAhW,EAAA,IACAkW,EAAAlW,EAAA,IACAqW,EAAArW,EAAA,IACAuW,EAAAvW,EAAA,IACA0O,EAAA1O,EAAA,IACA2V,EAAA3V,EAAA,IACA4V,EAAA5V,EAAA,IACA6V,EAAA7V,EAAA,IACAwO,EAAAxO,EAAA,IACAmW,EAAAnW,EAAA,IAUA,SAAAmT,EAAAvH,EAAAjG,GACAsM,EAAA/G,KAAAtG,KAAAgH,EAAAjG,GAMAf,KAAAqH,OAAA,GAMArH,KAAAiI,OAAAnN,GAMAkF,KAAAgZ,WAAAle,GAMAkF,KAAAyN,SAAA3S,GAMAkF,KAAAqM,MAAAvR,GAOAkF,KAAAshB,EAAA,KAOAthB,KAAAkM,EAAA,KAOAlM,KAAAuhB,EAAA,KAOAvhB,KAAAwhB,EAAA,KA0HA,SAAAlO,EAAA/L,GAKA,OAJAA,EAAA+Z,EAAA/Z,EAAA2E,EAAA3E,EAAAga,EAAA,YACAha,EAAAxK,cACAwK,EAAAzJ,cACAyJ,EAAAmL,OACAnL,EA5HAxI,OAAAiW,iBAAAzG,EAAArO,UAAA,CAQAuhB,WAAA,CACA/X,IAAA,WAGA,GAAA1J,KAAAshB,EACA,OAAAthB,KAAAshB,EAEAthB,KAAAshB,EAAA,GACA,IAAA,IAAA1N,EAAA7U,OAAAC,KAAAgB,KAAAqH,QAAAvK,EAAA,EAAAA,EAAA8W,EAAAhY,SAAAkB,EAAA,CACA,IAAAmN,EAAAjK,KAAAqH,OAAAuM,EAAA9W,IACA0K,EAAAyC,EAAAzC,GAGA,GAAAxH,KAAAshB,EAAA9Z,GACA,MAAAvJ,MAAA,gBAAAuJ,EAAA,OAAAxH,MAEAA,KAAAshB,EAAA9Z,GAAAyC,EAEA,OAAAjK,KAAAshB,IAUAzW,YAAA,CACAnB,IAAA,WACA,OAAA1J,KAAAkM,IAAAlM,KAAAkM,EAAApC,EAAA2J,QAAAzT,KAAAqH,WAUAqa,YAAA,CACAhY,IAAA,WACA,OAAA1J,KAAAuhB,IAAAvhB,KAAAuhB,EAAAzX,EAAA2J,QAAAzT,KAAAiI,WAUA8H,KAAA,CACArG,IAAA,WACA,OAAA1J,KAAAwhB,IAAAxhB,KAAA+P,KAAAxB,EAAAoT,oBAAA3hB,KAAAuO,KAEAkH,IAAA,SAAA1F,GAGA,IAAA7P,EAAA6P,EAAA7P,UACAA,aAAAoR,KACAvB,EAAA7P,UAAA,IAAAoR,GAAAnE,YAAA4C,EACAjG,EAAA8R,MAAA7L,EAAA7P,UAAAA,IAIA6P,EAAAsC,MAAAtC,EAAA7P,UAAAmS,MAAArS,KAGA8J,EAAA8R,MAAA7L,EAAAuB,GAAA,GAEAtR,KAAAwhB,EAAAzR,EAIA,IADA,IAAAjT,EAAA,EACAA,EAAAkD,KAAA6K,YAAAjP,SAAAkB,EACAkD,KAAAkM,EAAApP,GAAAb,UAGA,IAAA2lB,EAAA,GACA,IAAA9kB,EAAA,EAAAA,EAAAkD,KAAA0hB,YAAA9lB,SAAAkB,EACA8kB,EAAA5hB,KAAAuhB,EAAAzkB,GAAAb,UAAA+K,MAAA,CACA0C,IAAAI,EAAA0L,YAAAxV,KAAAuhB,EAAAzkB,GAAAqL,OACAsN,IAAA3L,EAAA4L,YAAA1V,KAAAuhB,EAAAzkB,GAAAqL,QAEArL,GACAiC,OAAAiW,iBAAAjF,EAAA7P,UAAA0hB,OAUArT,EAAAoT,oBAAA,SAAA/W,GAIA,IAFA,IAEAX,EAFAD,EAAAF,EAAA3L,QAAA,CAAA,KAAAyM,EAAA5D,MAEAlK,EAAA,EAAAA,EAAA8N,EAAAC,YAAAjP,SAAAkB,GACAmN,EAAAW,EAAAsB,EAAApP,IAAAiO,IAAAf,EACA,YAAAF,EAAAgB,SAAAb,EAAAjD,OACAiD,EAAAK,UAAAN,EACA,YAAAF,EAAAgB,SAAAb,EAAAjD,OACA,OAAAgD,EACA,wEADAA,CAEA,yBA6BAuE,EAAAb,SAAA,SAAA1G,EAAAC,GACA,IAAAM,EAAA,IAAAgH,EAAAvH,EAAAC,EAAAlG,SACAwG,EAAAyR,WAAA/R,EAAA+R,WACAzR,EAAAkG,SAAAxG,EAAAwG,SAGA,IAFA,IAAAmG,EAAA7U,OAAAC,KAAAiI,EAAAI,QACAvK,EAAA,EACAA,EAAA8W,EAAAhY,SAAAkB,EACAyK,EAAAwG,UACA,IAAA9G,EAAAI,OAAAuM,EAAA9W,IAAAiL,QACAoJ,EAAAzD,SACAY,EAAAZ,UAAAkG,EAAA9W,GAAAmK,EAAAI,OAAAuM,EAAA9W,MAEA,GAAAmK,EAAAgB,OACA,IAAA2L,EAAA7U,OAAAC,KAAAiI,EAAAgB,QAAAnL,EAAA,EAAAA,EAAA8W,EAAAhY,SAAAkB,EACAyK,EAAAwG,IAAAmD,EAAAxD,SAAAkG,EAAA9W,GAAAmK,EAAAgB,OAAA2L,EAAA9W,MACA,GAAAmK,EAAAC,OACA,IAAA0M,EAAA7U,OAAAC,KAAAiI,EAAAC,QAAApK,EAAA,EAAAA,EAAA8W,EAAAhY,SAAAkB,EAAA,CACA,IAAAoK,EAAAD,EAAAC,OAAA0M,EAAA9W,IACAyK,EAAAwG,KACA7G,EAAAM,KAAA1M,GACAwT,EAAAZ,SACAxG,EAAAG,SAAAvM,GACAyT,EAAAb,SACAxG,EAAAyB,SAAA7N,GACA+O,EAAA6D,SACAxG,EAAA2M,UAAA/Y,GACAsW,EAAA1D,SACAL,EAAAK,UAAAkG,EAAA9W,GAAAoK,IAWA,OARAD,EAAA+R,YAAA/R,EAAA+R,WAAApd,SACA2L,EAAAyR,WAAA/R,EAAA+R,YACA/R,EAAAwG,UAAAxG,EAAAwG,SAAA7R,SACA2L,EAAAkG,SAAAxG,EAAAwG,UACAxG,EAAAoF,QACA9E,EAAA8E,OAAA,GACApF,EAAAqG,UACA/F,EAAA+F,QAAArG,EAAAqG,SACA/F,GAQAgH,EAAArO,UAAA0N,OAAA,SAAAC,GACA,IAAA0Q,EAAAlR,EAAAnN,UAAA0N,OAAAtH,KAAAtG,KAAA6N,GACAC,IAAAD,KAAAA,EAAAC,aACA,OAAAhE,EAAAoB,SAAA,CACA,UAAAqT,GAAAA,EAAAxd,SAAAjG,GACA,SAAAuS,EAAA6F,YAAAlT,KAAA0hB,YAAA7T,GACA,SAAAR,EAAA6F,YAAAlT,KAAA6K,YAAAuB,OAAA,SAAAgH,GAAA,OAAAA,EAAApE,iBAAAnB,IAAA,GACA,aAAA7N,KAAAgZ,YAAAhZ,KAAAgZ,WAAApd,OAAAoE,KAAAgZ,WAAAle,GACA,WAAAkF,KAAAyN,UAAAzN,KAAAyN,SAAA7R,OAAAoE,KAAAyN,SAAA3S,GACA,QAAAkF,KAAAqM,OAAAvR,GACA,SAAAyjB,GAAAA,EAAArX,QAAApM,GACA,UAAAgT,EAAA9N,KAAAsN,QAAAxS,MAOAyT,EAAArO,UAAAqU,WAAA,WAEA,IADA,IAAAlN,EAAArH,KAAA6K,YAAA/N,EAAA,EACAA,EAAAuK,EAAAzL,QACAyL,EAAAvK,KAAAb,UACA,IAAAgM,EAAAjI,KAAA0hB,YACA,IADA5kB,EAAA,EACAA,EAAAmL,EAAArM,QACAqM,EAAAnL,KAAAb,UACA,OAAAoR,EAAAnN,UAAAqU,WAAAjO,KAAAtG,OAMAuO,EAAArO,UAAAwJ,IAAA,SAAA1C,GACA,OAAAhH,KAAAqH,OAAAL,IACAhH,KAAAiI,QAAAjI,KAAAiI,OAAAjB,IACAhH,KAAAkH,QAAAlH,KAAAkH,OAAAF,IACA,MAUAuH,EAAArO,UAAA6N,IAAA,SAAA4E,GAEA,GAAA3S,KAAA0J,IAAAiJ,EAAA3L,MACA,MAAA/I,MAAA,mBAAA0U,EAAA3L,KAAA,QAAAhH,MAEA,GAAA2S,aAAArE,GAAAqE,EAAAlE,SAAA3T,GAAA,CAMA,GAAAkF,KAAAshB,EAAAthB,KAAAshB,EAAA3O,EAAAnL,IAAAxH,KAAAyhB,WAAA9O,EAAAnL,IACA,MAAAvJ,MAAA,gBAAA0U,EAAAnL,GAAA,OAAAxH,MACA,GAAAA,KAAAkO,aAAAyE,EAAAnL,IACA,MAAAvJ,MAAA,MAAA0U,EAAAnL,GAAA,mBAAAxH,MACA,GAAAA,KAAAmO,eAAAwE,EAAA3L,MACA,MAAA/I,MAAA,SAAA0U,EAAA3L,KAAA,oBAAAhH,MAOA,OALA2S,EAAAnD,QACAmD,EAAAnD,OAAAnB,OAAAsE,IACA3S,KAAAqH,OAAAsL,EAAA3L,MAAA2L,GACA/D,QAAA5O,KACA2S,EAAAsB,MAAAjU,MACAsT,EAAAtT,MAEA,OAAA2S,aAAAzB,GACAlR,KAAAiI,SACAjI,KAAAiI,OAAA,KACAjI,KAAAiI,OAAA0K,EAAA3L,MAAA2L,GACAsB,MAAAjU,MACAsT,EAAAtT,OAEAqN,EAAAnN,UAAA6N,IAAAzH,KAAAtG,KAAA2S,IAUApE,EAAArO,UAAAmO,OAAA,SAAAsE,GACA,GAAAA,aAAArE,GAAAqE,EAAAlE,SAAA3T,GAAA,CAIA,IAAAkF,KAAAqH,QAAArH,KAAAqH,OAAAsL,EAAA3L,QAAA2L,EACA,MAAA1U,MAAA0U,EAAA,uBAAA3S,MAKA,cAHAA,KAAAqH,OAAAsL,EAAA3L,MACA2L,EAAAnD,OAAA,KACAmD,EAAAuB,SAAAlU,MACAsT,EAAAtT,MAEA,GAAA2S,aAAAzB,EAAA,CAGA,IAAAlR,KAAAiI,QAAAjI,KAAAiI,OAAA0K,EAAA3L,QAAA2L,EACA,MAAA1U,MAAA0U,EAAA,uBAAA3S,MAKA,cAHAA,KAAAiI,OAAA0K,EAAA3L,MACA2L,EAAAnD,OAAA,KACAmD,EAAAuB,SAAAlU,MACAsT,EAAAtT,MAEA,OAAAqN,EAAAnN,UAAAmO,OAAA/H,KAAAtG,KAAA2S,IAQApE,EAAArO,UAAAgO,aAAA,SAAA1G,GACA,OAAA6F,EAAAa,aAAAlO,KAAAyN,SAAAjG,IAQA+G,EAAArO,UAAAiO,eAAA,SAAAnH,GACA,OAAAqG,EAAAc,eAAAnO,KAAAyN,SAAAzG,IAQAuH,EAAArO,UAAAgN,OAAA,SAAAkF,GACA,OAAA,IAAApS,KAAA+P,KAAAqC,IAOA7D,EAAArO,UAAA2hB,MAAA,WAMA,IAFA,IAAArX,EAAAxK,KAAAwK,SACA8B,EAAA,GACAxP,EAAA,EAAAA,EAAAkD,KAAA6K,YAAAjP,SAAAkB,EACAwP,EAAA9O,KAAAwC,KAAAkM,EAAApP,GAAAb,UAAAoO,cAGArK,KAAAjD,OAAAgU,EAAA/Q,KAAA+Q,CAAA,CACAY,OAAAA,EACArF,MAAAA,EACAxC,KAAAA,IAEA9J,KAAAlC,OAAAkT,EAAAhR,KAAAgR,CAAA,CACAS,OAAAA,EACAnF,MAAAA,EACAxC,KAAAA,IAEA9J,KAAA0S,OAAAzB,EAAAjR,KAAAiR,CAAA,CACA3E,MAAAA,EACAxC,KAAAA,IAEA9J,KAAA2K,WAAAf,EAAAe,WAAA3K,KAAA4J,CAAA,CACA0C,MAAAA,EACAxC,KAAAA,IAEA9J,KAAAkL,SAAAtB,EAAAsB,SAAAlL,KAAA4J,CAAA,CACA0C,MAAAA,EACAxC,KAAAA,IAIA,IAAAgY,EAAAvQ,EAAA/G,GACA,GAAAsX,EAAA,CACA,IAAAC,EAAAhjB,OAAAmO,OAAAlN,MAEA+hB,EAAApX,WAAA3K,KAAA2K,WACA3K,KAAA2K,WAAAmX,EAAAnX,WAAA7G,KAAAie,GAGAA,EAAA7W,SAAAlL,KAAAkL,SACAlL,KAAAkL,SAAA4W,EAAA5W,SAAApH,KAAAie,GAIA,OAAA/hB,MASAuO,EAAArO,UAAAnD,OAAA,SAAA6R,EAAA0D,GACA,OAAAtS,KAAA6hB,QAAA9kB,OAAA6R,EAAA0D,IASA/D,EAAArO,UAAAqS,gBAAA,SAAA3D,EAAA0D,GACA,OAAAtS,KAAAjD,OAAA6R,EAAA0D,GAAAA,EAAA9L,IAAA8L,EAAA0P,OAAA1P,GAAA2P,UAWA1T,EAAArO,UAAApC,OAAA,SAAA0U,EAAA5W,GACA,OAAAoE,KAAA6hB,QAAA/jB,OAAA0U,EAAA5W,IAUA2S,EAAArO,UAAAuS,gBAAA,SAAAD,GAGA,OAFAA,aAAAf,IACAe,EAAAf,EAAAvE,OAAAsF,IACAxS,KAAAlC,OAAA0U,EAAAA,EAAA0I,WAQA3M,EAAArO,UAAAwS,OAAA,SAAA9D,GACA,OAAA5O,KAAA6hB,QAAAnP,OAAA9D,IAQAL,EAAArO,UAAAyK,WAAA,SAAAgI,GACA,OAAA3S,KAAA6hB,QAAAlX,WAAAgI,IA4BApE,EAAArO,UAAAgL,SAAA,SAAA0D,EAAA7N,GACA,OAAAf,KAAA6hB,QAAA3W,SAAA0D,EAAA7N,IAkBAwN,EAAAyB,EAAA,SAAAkS,GACA,OAAA,SAAAlK,GACAlO,EAAAsG,aAAA4H,EAAAkK,uHCpkBA,IAAA5V,EAAAhR,EAEAwO,EAAA1O,EAAA,IAEA0jB,EAAA,CACA,SACA,QACA,QACA,SACA,SACA,UACA,WACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,SAGA,SAAAqD,EAAAxZ,EAAA9M,GACA,IAAAiB,EAAA,EAAAslB,EAAA,GAEA,IADAvmB,GAAA,EACAiB,EAAA6L,EAAA/M,QAAAwmB,EAAAtD,EAAAhiB,EAAAjB,IAAA8M,EAAA7L,KACA,OAAAslB,EAuBA9V,EAAAC,MAAA4V,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,IAwBA7V,EAAAiD,SAAA4S,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,EACA,GACArY,EAAAgG,WACA,OAaAxD,EAAAZ,KAAAyW,EAAA,CACA,EACA,EACA,EACA,EACA,GACA,GAmBA7V,EAAAM,OAAAuV,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,GACA,GAoBA7V,EAAAE,OAAA2V,EAAA,CACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,EACA,gCC5LA,IAIA5T,EACA1E,EALAC,EAAAzO,EAAAC,QAAAF,EAAA,IAEA0W,EAAA1W,EAAA,IAKA0O,EAAA3L,QAAA/C,EAAA,GACA0O,EAAApJ,MAAAtF,EAAA,GACA0O,EAAAvE,KAAAnK,EAAA,GAMA0O,EAAAlJ,GAAAkJ,EAAAjJ,QAAA,MAOAiJ,EAAA2J,QAAA,SAAAd,GACA,GAAAA,EAAA,CAIA,IAHA,IAAA3T,EAAAD,OAAAC,KAAA2T,GACAQ,EAAAzX,MAAAsD,EAAApD,QACAE,EAAA,EACAA,EAAAkD,EAAApD,QACAuX,EAAArX,GAAA6W,EAAA3T,EAAAlD,MACA,OAAAqX,EAEA,MAAA,IAQArJ,EAAAoB,SAAA,SAAAiI,GAGA,IAFA,IAAAR,EAAA,GACA7W,EAAA,EACAA,EAAAqX,EAAAvX,QAAA,CACA,IAAAmR,EAAAoG,EAAArX,KACAwG,EAAA6Q,EAAArX,KACAwG,IAAAxH,KACA6X,EAAA5F,GAAAzK,GAEA,OAAAqQ,GAGA,IAAA0P,EAAA,MACAC,EAAA,KAOAxY,EAAA6U,WAAA,SAAA3X,GACA,MAAA,uTAAA9I,KAAA8I,IAQA8C,EAAAgB,SAAA,SAAAX,GACA,OAAA,YAAAjM,KAAAiM,IAAAL,EAAA6U,WAAAxU,GACA,KAAAA,EAAA5K,QAAA8iB,EAAA,QAAA9iB,QAAA+iB,EAAA,OAAA,KACA,IAAAnY,GAQAL,EAAAkQ,QAAA,SAAA2F,GACA,OAAAA,EAAAljB,OAAA,GAAA8lB,cAAA5C,EAAAhI,UAAA,IAGA,IAAA6K,EAAA,YAOA1Y,EAAAsN,UAAA,SAAAuI,GACA,OAAAA,EAAAhI,UAAA,EAAA,GACAgI,EAAAhI,UAAA,GACApY,QAAAijB,EAAA,SAAAhjB,EAAAC,GAAA,OAAAA,EAAA8iB,iBASAzY,EAAAsB,kBAAA,SAAAqX,EAAAllB,GACA,OAAAklB,EAAAjb,GAAAjK,EAAAiK,IAWAsC,EAAAsG,aAAA,SAAAL,EAAAmS,GAGA,GAAAnS,EAAAsC,MAMA,OALA6P,GAAAnS,EAAAsC,MAAArL,OAAAkb,IACApY,EAAA4Y,aAAArU,OAAA0B,EAAAsC,OACAtC,EAAAsC,MAAArL,KAAAkb,EACApY,EAAA4Y,aAAA3U,IAAAgC,EAAAsC,QAEAtC,EAAAsC,MAIA9D,IACAA,EAAAnT,EAAA,KAEA,IAAAmM,EAAA,IAAAgH,EAAA2T,GAAAnS,EAAA/I,MAKA,OAJA8C,EAAA4Y,aAAA3U,IAAAxG,GACAA,EAAAwI,KAAAA,EACAhR,OAAAmQ,eAAAa,EAAA,QAAA,CAAArQ,MAAA6H,EAAAob,YAAA,IACA5jB,OAAAmQ,eAAAa,EAAA7P,UAAA,QAAA,CAAAR,MAAA6H,EAAAob,YAAA,IACApb,GAGA,IAAAqb,EAAA,EAOA9Y,EAAAuG,aAAA,SAAAsC,GAGA,GAAAA,EAAAN,MACA,OAAAM,EAAAN,MAGAxI,IACAA,EAAAzO,EAAA,KAEA,IAAAuS,EAAA,IAAA9D,EAAA,OAAA+Y,IAAAjQ,GAGA,OAFA7I,EAAA4Y,aAAA3U,IAAAJ,GACA5O,OAAAmQ,eAAAyD,EAAA,QAAA,CAAAjT,MAAAiO,EAAAgV,YAAA,IACAhV,GASA5O,OAAAmQ,eAAApF,EAAA,eAAA,CACAJ,IAAA,WACA,OAAAoI,EAAA,YAAAA,EAAA,UAAA,IAAA1W,EAAA,yEC9KAC,EAAAC,QAAA+e,EAEA,IAAAvQ,EAAA1O,EAAA,IAUA,SAAAif,EAAApV,EAAAC,GASAlF,KAAAiF,GAAAA,IAAA,EAMAjF,KAAAkF,GAAAA,IAAA,EAQA,IAAA2d,EAAAxI,EAAAwI,KAAA,IAAAxI,EAAA,EAAA,GAEAwI,EAAA/W,SAAA,WAAA,OAAA,GACA+W,EAAAC,SAAAD,EAAA7G,SAAA,WAAA,OAAAhc,MACA6iB,EAAAjnB,OAAA,WAAA,OAAA,GAOA,IAAAmnB,EAAA1I,EAAA0I,SAAA,mBAOA1I,EAAA3K,WAAA,SAAAhQ,GACA,GAAA,IAAAA,EACA,OAAAmjB,EACA,IAAA3f,EAAAxD,EAAA,EACAwD,IACAxD,GAAAA,GACA,IAAAuF,EAAAvF,IAAA,EACAwF,GAAAxF,EAAAuF,GAAA,aAAA,EAUA,OATA/B,IACAgC,GAAAA,IAAA,EACAD,GAAAA,IAAA,EACA,aAAAA,IACAA,EAAA,EACA,aAAAC,IACAA,EAAA,KAGA,IAAAmV,EAAApV,EAAAC,IAQAmV,EAAA2I,KAAA,SAAAtjB,GACA,GAAA,iBAAAA,EACA,OAAA2a,EAAA3K,WAAAhQ,GACA,GAAAoK,EAAAkE,SAAAtO,GAAA,CAEA,IAAAoK,EAAAgF,KAGA,OAAAuL,EAAA3K,WAAAkI,SAAAlY,EAAA,KAFAA,EAAAoK,EAAAgF,KAAAmU,WAAAvjB,GAIA,OAAAA,EAAAiM,KAAAjM,EAAAkM,KAAA,IAAAyO,EAAA3a,EAAAiM,MAAA,EAAAjM,EAAAkM,OAAA,GAAAiX,GAQAxI,EAAAna,UAAA4L,SAAA,SAAAD,GACA,IAAAA,GAAA7L,KAAAkF,KAAA,GAAA,CACA,IAAAD,EAAA,GAAAjF,KAAAiF,KAAA,EACAC,GAAAlF,KAAAkF,KAAA,EAGA,OAFAD,IACAC,EAAAA,EAAA,IAAA,KACAD,EAAA,WAAAC,GAEA,OAAAlF,KAAAiF,GAAA,WAAAjF,KAAAkF,IAQAmV,EAAAna,UAAAgjB,OAAA,SAAArX,GACA,OAAA/B,EAAAgF,KACA,IAAAhF,EAAAgF,KAAA,EAAA9O,KAAAiF,GAAA,EAAAjF,KAAAkF,KAAA2G,GAEA,CAAAF,IAAA,EAAA3L,KAAAiF,GAAA2G,KAAA,EAAA5L,KAAAkF,GAAA2G,WAAAA,IAGA,IAAA7N,EAAAP,OAAAyC,UAAAlC,WAOAqc,EAAA8I,SAAA,SAAAC,GACA,OAAAA,IAAAL,EACAF,EACA,IAAAxI,GACArc,EAAAsI,KAAA8c,EAAA,GACAplB,EAAAsI,KAAA8c,EAAA,IAAA,EACAplB,EAAAsI,KAAA8c,EAAA,IAAA,GACAplB,EAAAsI,KAAA8c,EAAA,IAAA,MAAA,GAEAplB,EAAAsI,KAAA8c,EAAA,GACAplB,EAAAsI,KAAA8c,EAAA,IAAA,EACAplB,EAAAsI,KAAA8c,EAAA,IAAA,GACAplB,EAAAsI,KAAA8c,EAAA,IAAA,MAAA,IAQA/I,EAAAna,UAAAmjB,OAAA,WACA,OAAA5lB,OAAAC,aACA,IAAAsC,KAAAiF,GACAjF,KAAAiF,KAAA,EAAA,IACAjF,KAAAiF,KAAA,GAAA,IACAjF,KAAAiF,KAAA,GACA,IAAAjF,KAAAkF,GACAlF,KAAAkF,KAAA,EAAA,IACAlF,KAAAkF,KAAA,GAAA,IACAlF,KAAAkF,KAAA,KAQAmV,EAAAna,UAAA4iB,SAAA,WACA,IAAAQ,EAAAtjB,KAAAkF,IAAA,GAGA,OAFAlF,KAAAkF,KAAAlF,KAAAkF,IAAA,EAAAlF,KAAAiF,KAAA,IAAAqe,KAAA,EACAtjB,KAAAiF,IAAAjF,KAAAiF,IAAA,EAAAqe,KAAA,EACAtjB,MAOAqa,EAAAna,UAAA8b,SAAA,WACA,IAAAsH,IAAA,EAAAtjB,KAAAiF,IAGA,OAFAjF,KAAAiF,KAAAjF,KAAAiF,KAAA,EAAAjF,KAAAkF,IAAA,IAAAoe,KAAA,EACAtjB,KAAAkF,IAAAlF,KAAAkF,KAAA,EAAAoe,KAAA,EACAtjB,MAOAqa,EAAAna,UAAAtE,OAAA,WACA,IAAA2nB,EAAAvjB,KAAAiF,GACAue,GAAAxjB,KAAAiF,KAAA,GAAAjF,KAAAkF,IAAA,KAAA,EACAue,EAAAzjB,KAAAkF,KAAA,GACA,OAAA,IAAAue,EACA,IAAAD,EACAD,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,MACAA,EAAA,IAAA,EAAA,EACAA,EAAA,QAAA,EAAA,EACAC,EAAA,IAAA,EAAA,kCCrMA,IAAA3Z,EAAAxO,EA4NA,SAAAsgB,EAAA8H,EAAAC,EAAAtU,GACA,IAAA,IAAArQ,EAAAD,OAAAC,KAAA2kB,GAAA7mB,EAAA,EAAAA,EAAAkC,EAAApD,SAAAkB,EACA4mB,EAAA1kB,EAAAlC,MAAAhC,IAAAuU,IACAqU,EAAA1kB,EAAAlC,IAAA6mB,EAAA3kB,EAAAlC,KACA,OAAA4mB,EAoBA,SAAAE,EAAA5c,GAEA,SAAA6c,EAAAjV,EAAAwD,GAEA,KAAApS,gBAAA6jB,GACA,OAAA,IAAAA,EAAAjV,EAAAwD,GAKArT,OAAAmQ,eAAAlP,KAAA,UAAA,CAAA0J,IAAA,WAAA,OAAAkF,KAGA3Q,MAAA6lB,kBACA7lB,MAAA6lB,kBAAA9jB,KAAA6jB,GAEA9kB,OAAAmQ,eAAAlP,KAAA,QAAA,CAAAN,MAAAzB,QAAA8hB,OAAA,KAEA3N,GACAwJ,EAAA5b,KAAAoS,GAWA,OARAyR,EAAA3jB,UAAAnB,OAAAmO,OAAAjP,MAAAiC,YAAAiN,YAAA0W,EAEA9kB,OAAAmQ,eAAA2U,EAAA3jB,UAAA,OAAA,CAAAwJ,IAAA,WAAA,OAAA1C,KAEA6c,EAAA3jB,UAAAxB,SAAA,WACA,OAAAsB,KAAAgH,KAAA,KAAAhH,KAAA4O,SAGAiV,EA/QA/Z,EAAAnJ,UAAAvF,EAAA,GAGA0O,EAAAzN,OAAAjB,EAAA,GAGA0O,EAAA/J,aAAA3E,EAAA,GAGA0O,EAAA0R,MAAApgB,EAAA,GAGA0O,EAAAjJ,QAAAzF,EAAA,GAGA0O,EAAAvD,KAAAnL,EAAA,IAGA0O,EAAAia,KAAA3oB,EAAA,GAGA0O,EAAAuQ,SAAAjf,EAAA,IAGA0O,EAAAka,OAAA,oBAAAC,QAAAA,QACA,oBAAAD,QAAAA,QACA,oBAAAvH,MAAAA,MACAzc,KAQA8J,EAAAgG,WAAA/Q,OAAA4Q,OAAA5Q,OAAA4Q,OAAA,IAAA,GAOA7F,EAAA+F,YAAA9Q,OAAA4Q,OAAA5Q,OAAA4Q,OAAA,IAAA,GAQA7F,EAAAyT,UAAAzT,EAAAka,OAAA/G,SAAAnT,EAAAka,OAAA/G,QAAAiH,UAAApa,EAAAka,OAAA/G,QAAAiH,SAAAC,MAQAra,EAAAmE,UAAAmW,OAAAnW,WAAA,SAAAvO,GACA,MAAA,iBAAAA,GAAA2kB,SAAA3kB,IAAAhD,KAAAiD,MAAAD,KAAAA,GAQAoK,EAAAkE,SAAA,SAAAtO,GACA,MAAA,iBAAAA,GAAAA,aAAAjC,QAQAqM,EAAA4E,SAAA,SAAAhP,GACA,OAAAA,GAAA,iBAAAA,GAWAoK,EAAAwa,MAQAxa,EAAAya,MAAA,SAAAnR,EAAAjJ,GACA,IAAAzK,EAAA0T,EAAAjJ,GACA,QAAA,MAAAzK,IAAA0T,EAAAoR,eAAAra,MACA,iBAAAzK,GAAA,GAAAhE,MAAA0Y,QAAA1U,GAAAA,EAAA9D,OAAAmD,OAAAC,KAAAU,GAAA9D,UAeAkO,EAAAgR,OAAA,WACA,IACA,IAAAA,EAAAhR,EAAAjJ,QAAA,UAAAia,OAEA,OAAAA,EAAA5a,UAAAukB,UAAA3J,EAAA,KACA,MAAAxV,GAEA,OAAA,MAPA,GAYAwE,EAAA4a,EAAA,KAGA5a,EAAA6a,EAAA,KAOA7a,EAAA8F,UAAA,SAAAgV,GAEA,MAAA,iBAAAA,EACA9a,EAAAgR,OACAhR,EAAA6a,EAAAC,GACA,IAAA9a,EAAApO,MAAAkpB,GACA9a,EAAAgR,OACAhR,EAAA4a,EAAAE,GACA,oBAAAjjB,WACAijB,EACA,IAAAjjB,WAAAijB,IAOA9a,EAAApO,MAAA,oBAAAiG,WAAAA,WAAAjG,MAOAoO,EAAAgF,KAAA,oBAAAmO,SAAAA,QAAA4H,IAAAC,YAAAhb,EAAAka,OAAAe,SAAAjb,EAAAka,OAAAe,QAAAjW,MACAhF,EAAAka,OAAAlV,MACAhF,EAAAjJ,QAAA,QAAA/F,GAOAgP,EAAAkb,OAAA,mBAOAlb,EAAAmb,QAAA,wBAOAnb,EAAAob,QAAA,6CAOApb,EAAAqb,WAAA,SAAAzlB,GACA,OAAAA,EACAoK,EAAAuQ,SAAA2I,KAAAtjB,GAAA2jB,SACAvZ,EAAAuQ,SAAA0I,UASAjZ,EAAAsb,aAAA,SAAAhC,EAAAvX,GACA,IAAA8O,EAAA7Q,EAAAuQ,SAAA8I,SAAAC,GACA,OAAAtZ,EAAAgF,KACAhF,EAAAgF,KAAAuW,SAAA1K,EAAA1V,GAAA0V,EAAAzV,GAAA2G,GACA8O,EAAA7O,WAAAD,IAkBA/B,EAAA8R,MAAAA,EAOA9R,EAAAiQ,QAAA,SAAA4F,GACA,OAAAA,EAAAljB,OAAA,GAAAkS,cAAAgR,EAAAhI,UAAA,IA0CA7N,EAAA8Z,SAAAA,EAmBA9Z,EAAAwb,cAAA1B,EAAA,iBAoBA9Z,EAAA0L,YAAA,SAAAH,GAEA,IADA,IAAAkQ,EAAA,GACAzoB,EAAA,EAAAA,EAAAuY,EAAAzZ,SAAAkB,EACAyoB,EAAAlQ,EAAAvY,IAAA,EAOA,OAAA,WACA,IAAA,IAAAkC,EAAAD,OAAAC,KAAAgB,MAAAlD,EAAAkC,EAAApD,OAAA,GAAA,EAAAkB,IAAAA,EACA,GAAA,IAAAyoB,EAAAvmB,EAAAlC,KAAAkD,KAAAhB,EAAAlC,MAAAhC,IAAA,OAAAkF,KAAAhB,EAAAlC,IACA,OAAAkC,EAAAlC,KAiBAgN,EAAA4L,YAAA,SAAAL,GAQA,OAAA,SAAArO,GACA,IAAA,IAAAlK,EAAA,EAAAA,EAAAuY,EAAAzZ,SAAAkB,EACAuY,EAAAvY,KAAAkK,UACAhH,KAAAqV,EAAAvY,MAoBAgN,EAAA+D,cAAA,CACA2X,MAAA/nB,OACAgoB,MAAAhoB,OACAsO,MAAAtO,OACAwJ,MAAA,GAIA6C,EAAA0G,EAAA,WACA,IAAAsK,EAAAhR,EAAAgR,OAEAA,GAMAhR,EAAA4a,EAAA5J,EAAAkI,OAAArhB,WAAAqhB,MAAAlI,EAAAkI,MAEA,SAAAtjB,EAAAgmB,GACA,OAAA,IAAA5K,EAAApb,EAAAgmB,IAEA5b,EAAA6a,EAAA7J,EAAA6K,aAEA,SAAAzf,GACA,OAAA,IAAA4U,EAAA5U,KAbA4D,EAAA4a,EAAA5a,EAAA6a,EAAA,gECrYAtpB,EAAAC,QAwHA,SAAAsP,GAGA,IAAAZ,EAAAF,EAAA3L,QAAA,CAAA,KAAAyM,EAAA5D,KAAA,UAAA8C,CACA,oCADAA,CAEA,WAAA,mBACA7B,EAAA2C,EAAA8W,YACAkE,EAAA,GACA3d,EAAArM,QAAAoO,EACA,YAEA,IAAA,IAAAlN,EAAA,EAAAA,EAAA8N,EAAAC,YAAAjP,SAAAkB,EAAA,CACA,IAAAmN,EAAAW,EAAAsB,EAAApP,GAAAb,UACAmO,EAAA,IAAAN,EAAAgB,SAAAb,EAAAjD,MAMA,GAJAiD,EAAA6C,UAAA9C,EACA,sCAAAI,EAAAH,EAAAjD,MAGAiD,EAAAc,IAAAf,EACA,yBAAAI,EADAJ,CAEA,WAAA6b,EAAA5b,EAAA,UAFAD,CAGA,wBAAAI,EAHAJ,CAIA,gCACA8b,EAAA9b,EAAAC,EAAA,QACA8b,EAAA/b,EAAAC,EAAAnN,EAAAsN,EAAA,SAAA2b,CACA,UAGA,GAAA9b,EAAAK,SAAA,CACA,IAAAU,EAAAZ,EACAH,EAAAgB,eACAD,EAAA,QAAAf,EAAAzC,GACAwC,EAAA,SAAAgB,GACAhB,EAAA,mEACAI,EAAAA,EAAAY,EAAAZ,EAAAY,EAAAZ,IAEAJ,EACA,yBAAAgB,EADAhB,CAEA,WAAA6b,EAAA5b,EAAA,SAFAD,CAGA,gCAAAgB,GACAf,EAAA+C,cACAhD,EAAA,qCAAAgB,EAAA,OAEA+a,EAAA/b,EAAAC,EAAAnN,EAAAkO,EAAA,OACAf,EAAA+C,cACAhD,EAAA,KAEAA,EAAA,SAGA,CACA,GAAAC,EAAAuB,OAAA,CACA,IAAAwa,EAAAlc,EAAAgB,SAAAb,EAAAuB,OAAAxE,MACA,IAAA4e,EAAA3b,EAAAuB,OAAAxE,OAAAgD,EACA,cAAAgc,EADAhc,CAEA,WAAAC,EAAAuB,OAAAxE,KAAA,qBACA4e,EAAA3b,EAAAuB,OAAAxE,MAAA,EACAgD,EACA,QAAAgc,GAEAD,EAAA/b,EAAAC,EAAAnN,EAAAsN,GAEAH,EAAA6C,UAAA9C,EACA,KAEA,OAAAA,EACA,gBAzLA,IAAAH,EAAAzO,EAAA,IACA0O,EAAA1O,EAAA,IAEA,SAAAyqB,EAAA5b,EAAAkX,GACA,OAAAlX,EAAAjD,KAAA,KAAAma,GAAAlX,EAAAK,UAAA,UAAA6W,EAAA,KAAAlX,EAAAc,KAAA,WAAAoW,EAAA,MAAAlX,EAAAlC,QAAA,IAAA,IAAA,YAYA,SAAAge,EAAA/b,EAAAC,EAAAC,EAAAE,GAEA,GAAAH,EAAAI,aACA,GAAAJ,EAAAI,wBAAAR,EAAA,CAAAG,EACA,cAAAI,EADAJ,CAEA,WAFAA,CAGA,WAAA6b,EAAA5b,EAAA,eACA,IAAA,IAAAjL,EAAAD,OAAAC,KAAAiL,EAAAI,aAAA1B,QAAArL,EAAA,EAAAA,EAAA0B,EAAApD,SAAA0B,EAAA0M,EACA,WAAAC,EAAAI,aAAA1B,OAAA3J,EAAA1B,KACA0M,EACA,QADAA,CAEA,UAEAA,EACA,IADAA,CAEA,8BAAAE,EAAAE,EAFAJ,CAGA,QAHAA,CAIA,aAAAC,EAAAjD,KAAA,IAJAgD,CAKA,UAGA,OAAAC,EAAA1C,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAyC,EACA,0BAAAI,EADAJ,CAEA,WAAA6b,EAAA5b,EAAA,YACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,kFAAAI,EAAAA,EAAAA,EAAAA,EADAJ,CAEA,WAAA6b,EAAA5b,EAAA,iBACA,MACA,IAAA,QACA,IAAA,SAAAD,EACA,2BAAAI,EADAJ,CAEA,WAAA6b,EAAA5b,EAAA,WACA,MACA,IAAA,OAAAD,EACA,4BAAAI,EADAJ,CAEA,WAAA6b,EAAA5b,EAAA,YACA,MACA,IAAA,SAAAD,EACA,yBAAAI,EADAJ,CAEA,WAAA6b,EAAA5b,EAAA,WACA,MACA,IAAA,QAAAD,EACA,4DAAAI,EAAAA,EAAAA,EADAJ,CAEA,WAAA6b,EAAA5b,EAAA,WAIA,OAAAD,EAYA,SAAA8b,EAAA9b,EAAAC,EAAAG,GAEA,OAAAH,EAAAlC,SACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAiC,EACA,6BAAAI,EADAJ,CAEA,WAAA6b,EAAA5b,EAAA,gBACA,MACA,IAAA,QACA,IAAA,SACA,IAAA,SACA,IAAA,UACA,IAAA,WAAAD,EACA,6BAAAI,EADAJ,CAEA,WAAA6b,EAAA5b,EAAA,qBACA,MACA,IAAA,OAAAD,EACA,4BAAAI,EADAJ,CAEA,WAAA6b,EAAA5b,EAAA,gBAGA,OAAAD,uCCzGA,IAAAuH,EAAAjW,EAEAgW,EAAAlW,EAAA,IA6BAmW,EAAA,wBAAA,CAEA5G,WAAA,SAAAgI,GAGA,GAAAA,GAAAA,EAAA,SAAA,CACA,IAAApL,EAAAvH,KAAAwU,OAAA7B,EAAA,UAEA,GAAApL,EAAA,CAEA,IAAAD,EAAA,MAAAqL,EAAA,SAAAlW,OAAA,GACAkW,EAAA,SAAAsT,OAAA,GAAAtT,EAAA,SAEA,OAAA3S,KAAAkN,OAAA,CACA5F,SAAA,IAAAA,EACA5H,MAAA6H,EAAAxK,OAAAwK,EAAAoD,WAAAgI,IAAAgK,YAKA,OAAA3c,KAAA2K,WAAAgI,IAGAzH,SAAA,SAAA0D,EAAA7N,GAGA,GAAAA,GAAAA,EAAAkG,MAAA2H,EAAAtH,UAAAsH,EAAAlP,MAAA,CAEA,IAAAsH,EAAA4H,EAAAtH,SAAAqQ,UAAA/I,EAAAtH,SAAAyV,YAAA,KAAA,GACAxV,EAAAvH,KAAAwU,OAAAxN,GAEAO,IACAqH,EAAArH,EAAAzJ,OAAA8Q,EAAAlP,QAIA,KAAAkP,aAAA5O,KAAA+P,OAAAnB,aAAA0C,EAAA,CACA,IAAAqB,EAAA/D,EAAAyD,MAAAnH,SAAA0D,EAAA7N,GAEA,OADA4R,EAAA,SAAA/D,EAAAyD,MAAA7H,SACAmI,EAGA,OAAA3S,KAAAkL,SAAA0D,EAAA7N,iCC/EA1F,EAAAC,QAAAqW,EAEA,IAEAC,EAFA9H,EAAA1O,EAAA,IAIAif,EAAAvQ,EAAAuQ,SACAhe,EAAAyN,EAAAzN,OACAkK,EAAAuD,EAAAvD,KAWA,SAAA2f,EAAA3qB,EAAAiL,EAAAlE,GAMAtC,KAAAzE,GAAAA,EAMAyE,KAAAwG,IAAAA,EAMAxG,KAAA6W,KAAA/b,GAMAkF,KAAAsC,IAAAA,EAIA,SAAA6jB,KAUA,SAAAC,EAAA9T,GAMAtS,KAAAiX,KAAA3E,EAAA2E,KAMAjX,KAAAqmB,KAAA/T,EAAA+T,KAMArmB,KAAAwG,IAAA8L,EAAA9L,IAMAxG,KAAA6W,KAAAvE,EAAAgU,OAQA,SAAA3U,IAMA3R,KAAAwG,IAAA,EAMAxG,KAAAiX,KAAA,IAAAiP,EAAAC,EAAA,EAAA,GAMAnmB,KAAAqmB,KAAArmB,KAAAiX,KAMAjX,KAAAsmB,OAAA,KAqDA,SAAAC,EAAAjkB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EAoBA,SAAAkkB,EAAAhgB,EAAAlE,GACAtC,KAAAwG,IAAAA,EACAxG,KAAA6W,KAAA/b,GACAkF,KAAAsC,IAAAA,EA8CA,SAAAmkB,EAAAnkB,EAAAC,EAAAC,GACA,KAAAF,EAAA4C,IACA3C,EAAAC,KAAA,IAAAF,EAAA2C,GAAA,IACA3C,EAAA2C,IAAA3C,EAAA2C,KAAA,EAAA3C,EAAA4C,IAAA,MAAA,EACA5C,EAAA4C,MAAA,EAEA,KAAA,IAAA5C,EAAA2C,IACA1C,EAAAC,KAAA,IAAAF,EAAA2C,GAAA,IACA3C,EAAA2C,GAAA3C,EAAA2C,KAAA,EAEA1C,EAAAC,KAAAF,EAAA2C,GA2CA,SAAAyhB,EAAApkB,EAAAC,EAAAC,GACAD,EAAAC,GAAA,IAAAF,EACAC,EAAAC,EAAA,GAAAF,IAAA,EAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAAA,IACAC,EAAAC,EAAA,GAAAF,IAAA,GAtKAqP,EAAAzE,OAAApD,EAAAgR,OACA,WACA,OAAAnJ,EAAAzE,OAAA,WACA,OAAA,IAAA0E,OAIA,WACA,OAAA,IAAAD,GAQAA,EAAA1L,MAAA,SAAAC,GACA,OAAA,IAAA4D,EAAApO,MAAAwK,IAKA4D,EAAApO,QAAAA,QACAiW,EAAA1L,MAAA6D,EAAAia,KAAApS,EAAA1L,MAAA6D,EAAApO,MAAAwE,UAAA+a,WAUAtJ,EAAAzR,UAAAymB,EAAA,SAAAprB,EAAAiL,EAAAlE,GAGA,OAFAtC,KAAAqmB,KAAArmB,KAAAqmB,KAAAxP,KAAA,IAAAqP,EAAA3qB,EAAAiL,EAAAlE,GACAtC,KAAAwG,KAAAA,EACAxG,OA8BAwmB,EAAAtmB,UAAAnB,OAAAmO,OAAAgZ,EAAAhmB,YACA3E,GAxBA,SAAA+G,EAAAC,EAAAC,GACA,KAAA,IAAAF,GACAC,EAAAC,KAAA,IAAAF,EAAA,IACAA,KAAA,EAEAC,EAAAC,GAAAF,GA0BAqP,EAAAzR,UAAAgb,OAAA,SAAAxb,GAWA,OARAM,KAAAwG,MAAAxG,KAAAqmB,KAAArmB,KAAAqmB,KAAAxP,KAAA,IAAA2P,GACA9mB,KAAA,GACA,IAAA,EACAA,EAAA,MAAA,EACAA,EAAA,QAAA,EACAA,EAAA,UAAA,EACA,EACAA,IAAA8G,IACAxG,MASA2R,EAAAzR,UAAAib,MAAA,SAAAzb,GACA,OAAAA,EAAA,EACAM,KAAA2mB,EAAAF,EAAA,GAAApM,EAAA3K,WAAAhQ,IACAM,KAAAkb,OAAAxb,IAQAiS,EAAAzR,UAAAkb,OAAA,SAAA1b,GACA,OAAAM,KAAAkb,QAAAxb,GAAA,EAAAA,GAAA,MAAA,IAkCAiS,EAAAzR,UAAA2b,MAZAlK,EAAAzR,UAAA4b,OAAA,SAAApc,GACA,IAAAib,EAAAN,EAAA2I,KAAAtjB,GACA,OAAAM,KAAA2mB,EAAAF,EAAA9L,EAAA/e,SAAA+e,IAkBAhJ,EAAAzR,UAAA6b,OAAA,SAAArc,GACA,IAAAib,EAAAN,EAAA2I,KAAAtjB,GAAAojB,WACA,OAAA9iB,KAAA2mB,EAAAF,EAAA9L,EAAA/e,SAAA+e,IAQAhJ,EAAAzR,UAAAmb,KAAA,SAAA3b,GACA,OAAAM,KAAA2mB,EAAAJ,EAAA,EAAA7mB,EAAA,EAAA,IAyBAiS,EAAAzR,UAAAqb,SAVA5J,EAAAzR,UAAAob,QAAA,SAAA5b,GACA,OAAAM,KAAA2mB,EAAAD,EAAA,EAAAhnB,IAAA,IA6BAiS,EAAAzR,UAAAgc,SAZAvK,EAAAzR,UAAA+b,QAAA,SAAAvc,GACA,IAAAib,EAAAN,EAAA2I,KAAAtjB,GACA,OAAAM,KAAA2mB,EAAAD,EAAA,EAAA/L,EAAA1V,IAAA0hB,EAAAD,EAAA,EAAA/L,EAAAzV,KAkBAyM,EAAAzR,UAAAsb,MAAA,SAAA9b,GACA,OAAAM,KAAA2mB,EAAA7c,EAAA0R,MAAA5Y,aAAA,EAAAlD,IASAiS,EAAAzR,UAAAub,OAAA,SAAA/b,GACA,OAAAM,KAAA2mB,EAAA7c,EAAA0R,MAAA/W,cAAA,EAAA/E,IAGA,IAAAknB,EAAA9c,EAAApO,MAAAwE,UAAAuV,IACA,SAAAnT,EAAAC,EAAAC,GACAD,EAAAkT,IAAAnT,EAAAE,IAGA,SAAAF,EAAAC,EAAAC,GACA,IAAA,IAAA1F,EAAA,EAAAA,EAAAwF,EAAA1G,SAAAkB,EACAyF,EAAAC,EAAA1F,GAAAwF,EAAAxF,IAQA6U,EAAAzR,UAAA6L,MAAA,SAAArM,GACA,IAAA8G,EAAA9G,EAAA9D,SAAA,EACA,IAAA4K,EACA,OAAAxG,KAAA2mB,EAAAJ,EAAA,EAAA,GACA,GAAAzc,EAAAkE,SAAAtO,GAAA,CACA,IAAA6C,EAAAoP,EAAA1L,MAAAO,EAAAnK,EAAAT,OAAA8D,IACArD,EAAAyB,OAAA4B,EAAA6C,EAAA,GACA7C,EAAA6C,EAEA,OAAAvC,KAAAkb,OAAA1U,GAAAmgB,EAAAC,EAAApgB,EAAA9G,IAQAiS,EAAAzR,UAAA5D,OAAA,SAAAoD,GACA,IAAA8G,EAAAD,EAAA3K,OAAA8D,GACA,OAAA8G,EACAxG,KAAAkb,OAAA1U,GAAAmgB,EAAApgB,EAAAG,MAAAF,EAAA9G,GACAM,KAAA2mB,EAAAJ,EAAA,EAAA,IAQA5U,EAAAzR,UAAA8hB,KAAA,WAIA,OAHAhiB,KAAAsmB,OAAA,IAAAF,EAAApmB,MACAA,KAAAiX,KAAAjX,KAAAqmB,KAAA,IAAAH,EAAAC,EAAA,EAAA,GACAnmB,KAAAwG,IAAA,EACAxG,MAOA2R,EAAAzR,UAAA2mB,MAAA,WAUA,OATA7mB,KAAAsmB,QACAtmB,KAAAiX,KAAAjX,KAAAsmB,OAAArP,KACAjX,KAAAqmB,KAAArmB,KAAAsmB,OAAAD,KACArmB,KAAAwG,IAAAxG,KAAAsmB,OAAA9f,IACAxG,KAAAsmB,OAAAtmB,KAAAsmB,OAAAzP,OAEA7W,KAAAiX,KAAAjX,KAAAqmB,KAAA,IAAAH,EAAAC,EAAA,EAAA,GACAnmB,KAAAwG,IAAA,GAEAxG,MAOA2R,EAAAzR,UAAA+hB,OAAA,WACA,IAAAhL,EAAAjX,KAAAiX,KACAoP,EAAArmB,KAAAqmB,KACA7f,EAAAxG,KAAAwG,IAOA,OANAxG,KAAA6mB,QAAA3L,OAAA1U,GACAA,IACAxG,KAAAqmB,KAAAxP,KAAAI,EAAAJ,KACA7W,KAAAqmB,KAAAA,EACArmB,KAAAwG,KAAAA,GAEAxG,MAOA2R,EAAAzR,UAAAyc,OAAA,WAIA,IAHA,IAAA1F,EAAAjX,KAAAiX,KAAAJ,KACAtU,EAAAvC,KAAAmN,YAAAlH,MAAAjG,KAAAwG,KACAhE,EAAA,EACAyU,GACAA,EAAA1b,GAAA0b,EAAA3U,IAAAC,EAAAC,GACAA,GAAAyU,EAAAzQ,IACAyQ,EAAAA,EAAAJ,KAGA,OAAAtU,GAGAoP,EAAAnB,EAAA,SAAAsW,GACAlV,EAAAkV,+BCxcAzrB,EAAAC,QAAAsW,EAGA,IAAAD,EAAAvW,EAAA,KACAwW,EAAA1R,UAAAnB,OAAAmO,OAAAyE,EAAAzR,YAAAiN,YAAAyE,EAEA,IAAA9H,EAAA1O,EAAA,IAEA0f,EAAAhR,EAAAgR,OAQA,SAAAlJ,IACAD,EAAArL,KAAAtG,MAQA4R,EAAA3L,MAAA,SAAAC,GACA,OAAA0L,EAAA3L,MAAA6D,EAAA6a,GAAAze,IAGA,IAAA6gB,EAAAjM,GAAAA,EAAA5a,qBAAAyB,YAAA,QAAAmZ,EAAA5a,UAAAuV,IAAAzO,KACA,SAAA1E,EAAAC,EAAAC,GACAD,EAAAkT,IAAAnT,EAAAE,IAIA,SAAAF,EAAAC,EAAAC,GACA,GAAAF,EAAA0kB,KACA1kB,EAAA0kB,KAAAzkB,EAAAC,EAAA,EAAAF,EAAA1G,aACA,IAAA,IAAAkB,EAAA,EAAAA,EAAAwF,EAAA1G,QACA2G,EAAAC,KAAAF,EAAAxF,MAgBA,SAAAmqB,EAAA3kB,EAAAC,EAAAC,GACAF,EAAA1G,OAAA,GACAkO,EAAAvD,KAAAG,MAAApE,EAAAC,EAAAC,GAEAD,EAAAkiB,UAAAniB,EAAAE,GAdAoP,EAAA1R,UAAA6L,MAAA,SAAArM,GACAoK,EAAAkE,SAAAtO,KACAA,EAAAoK,EAAA4a,EAAAhlB,EAAA,WACA,IAAA8G,EAAA9G,EAAA9D,SAAA,EAIA,OAHAoE,KAAAkb,OAAA1U,GACAA,GACAxG,KAAA2mB,EAAAI,EAAAvgB,EAAA9G,GACAM,MAaA4R,EAAA1R,UAAA5D,OAAA,SAAAoD,GACA,IAAA8G,EAAAsU,EAAAoM,WAAAxnB,GAIA,OAHAM,KAAAkb,OAAA1U,GACAA,GACAxG,KAAA2mB,EAAAM,EAAAzgB,EAAA9G,GACAM,uB3CvEAhF,KAAAC,OAcAC,EAPA,SAAAisB,EAAAngB,GACA,IAAAogB,EAAApsB,EAAAgM,GAGA,OAFAogB,GACArsB,EAAAiM,GAAA,GAAAV,KAAA8gB,EAAApsB,EAAAgM,GAAA,CAAA1L,QAAA,IAAA6rB,EAAAC,EAAAA,EAAA9rB,SACA8rB,EAAA9rB,QAGA6rB,CAAAlsB,EAAA,IAGAC,EAAA4O,KAAAka,OAAA9oB,SAAAA,EAGA,mBAAAiZ,QAAAA,OAAAkT,KACAlT,OAAA,CAAA,QAAA,SAAArF,GAKA,OAJAA,GAAAA,EAAAwY,SACApsB,EAAA4O,KAAAgF,KAAAA,EACA5T,EAAAsW,aAEAtW,IAIA,iBAAAG,QAAAA,QAAAA,OAAAC,UACAD,OAAAC,QAAAJ,GA/BA", "file": "protobuf.min.js", "sourcesContent": ["(function prelude(modules, cache, entries) {\n\n    // This is the prelude used to bundle protobuf.js for the browser. Wraps up the CommonJS\n    // sources through a conflict-free require shim and is again wrapped within an iife that\n    // provides a minification-friendly `undefined` var plus a global \"use strict\" directive\n    // so that minification can remove the directives of each module.\n\n    function $require(name) {\n        var $module = cache[name];\n        if (!$module)\n            modules[name][0].call($module = cache[name] = { exports: {} }, $require, $module, $module.exports);\n        return $module.exports;\n    }\n\n    var protobuf = $require(entries[0]);\n\n    // Expose globally\n    protobuf.util.global.protobuf = protobuf;\n\n    // Be nice to AMD\n    if (typeof define === \"function\" && define.amd)\n        define([\"long\"], function(Long) {\n            if (Long && Long.isLong) {\n                protobuf.util.Long = Long;\n                protobuf.configure();\n            }\n            return protobuf;\n        });\n\n    // Be nice to CommonJS\n    if (typeof module === \"object\" && module && module.exports)\n        module.exports = protobuf;\n\n})/* end of prelude */", "\"use strict\";\r\nmodule.exports = asPromise;\r\n\r\n/**\r\n * Callback as used by {@link util.asPromise}.\r\n * @typedef asPromiseCallback\r\n * @type {function}\r\n * @param {Error|null} error Error, if any\r\n * @param {...*} params Additional arguments\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Returns a promise from a node-style callback function.\r\n * @memberof util\r\n * @param {asPromiseCallback} fn Function to call\r\n * @param {*} ctx Function context\r\n * @param {...*} params Function arguments\r\n * @returns {Promise<*>} Promisified function\r\n */\r\nfunction asPromise(fn, ctx/*, varargs */) {\r\n    var params  = new Array(arguments.length - 1),\r\n        offset  = 0,\r\n        index   = 2,\r\n        pending = true;\r\n    while (index < arguments.length)\r\n        params[offset++] = arguments[index++];\r\n    return new Promise(function executor(resolve, reject) {\r\n        params[offset] = function callback(err/*, varargs */) {\r\n            if (pending) {\r\n                pending = false;\r\n                if (err)\r\n                    reject(err);\r\n                else {\r\n                    var params = new Array(arguments.length - 1),\r\n                        offset = 0;\r\n                    while (offset < params.length)\r\n                        params[offset++] = arguments[offset];\r\n                    resolve.apply(null, params);\r\n                }\r\n            }\r\n        };\r\n        try {\r\n            fn.apply(ctx || null, params);\r\n        } catch (err) {\r\n            if (pending) {\r\n                pending = false;\r\n                reject(err);\r\n            }\r\n        }\r\n    });\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal base64 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar base64 = exports;\r\n\r\n/**\r\n * Calculates the byte length of a base64 encoded string.\r\n * @param {string} string Base64 encoded string\r\n * @returns {number} Byte length\r\n */\r\nbase64.length = function length(string) {\r\n    var p = string.length;\r\n    if (!p)\r\n        return 0;\r\n    var n = 0;\r\n    while (--p % 4 > 1 && string.charAt(p) === \"=\")\r\n        ++n;\r\n    return Math.ceil(string.length * 3) / 4 - n;\r\n};\r\n\r\n// Base64 encoding table\r\nvar b64 = new Array(64);\r\n\r\n// Base64 decoding table\r\nvar s64 = new Array(123);\r\n\r\n// 65..90, 97..122, 48..57, 43, 47\r\nfor (var i = 0; i < 64;)\r\n    s64[b64[i] = i < 26 ? i + 65 : i < 52 ? i + 71 : i < 62 ? i - 4 : i - 59 | 43] = i++;\r\n\r\n/**\r\n * Encodes a buffer to a base64 encoded string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} Base64 encoded string\r\n */\r\nbase64.encode = function encode(buffer, start, end) {\r\n    var parts = null,\r\n        chunk = [];\r\n    var i = 0, // output index\r\n        j = 0, // goto index\r\n        t;     // temporary\r\n    while (start < end) {\r\n        var b = buffer[start++];\r\n        switch (j) {\r\n            case 0:\r\n                chunk[i++] = b64[b >> 2];\r\n                t = (b & 3) << 4;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                chunk[i++] = b64[t | b >> 4];\r\n                t = (b & 15) << 2;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                chunk[i++] = b64[t | b >> 6];\r\n                chunk[i++] = b64[b & 63];\r\n                j = 0;\r\n                break;\r\n        }\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (j) {\r\n        chunk[i++] = b64[t];\r\n        chunk[i++] = 61;\r\n        if (j === 1)\r\n            chunk[i++] = 61;\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\nvar invalidEncoding = \"invalid encoding\";\r\n\r\n/**\r\n * Decodes a base64 encoded string to a buffer.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Number of bytes written\r\n * @throws {Error} If encoding is invalid\r\n */\r\nbase64.decode = function decode(string, buffer, offset) {\r\n    var start = offset;\r\n    var j = 0, // goto index\r\n        t;     // temporary\r\n    for (var i = 0; i < string.length;) {\r\n        var c = string.charCodeAt(i++);\r\n        if (c === 61 && j > 1)\r\n            break;\r\n        if ((c = s64[c]) === undefined)\r\n            throw Error(invalidEncoding);\r\n        switch (j) {\r\n            case 0:\r\n                t = c;\r\n                j = 1;\r\n                break;\r\n            case 1:\r\n                buffer[offset++] = t << 2 | (c & 48) >> 4;\r\n                t = c;\r\n                j = 2;\r\n                break;\r\n            case 2:\r\n                buffer[offset++] = (t & 15) << 4 | (c & 60) >> 2;\r\n                t = c;\r\n                j = 3;\r\n                break;\r\n            case 3:\r\n                buffer[offset++] = (t & 3) << 6 | c;\r\n                j = 0;\r\n                break;\r\n        }\r\n    }\r\n    if (j === 1)\r\n        throw Error(invalidEncoding);\r\n    return offset - start;\r\n};\r\n\r\n/**\r\n * Tests if the specified string appears to be base64 encoded.\r\n * @param {string} string String to test\r\n * @returns {boolean} `true` if probably base64 encoded, otherwise false\r\n */\r\nbase64.test = function test(string) {\r\n    return /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(string);\r\n};\r\n", "\"use strict\";\r\nmodule.exports = codegen;\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @param {string[]} functionParams Function parameter names\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n */\r\nfunction codegen(functionParams, functionName) {\r\n\r\n    /* istanbul ignore if */\r\n    if (typeof functionParams === \"string\") {\r\n        functionName = functionParams;\r\n        functionParams = undefined;\r\n    }\r\n\r\n    var body = [];\r\n\r\n    /**\r\n     * Appends code to the function's body or finishes generation.\r\n     * @typedef Codegen\r\n     * @type {function}\r\n     * @param {string|Object.<string,*>} [formatStringOrScope] Format string or, to finish the function, an object of additional scope variables, if any\r\n     * @param {...*} [formatParams] Format parameters\r\n     * @returns {Codegen|Function} Itself or the generated function if finished\r\n     * @throws {Error} If format parameter counts do not match\r\n     */\r\n\r\n    function Codegen(formatStringOrScope) {\r\n        // note that explicit array handling below makes this ~50% faster\r\n\r\n        // finish the function\r\n        if (typeof formatStringOrScope !== \"string\") {\r\n            var source = toString();\r\n            if (codegen.verbose)\r\n                console.log(\"codegen: \" + source); // eslint-disable-line no-console\r\n            source = \"return \" + source;\r\n            if (formatStringOrScope) {\r\n                var scopeKeys   = Object.keys(formatStringOrScope),\r\n                    scopeParams = new Array(scopeKeys.length + 1),\r\n                    scopeValues = new Array(scopeKeys.length),\r\n                    scopeOffset = 0;\r\n                while (scopeOffset < scopeKeys.length) {\r\n                    scopeParams[scopeOffset] = scopeKeys[scopeOffset];\r\n                    scopeValues[scopeOffset] = formatStringOrScope[scopeKeys[scopeOffset++]];\r\n                }\r\n                scopeParams[scopeOffset] = source;\r\n                return Function.apply(null, scopeParams).apply(null, scopeValues); // eslint-disable-line no-new-func\r\n            }\r\n            return Function(source)(); // eslint-disable-line no-new-func\r\n        }\r\n\r\n        // otherwise append to body\r\n        var formatParams = new Array(arguments.length - 1),\r\n            formatOffset = 0;\r\n        while (formatOffset < formatParams.length)\r\n            formatParams[formatOffset] = arguments[++formatOffset];\r\n        formatOffset = 0;\r\n        formatStringOrScope = formatStringOrScope.replace(/%([%dfijs])/g, function replace($0, $1) {\r\n            var value = formatParams[formatOffset++];\r\n            switch ($1) {\r\n                case \"d\": case \"f\": return String(Number(value));\r\n                case \"i\": return String(Math.floor(value));\r\n                case \"j\": return JSON.stringify(value);\r\n                case \"s\": return String(value);\r\n            }\r\n            return \"%\";\r\n        });\r\n        if (formatOffset !== formatParams.length)\r\n            throw Error(\"parameter count mismatch\");\r\n        body.push(formatStringOrScope);\r\n        return Codegen;\r\n    }\r\n\r\n    function toString(functionNameOverride) {\r\n        return \"function \" + (functionNameOverride || functionName || \"\") + \"(\" + (functionParams && functionParams.join(\",\") || \"\") + \"){\\n  \" + body.join(\"\\n  \") + \"\\n}\";\r\n    }\r\n\r\n    Codegen.toString = toString;\r\n    return Codegen;\r\n}\r\n\r\n/**\r\n * Begins generating a function.\r\n * @memberof util\r\n * @function codegen\r\n * @param {string} [functionName] Function name if not anonymous\r\n * @returns {Codegen} Appender that appends code to the function's body\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * When set to `true`, codegen will log generated code to console. Useful for debugging.\r\n * @name util.codegen.verbose\r\n * @type {boolean}\r\n */\r\ncodegen.verbose = false;\r\n", "\"use strict\";\r\nmodule.exports = EventEmitter;\r\n\r\n/**\r\n * Constructs a new event emitter instance.\r\n * @classdesc A minimal event emitter.\r\n * @memberof util\r\n * @constructor\r\n */\r\nfunction EventEmitter() {\r\n\r\n    /**\r\n     * Registered listeners.\r\n     * @type {Object.<string,*>}\r\n     * @private\r\n     */\r\n    this._listeners = {};\r\n}\r\n\r\n/**\r\n * Registers an event listener.\r\n * @param {string} evt Event name\r\n * @param {function} fn Listener\r\n * @param {*} [ctx] Listener context\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.on = function on(evt, fn, ctx) {\r\n    (this._listeners[evt] || (this._listeners[evt] = [])).push({\r\n        fn  : fn,\r\n        ctx : ctx || this\r\n    });\r\n    return this;\r\n};\r\n\r\n/**\r\n * Removes an event listener or any matching listeners if arguments are omitted.\r\n * @param {string} [evt] Event name. Removes all listeners if omitted.\r\n * @param {function} [fn] Listener to remove. Removes all listeners of `evt` if omitted.\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.off = function off(evt, fn) {\r\n    if (evt === undefined)\r\n        this._listeners = {};\r\n    else {\r\n        if (fn === undefined)\r\n            this._listeners[evt] = [];\r\n        else {\r\n            var listeners = this._listeners[evt];\r\n            for (var i = 0; i < listeners.length;)\r\n                if (listeners[i].fn === fn)\r\n                    listeners.splice(i, 1);\r\n                else\r\n                    ++i;\r\n        }\r\n    }\r\n    return this;\r\n};\r\n\r\n/**\r\n * Emits an event by calling its listeners with the specified arguments.\r\n * @param {string} evt Event name\r\n * @param {...*} args Arguments\r\n * @returns {util.EventEmitter} `this`\r\n */\r\nEventEmitter.prototype.emit = function emit(evt) {\r\n    var listeners = this._listeners[evt];\r\n    if (listeners) {\r\n        var args = [],\r\n            i = 1;\r\n        for (; i < arguments.length;)\r\n            args.push(arguments[i++]);\r\n        for (i = 0; i < listeners.length;)\r\n            listeners[i].fn.apply(listeners[i++].ctx, args);\r\n    }\r\n    return this;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = fetch;\r\n\r\nvar asPromise = require(1),\r\n    inquire   = require(7);\r\n\r\nvar fs = inquire(\"fs\");\r\n\r\n/**\r\n * Node-style callback as used by {@link util.fetch}.\r\n * @typedef FetchCallback\r\n * @type {function}\r\n * @param {?Error} error Error, if any, otherwise `null`\r\n * @param {string} [contents] File contents, if there hasn't been an error\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Options as used by {@link util.fetch}.\r\n * @typedef FetchOptions\r\n * @type {Object}\r\n * @property {boolean} [binary=false] Whether expecting a binary response\r\n * @property {boolean} [xhr=false] If `true`, forces the use of XMLHttpRequest\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @memberof util\r\n * @param {string} filename File path or url\r\n * @param {FetchOptions} options Fetch options\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n */\r\nfunction fetch(filename, options, callback) {\r\n    if (typeof options === \"function\") {\r\n        callback = options;\r\n        options = {};\r\n    } else if (!options)\r\n        options = {};\r\n\r\n    if (!callback)\r\n        return asPromise(fetch, this, filename, options); // eslint-disable-line no-invalid-this\r\n\r\n    // if a node-like filesystem is present, try it first but fall back to XHR if nothing is found.\r\n    if (!options.xhr && fs && fs.readFile)\r\n        return fs.readFile(filename, function fetchReadFileCallback(err, contents) {\r\n            return err && typeof XMLHttpRequest !== \"undefined\"\r\n                ? fetch.xhr(filename, options, callback)\r\n                : err\r\n                ? callback(err)\r\n                : callback(null, options.binary ? contents : contents.toString(\"utf8\"));\r\n        });\r\n\r\n    // use the XHR version otherwise.\r\n    return fetch.xhr(filename, options, callback);\r\n}\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchCallback} callback Callback function\r\n * @returns {undefined}\r\n * @variation 2\r\n */\r\n\r\n/**\r\n * Fetches the contents of a file.\r\n * @name util.fetch\r\n * @function\r\n * @param {string} path File path or url\r\n * @param {FetchOptions} [options] Fetch options\r\n * @returns {Promise<string|Uint8Array>} Promise\r\n * @variation 3\r\n */\r\n\r\n/**/\r\nfetch.xhr = function fetch_xhr(filename, options, callback) {\r\n    var xhr = new XMLHttpRequest();\r\n    xhr.onreadystatechange /* works everywhere */ = function fetchOnReadyStateChange() {\r\n\r\n        if (xhr.readyState !== 4)\r\n            return undefined;\r\n\r\n        // local cors security errors return status 0 / empty string, too. afaik this cannot be\r\n        // reliably distinguished from an actually empty file for security reasons. feel free\r\n        // to send a pull request if you are aware of a solution.\r\n        if (xhr.status !== 0 && xhr.status !== 200)\r\n            return callback(Error(\"status \" + xhr.status));\r\n\r\n        // if binary data is expected, make sure that some sort of array is returned, even if\r\n        // ArrayBuffers are not supported. the binary string fallback, however, is unsafe.\r\n        if (options.binary) {\r\n            var buffer = xhr.response;\r\n            if (!buffer) {\r\n                buffer = [];\r\n                for (var i = 0; i < xhr.responseText.length; ++i)\r\n                    buffer.push(xhr.responseText.charCodeAt(i) & 255);\r\n            }\r\n            return callback(null, typeof Uint8Array !== \"undefined\" ? new Uint8Array(buffer) : buffer);\r\n        }\r\n        return callback(null, xhr.responseText);\r\n    };\r\n\r\n    if (options.binary) {\r\n        // ref: https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/Sending_and_Receiving_Binary_Data#Receiving_binary_data_in_older_browsers\r\n        if (\"overrideMimeType\" in xhr)\r\n            xhr.overrideMimeType(\"text/plain; charset=x-user-defined\");\r\n        xhr.responseType = \"arraybuffer\";\r\n    }\r\n\r\n    xhr.open(\"GET\", filename);\r\n    xhr.send();\r\n};\r\n", "\"use strict\";\r\n\r\nmodule.exports = factory(factory);\r\n\r\n/**\r\n * Reads / writes floats / doubles from / to buffers.\r\n * @name util.float\r\n * @namespace\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using little endian byte order.\r\n * @name util.float.writeFloatLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 32 bit float to a buffer using big endian byte order.\r\n * @name util.float.writeFloatBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using little endian byte order.\r\n * @name util.float.readFloatLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 32 bit float from a buffer using big endian byte order.\r\n * @name util.float.readFloatBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using little endian byte order.\r\n * @name util.float.writeDoubleLE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Writes a 64 bit double to a buffer using big endian byte order.\r\n * @name util.float.writeDoubleBE\r\n * @function\r\n * @param {number} val Value to write\r\n * @param {Uint8Array} buf Target buffer\r\n * @param {number} pos Target buffer offset\r\n * @returns {undefined}\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using little endian byte order.\r\n * @name util.float.readDoubleLE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n/**\r\n * Reads a 64 bit double from a buffer using big endian byte order.\r\n * @name util.float.readDoubleBE\r\n * @function\r\n * @param {Uint8Array} buf Source buffer\r\n * @param {number} pos Source buffer offset\r\n * @returns {number} Value read\r\n */\r\n\r\n// Factory function for the purpose of node-based testing in modified global environments\r\nfunction factory(exports) {\r\n\r\n    // float: typed array\r\n    if (typeof Float32Array !== \"undefined\") (function() {\r\n\r\n        var f32 = new Float32Array([ -0 ]),\r\n            f8b = new Uint8Array(f32.buffer),\r\n            le  = f8b[3] === 128;\r\n\r\n        function writeFloat_f32_cpy(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n        }\r\n\r\n        function writeFloat_f32_rev(val, buf, pos) {\r\n            f32[0] = val;\r\n            buf[pos    ] = f8b[3];\r\n            buf[pos + 1] = f8b[2];\r\n            buf[pos + 2] = f8b[1];\r\n            buf[pos + 3] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeFloatLE = le ? writeFloat_f32_cpy : writeFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeFloatBE = le ? writeFloat_f32_rev : writeFloat_f32_cpy;\r\n\r\n        function readFloat_f32_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        function readFloat_f32_rev(buf, pos) {\r\n            f8b[3] = buf[pos    ];\r\n            f8b[2] = buf[pos + 1];\r\n            f8b[1] = buf[pos + 2];\r\n            f8b[0] = buf[pos + 3];\r\n            return f32[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readFloatLE = le ? readFloat_f32_cpy : readFloat_f32_rev;\r\n        /* istanbul ignore next */\r\n        exports.readFloatBE = le ? readFloat_f32_rev : readFloat_f32_cpy;\r\n\r\n    // float: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeFloat_ieee754(writeUint, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0)\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos);\r\n            else if (isNaN(val))\r\n                writeUint(2143289344, buf, pos);\r\n            else if (val > 3.4028234663852886e+38) // +-Infinity\r\n                writeUint((sign << 31 | 2139095040) >>> 0, buf, pos);\r\n            else if (val < 1.1754943508222875e-38) // denormal\r\n                writeUint((sign << 31 | Math.round(val / 1.401298464324817e-45)) >>> 0, buf, pos);\r\n            else {\r\n                var exponent = Math.floor(Math.log(val) / Math.LN2),\r\n                    mantissa = Math.round(val * Math.pow(2, -exponent) * 8388608) & 8388607;\r\n                writeUint((sign << 31 | exponent + 127 << 23 | mantissa) >>> 0, buf, pos);\r\n            }\r\n        }\r\n\r\n        exports.writeFloatLE = writeFloat_ieee754.bind(null, writeUintLE);\r\n        exports.writeFloatBE = writeFloat_ieee754.bind(null, writeUintBE);\r\n\r\n        function readFloat_ieee754(readUint, buf, pos) {\r\n            var uint = readUint(buf, pos),\r\n                sign = (uint >> 31) * 2 + 1,\r\n                exponent = uint >>> 23 & 255,\r\n                mantissa = uint & 8388607;\r\n            return exponent === 255\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 1.401298464324817e-45 * mantissa\r\n                : sign * Math.pow(2, exponent - 150) * (mantissa + 8388608);\r\n        }\r\n\r\n        exports.readFloatLE = readFloat_ieee754.bind(null, readUintLE);\r\n        exports.readFloatBE = readFloat_ieee754.bind(null, readUintBE);\r\n\r\n    })();\r\n\r\n    // double: typed array\r\n    if (typeof Float64Array !== \"undefined\") (function() {\r\n\r\n        var f64 = new Float64Array([-0]),\r\n            f8b = new Uint8Array(f64.buffer),\r\n            le  = f8b[7] === 128;\r\n\r\n        function writeDouble_f64_cpy(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[0];\r\n            buf[pos + 1] = f8b[1];\r\n            buf[pos + 2] = f8b[2];\r\n            buf[pos + 3] = f8b[3];\r\n            buf[pos + 4] = f8b[4];\r\n            buf[pos + 5] = f8b[5];\r\n            buf[pos + 6] = f8b[6];\r\n            buf[pos + 7] = f8b[7];\r\n        }\r\n\r\n        function writeDouble_f64_rev(val, buf, pos) {\r\n            f64[0] = val;\r\n            buf[pos    ] = f8b[7];\r\n            buf[pos + 1] = f8b[6];\r\n            buf[pos + 2] = f8b[5];\r\n            buf[pos + 3] = f8b[4];\r\n            buf[pos + 4] = f8b[3];\r\n            buf[pos + 5] = f8b[2];\r\n            buf[pos + 6] = f8b[1];\r\n            buf[pos + 7] = f8b[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleLE = le ? writeDouble_f64_cpy : writeDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.writeDoubleBE = le ? writeDouble_f64_rev : writeDouble_f64_cpy;\r\n\r\n        function readDouble_f64_cpy(buf, pos) {\r\n            f8b[0] = buf[pos    ];\r\n            f8b[1] = buf[pos + 1];\r\n            f8b[2] = buf[pos + 2];\r\n            f8b[3] = buf[pos + 3];\r\n            f8b[4] = buf[pos + 4];\r\n            f8b[5] = buf[pos + 5];\r\n            f8b[6] = buf[pos + 6];\r\n            f8b[7] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        function readDouble_f64_rev(buf, pos) {\r\n            f8b[7] = buf[pos    ];\r\n            f8b[6] = buf[pos + 1];\r\n            f8b[5] = buf[pos + 2];\r\n            f8b[4] = buf[pos + 3];\r\n            f8b[3] = buf[pos + 4];\r\n            f8b[2] = buf[pos + 5];\r\n            f8b[1] = buf[pos + 6];\r\n            f8b[0] = buf[pos + 7];\r\n            return f64[0];\r\n        }\r\n\r\n        /* istanbul ignore next */\r\n        exports.readDoubleLE = le ? readDouble_f64_cpy : readDouble_f64_rev;\r\n        /* istanbul ignore next */\r\n        exports.readDoubleBE = le ? readDouble_f64_rev : readDouble_f64_cpy;\r\n\r\n    // double: ieee754\r\n    })(); else (function() {\r\n\r\n        function writeDouble_ieee754(writeUint, off0, off1, val, buf, pos) {\r\n            var sign = val < 0 ? 1 : 0;\r\n            if (sign)\r\n                val = -val;\r\n            if (val === 0) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(1 / val > 0 ? /* positive */ 0 : /* negative 0 */ 2147483648, buf, pos + off1);\r\n            } else if (isNaN(val)) {\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint(2146959360, buf, pos + off1);\r\n            } else if (val > 1.7976931348623157e+308) { // +-Infinity\r\n                writeUint(0, buf, pos + off0);\r\n                writeUint((sign << 31 | 2146435072) >>> 0, buf, pos + off1);\r\n            } else {\r\n                var mantissa;\r\n                if (val < 2.2250738585072014e-308) { // denormal\r\n                    mantissa = val / 5e-324;\r\n                    writeUint(mantissa >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | mantissa / 4294967296) >>> 0, buf, pos + off1);\r\n                } else {\r\n                    var exponent = Math.floor(Math.log(val) / Math.LN2);\r\n                    if (exponent === 1024)\r\n                        exponent = 1023;\r\n                    mantissa = val * Math.pow(2, -exponent);\r\n                    writeUint(mantissa * 4503599627370496 >>> 0, buf, pos + off0);\r\n                    writeUint((sign << 31 | exponent + 1023 << 20 | mantissa * 1048576 & 1048575) >>> 0, buf, pos + off1);\r\n                }\r\n            }\r\n        }\r\n\r\n        exports.writeDoubleLE = writeDouble_ieee754.bind(null, writeUintLE, 0, 4);\r\n        exports.writeDoubleBE = writeDouble_ieee754.bind(null, writeUintBE, 4, 0);\r\n\r\n        function readDouble_ieee754(readUint, off0, off1, buf, pos) {\r\n            var lo = readUint(buf, pos + off0),\r\n                hi = readUint(buf, pos + off1);\r\n            var sign = (hi >> 31) * 2 + 1,\r\n                exponent = hi >>> 20 & 2047,\r\n                mantissa = 4294967296 * (hi & 1048575) + lo;\r\n            return exponent === 2047\r\n                ? mantissa\r\n                ? NaN\r\n                : sign * Infinity\r\n                : exponent === 0 // denormal\r\n                ? sign * 5e-324 * mantissa\r\n                : sign * Math.pow(2, exponent - 1075) * (mantissa + 4503599627370496);\r\n        }\r\n\r\n        exports.readDoubleLE = readDouble_ieee754.bind(null, readUintLE, 0, 4);\r\n        exports.readDoubleBE = readDouble_ieee754.bind(null, readUintBE, 4, 0);\r\n\r\n    })();\r\n\r\n    return exports;\r\n}\r\n\r\n// uint helpers\r\n\r\nfunction writeUintLE(val, buf, pos) {\r\n    buf[pos    ] =  val        & 255;\r\n    buf[pos + 1] =  val >>> 8  & 255;\r\n    buf[pos + 2] =  val >>> 16 & 255;\r\n    buf[pos + 3] =  val >>> 24;\r\n}\r\n\r\nfunction writeUintBE(val, buf, pos) {\r\n    buf[pos    ] =  val >>> 24;\r\n    buf[pos + 1] =  val >>> 16 & 255;\r\n    buf[pos + 2] =  val >>> 8  & 255;\r\n    buf[pos + 3] =  val        & 255;\r\n}\r\n\r\nfunction readUintLE(buf, pos) {\r\n    return (buf[pos    ]\r\n          | buf[pos + 1] << 8\r\n          | buf[pos + 2] << 16\r\n          | buf[pos + 3] << 24) >>> 0;\r\n}\r\n\r\nfunction readUintBE(buf, pos) {\r\n    return (buf[pos    ] << 24\r\n          | buf[pos + 1] << 16\r\n          | buf[pos + 2] << 8\r\n          | buf[pos + 3]) >>> 0;\r\n}\r\n", "\"use strict\";\r\nmodule.exports = inquire;\r\n\r\n/**\r\n * Requires a module only if available.\r\n * @memberof util\r\n * @param {string} moduleName Module to require\r\n * @returns {?Object} Required module if available and not empty, otherwise `null`\r\n */\r\nfunction inquire(moduleName) {\r\n    try {\r\n        var mod = eval(\"quire\".replace(/^/,\"re\"))(moduleName); // eslint-disable-line no-eval\r\n        if (mod && (mod.length || Object.keys(mod).length))\r\n            return mod;\r\n    } catch (e) {} // eslint-disable-line no-empty\r\n    return null;\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal path module to resolve Unix, Windows and URL paths alike.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar path = exports;\r\n\r\nvar isAbsolute =\r\n/**\r\n * Tests if the specified path is absolute.\r\n * @param {string} path Path to test\r\n * @returns {boolean} `true` if path is absolute\r\n */\r\npath.isAbsolute = function isAbsolute(path) {\r\n    return /^(?:\\/|\\w+:)/.test(path);\r\n};\r\n\r\nvar normalize =\r\n/**\r\n * Normalizes the specified path.\r\n * @param {string} path Path to normalize\r\n * @returns {string} Normalized path\r\n */\r\npath.normalize = function normalize(path) {\r\n    path = path.replace(/\\\\/g, \"/\")\r\n               .replace(/\\/{2,}/g, \"/\");\r\n    var parts    = path.split(\"/\"),\r\n        absolute = isAbsolute(path),\r\n        prefix   = \"\";\r\n    if (absolute)\r\n        prefix = parts.shift() + \"/\";\r\n    for (var i = 0; i < parts.length;) {\r\n        if (parts[i] === \"..\") {\r\n            if (i > 0 && parts[i - 1] !== \"..\")\r\n                parts.splice(--i, 2);\r\n            else if (absolute)\r\n                parts.splice(i, 1);\r\n            else\r\n                ++i;\r\n        } else if (parts[i] === \".\")\r\n            parts.splice(i, 1);\r\n        else\r\n            ++i;\r\n    }\r\n    return prefix + parts.join(\"/\");\r\n};\r\n\r\n/**\r\n * Resolves the specified include path against the specified origin path.\r\n * @param {string} originPath Path to the origin file\r\n * @param {string} includePath Include path relative to origin path\r\n * @param {boolean} [alreadyNormalized=false] `true` if both paths are already known to be normalized\r\n * @returns {string} Path to the include file\r\n */\r\npath.resolve = function resolve(originPath, includePath, alreadyNormalized) {\r\n    if (!alreadyNormalized)\r\n        includePath = normalize(includePath);\r\n    if (isAbsolute(includePath))\r\n        return includePath;\r\n    if (!alreadyNormalized)\r\n        originPath = normalize(originPath);\r\n    return (originPath = originPath.replace(/(?:\\/|^)[^/]+$/, \"\")).length ? normalize(originPath + \"/\" + includePath) : includePath;\r\n};\r\n", "\"use strict\";\r\nmodule.exports = pool;\r\n\r\n/**\r\n * An allocator as used by {@link util.pool}.\r\n * @typedef PoolAllocator\r\n * @type {function}\r\n * @param {number} size Buffer size\r\n * @returns {Uint8Array} Buffer\r\n */\r\n\r\n/**\r\n * A slicer as used by {@link util.pool}.\r\n * @typedef PoolSlicer\r\n * @type {function}\r\n * @param {number} start Start offset\r\n * @param {number} end End offset\r\n * @returns {Uint8Array} Buffer slice\r\n * @this {Uint8Array}\r\n */\r\n\r\n/**\r\n * A general purpose buffer pool.\r\n * @memberof util\r\n * @function\r\n * @param {PoolAllocator} alloc Allocator\r\n * @param {PoolSlicer} slice Slicer\r\n * @param {number} [size=8192] Slab size\r\n * @returns {PoolAllocator} Pooled allocator\r\n */\r\nfunction pool(alloc, slice, size) {\r\n    var SIZE   = size || 8192;\r\n    var MAX    = SIZE >>> 1;\r\n    var slab   = null;\r\n    var offset = SIZE;\r\n    return function pool_alloc(size) {\r\n        if (size < 1 || size > MAX)\r\n            return alloc(size);\r\n        if (offset + size > SIZE) {\r\n            slab = alloc(SIZE);\r\n            offset = 0;\r\n        }\r\n        var buf = slice.call(slab, offset, offset += size);\r\n        if (offset & 7) // align to 32 bit\r\n            offset = (offset | 7) + 1;\r\n        return buf;\r\n    };\r\n}\r\n", "\"use strict\";\r\n\r\n/**\r\n * A minimal UTF8 implementation for number arrays.\r\n * @memberof util\r\n * @namespace\r\n */\r\nvar utf8 = exports;\r\n\r\n/**\r\n * Calculates the UTF8 byte length of a string.\r\n * @param {string} string String\r\n * @returns {number} Byte length\r\n */\r\nutf8.length = function utf8_length(string) {\r\n    var len = 0,\r\n        c = 0;\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c = string.charCodeAt(i);\r\n        if (c < 128)\r\n            len += 1;\r\n        else if (c < 2048)\r\n            len += 2;\r\n        else if ((c & 0xFC00) === 0xD800 && (string.charCodeAt(i + 1) & 0xFC00) === 0xDC00) {\r\n            ++i;\r\n            len += 4;\r\n        } else\r\n            len += 3;\r\n    }\r\n    return len;\r\n};\r\n\r\n/**\r\n * Reads UTF8 bytes as a string.\r\n * @param {Uint8Array} buffer Source buffer\r\n * @param {number} start Source start\r\n * @param {number} end Source end\r\n * @returns {string} String read\r\n */\r\nutf8.read = function utf8_read(buffer, start, end) {\r\n    var len = end - start;\r\n    if (len < 1)\r\n        return \"\";\r\n    var parts = null,\r\n        chunk = [],\r\n        i = 0, // char offset\r\n        t;     // temporary\r\n    while (start < end) {\r\n        t = buffer[start++];\r\n        if (t < 128)\r\n            chunk[i++] = t;\r\n        else if (t > 191 && t < 224)\r\n            chunk[i++] = (t & 31) << 6 | buffer[start++] & 63;\r\n        else if (t > 239 && t < 365) {\r\n            t = ((t & 7) << 18 | (buffer[start++] & 63) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63) - 0x10000;\r\n            chunk[i++] = 0xD800 + (t >> 10);\r\n            chunk[i++] = 0xDC00 + (t & 1023);\r\n        } else\r\n            chunk[i++] = (t & 15) << 12 | (buffer[start++] & 63) << 6 | buffer[start++] & 63;\r\n        if (i > 8191) {\r\n            (parts || (parts = [])).push(String.fromCharCode.apply(String, chunk));\r\n            i = 0;\r\n        }\r\n    }\r\n    if (parts) {\r\n        if (i)\r\n            parts.push(String.fromCharCode.apply(String, chunk.slice(0, i)));\r\n        return parts.join(\"\");\r\n    }\r\n    return String.fromCharCode.apply(String, chunk.slice(0, i));\r\n};\r\n\r\n/**\r\n * Writes a string as UTF8 bytes.\r\n * @param {string} string Source string\r\n * @param {Uint8Array} buffer Destination buffer\r\n * @param {number} offset Destination offset\r\n * @returns {number} Bytes written\r\n */\r\nutf8.write = function utf8_write(string, buffer, offset) {\r\n    var start = offset,\r\n        c1, // character 1\r\n        c2; // character 2\r\n    for (var i = 0; i < string.length; ++i) {\r\n        c1 = string.charCodeAt(i);\r\n        if (c1 < 128) {\r\n            buffer[offset++] = c1;\r\n        } else if (c1 < 2048) {\r\n            buffer[offset++] = c1 >> 6       | 192;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else if ((c1 & 0xFC00) === 0xD800 && ((c2 = string.charCodeAt(i + 1)) & 0xFC00) === 0xDC00) {\r\n            c1 = 0x10000 + ((c1 & 0x03FF) << 10) + (c2 & 0x03FF);\r\n            ++i;\r\n            buffer[offset++] = c1 >> 18      | 240;\r\n            buffer[offset++] = c1 >> 12 & 63 | 128;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        } else {\r\n            buffer[offset++] = c1 >> 12      | 224;\r\n            buffer[offset++] = c1 >> 6  & 63 | 128;\r\n            buffer[offset++] = c1       & 63 | 128;\r\n        }\r\n    }\r\n    return offset - start;\r\n};\r\n", "\"use strict\";\nmodule.exports = common;\n\nvar commonRe = /\\/|\\./;\n\n/**\n * Provides common type definitions.\n * Can also be used to provide additional google types or your own custom types.\n * @param {string} name Short name as in `google/protobuf/[name].proto` or full file name\n * @param {Object.<string,*>} json JSON definition within `google.protobuf` if a short name, otherwise the file's root definition\n * @returns {undefined}\n * @property {INamespace} google/protobuf/any.proto Any\n * @property {INamespace} google/protobuf/duration.proto Duration\n * @property {INamespace} google/protobuf/empty.proto Empty\n * @property {INamespace} google/protobuf/field_mask.proto FieldMask\n * @property {INamespace} google/protobuf/struct.proto Struct, Value, NullValue and ListValue\n * @property {INamespace} google/protobuf/timestamp.proto Timestamp\n * @property {INamespace} google/protobuf/wrappers.proto Wrappers\n * @example\n * // manually provides descriptor.proto (assumes google/protobuf/ namespace and .proto extension)\n * protobuf.common(\"descriptor\", descriptorJson);\n *\n * // manually provides a custom definition (uses my.foo namespace)\n * protobuf.common(\"my/foo/bar.proto\", myFooBarJson);\n */\nfunction common(name, json) {\n    if (!commonRe.test(name)) {\n        name = \"google/protobuf/\" + name + \".proto\";\n        json = { nested: { google: { nested: { protobuf: { nested: json } } } } };\n    }\n    common[name] = json;\n}\n\n// Not provided because of limited use (feel free to discuss or to provide yourself):\n//\n// google/protobuf/descriptor.proto\n// google/protobuf/source_context.proto\n// google/protobuf/type.proto\n//\n// Stripped and pre-parsed versions of these non-bundled files are instead available as part of\n// the repository or package within the google/protobuf directory.\n\ncommon(\"any\", {\n\n    /**\n     * Properties of a google.protobuf.Any message.\n     * @interface IAny\n     * @type {Object}\n     * @property {string} [typeUrl]\n     * @property {Uint8Array} [bytes]\n     * @memberof common\n     */\n    Any: {\n        fields: {\n            type_url: {\n                type: \"string\",\n                id: 1\n            },\n            value: {\n                type: \"bytes\",\n                id: 2\n            }\n        }\n    }\n});\n\nvar timeType;\n\ncommon(\"duration\", {\n\n    /**\n     * Properties of a google.protobuf.Duration message.\n     * @interface IDuration\n     * @type {Object}\n     * @property {number} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Duration: timeType = {\n        fields: {\n            seconds: {\n                type: \"int64\",\n                id: 1\n            },\n            nanos: {\n                type: \"int32\",\n                id: 2\n            }\n        }\n    }\n});\n\ncommon(\"timestamp\", {\n\n    /**\n     * Properties of a google.protobuf.Timestamp message.\n     * @interface ITimestamp\n     * @type {Object}\n     * @property {number} [seconds]\n     * @property {number} [nanos]\n     * @memberof common\n     */\n    Timestamp: timeType\n});\n\ncommon(\"empty\", {\n\n    /**\n     * Properties of a google.protobuf.Empty message.\n     * @interface IEmpty\n     * @memberof common\n     */\n    Empty: {\n        fields: {}\n    }\n});\n\ncommon(\"struct\", {\n\n    /**\n     * Properties of a google.protobuf.Struct message.\n     * @interface IStruct\n     * @type {Object}\n     * @property {Object.<string,IValue>} [fields]\n     * @memberof common\n     */\n    Struct: {\n        fields: {\n            fields: {\n                keyType: \"string\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Value message.\n     * @interface IValue\n     * @type {Object}\n     * @property {string} [kind]\n     * @property {0} [nullValue]\n     * @property {number} [numberValue]\n     * @property {string} [stringValue]\n     * @property {boolean} [boolValue]\n     * @property {IStruct} [structValue]\n     * @property {IListValue} [listValue]\n     * @memberof common\n     */\n    Value: {\n        oneofs: {\n            kind: {\n                oneof: [\n                    \"nullValue\",\n                    \"numberValue\",\n                    \"stringValue\",\n                    \"boolValue\",\n                    \"structValue\",\n                    \"listValue\"\n                ]\n            }\n        },\n        fields: {\n            nullValue: {\n                type: \"NullValue\",\n                id: 1\n            },\n            numberValue: {\n                type: \"double\",\n                id: 2\n            },\n            stringValue: {\n                type: \"string\",\n                id: 3\n            },\n            boolValue: {\n                type: \"bool\",\n                id: 4\n            },\n            structValue: {\n                type: \"Struct\",\n                id: 5\n            },\n            listValue: {\n                type: \"ListValue\",\n                id: 6\n            }\n        }\n    },\n\n    NullValue: {\n        values: {\n            NULL_VALUE: 0\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.ListValue message.\n     * @interface IListValue\n     * @type {Object}\n     * @property {Array.<IValue>} [values]\n     * @memberof common\n     */\n    ListValue: {\n        fields: {\n            values: {\n                rule: \"repeated\",\n                type: \"Value\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"wrappers\", {\n\n    /**\n     * Properties of a google.protobuf.DoubleValue message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    DoubleValue: {\n        fields: {\n            value: {\n                type: \"double\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.FloatValue message.\n     * @interface IFloatValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FloatValue: {\n        fields: {\n            value: {\n                type: \"float\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int64Value message.\n     * @interface IInt64Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    Int64Value: {\n        fields: {\n            value: {\n                type: \"int64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt64Value message.\n     * @interface IUInt64Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    UInt64Value: {\n        fields: {\n            value: {\n                type: \"uint64\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.Int32Value message.\n     * @interface IInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    Int32Value: {\n        fields: {\n            value: {\n                type: \"int32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.UInt32Value message.\n     * @interface IUInt32Value\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    UInt32Value: {\n        fields: {\n            value: {\n                type: \"uint32\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BoolValue message.\n     * @interface IBoolValue\n     * @type {Object}\n     * @property {boolean} [value]\n     * @memberof common\n     */\n    BoolValue: {\n        fields: {\n            value: {\n                type: \"bool\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.StringValue message.\n     * @interface IStringValue\n     * @type {Object}\n     * @property {string} [value]\n     * @memberof common\n     */\n    StringValue: {\n        fields: {\n            value: {\n                type: \"string\",\n                id: 1\n            }\n        }\n    },\n\n    /**\n     * Properties of a google.protobuf.BytesValue message.\n     * @interface IBytesValue\n     * @type {Object}\n     * @property {Uint8Array} [value]\n     * @memberof common\n     */\n    BytesValue: {\n        fields: {\n            value: {\n                type: \"bytes\",\n                id: 1\n            }\n        }\n    }\n});\n\ncommon(\"field_mask\", {\n\n    /**\n     * Properties of a google.protobuf.FieldMask message.\n     * @interface IDoubleValue\n     * @type {Object}\n     * @property {number} [value]\n     * @memberof common\n     */\n    FieldMask: {\n        fields: {\n            paths: {\n                rule: \"repeated\",\n                type: \"string\",\n                id: 1\n            }\n        }\n    }\n});\n\n/**\n * Gets the root definition of the specified common proto file.\n *\n * Bundled definitions are:\n * - google/protobuf/any.proto\n * - google/protobuf/duration.proto\n * - google/protobuf/empty.proto\n * - google/protobuf/field_mask.proto\n * - google/protobuf/struct.proto\n * - google/protobuf/timestamp.proto\n * - google/protobuf/wrappers.proto\n *\n * @param {string} file Proto file name\n * @returns {INamespace|null} Root definition or `null` if not defined\n */\ncommon.get = function get(file) {\n    return common[file] || null;\n};\n", "\"use strict\";\n/**\n * Runtime message from/to plain object converters.\n * @namespace\n */\nvar converter = exports;\n\nvar Enum = require(15),\n    util = require(37);\n\n/**\n * Generates a partial value fromObject conveter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_fromObject(gen, field, fieldIndex, prop, ref) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (ref === undefined) {\n      ref = \"d\" + prop;\n    }\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref);\n            for (var values = field.resolvedType.values, keys = Object.keys(values), i = 0; i < keys.length; ++i) {\n                if (field.repeated && values[keys[i]] === field.typeDefault) gen\n                (\"default:\");\n                gen\n                (\"case%j:\", keys[i])\n                (\"case %i:\", values[keys[i]])\n                    (\"m%s=%j\", prop, values[keys[i]])\n                    (\"break\");\n            } gen\n            (\"}\");\n        } else gen\n            (\"if(typeof %s!==\\\"object\\\")\", ref)\n                (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n            (\"m%s=types[%i].fromObject(%s)\", prop, fieldIndex, ref);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n                (\"m%s=Number(%s)\", prop, ref); // also catches \"NaN\", \"Infinity\"\n                break;\n            case \"uint32\":\n            case \"fixed32\": gen\n                (\"m%s=%s>>>0\", prop, ref);\n                break;\n            case \"int32\":\n            case \"sint32\":\n            case \"sfixed32\": gen\n                (\"m%s=%s|0\", prop, ref);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(util.Long)\")\n                    (\"(m%s=util.Long.fromValue(%s)).unsigned=%j\", prop, ref, isUnsigned)\n                (\"else if(typeof %s===\\\"string\\\")\", ref)\n                    (\"m%s=parseInt(%s,10)\", prop, ref)\n                (\"else if(typeof %s===\\\"number\\\")\", ref)\n                    (\"m%s=%s\", prop, ref)\n                (\"else if(typeof %s===\\\"object\\\")\", ref)\n                    (\"m%s=new util.LongBits(%s.low>>>0,%s.high>>>0).toNumber(%s)\", prop, ref, ref, isUnsigned ? \"true\" : \"\");\n                break;\n            case \"bytes\": gen\n                (\"if(typeof %s===\\\"string\\\")\", ref)\n                    (\"util.base64.decode(%s,m%s=util.newBuffer(util.base64.length(%s)),0)\", ref, prop, ref)\n                (\"else if(%s.length)\", ref)\n                    (\"m%s=%s\", prop, ref);\n                break;\n            case \"string\": gen\n                (\"m%s=String(%s)\", prop, ref);\n                break;\n            case \"bool\": gen\n                (\"m%s=Boolean(%s)\", prop, ref);\n                break;\n            /* default: gen\n                (\"m%s=%s\", prop, ref);\n                break; */\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a plain object to runtime message converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.fromObject = function fromObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray;\n    var gen = util.codegen([\"d\"], mtype.name + \"$fromObject\")\n    (\"if(d instanceof this.ctor)\")\n        (\"return d\");\n    if (!fields.length) return gen\n    (\"return new this.ctor\");\n    gen\n    (\"var m=new this.ctor\");\n    for (var i = 0; i < fields.length; ++i) {\n        var field  = fields[i].resolve(),\n            prop   = util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) { gen\n    (\"if(d%s){\", prop)\n        (\"if(typeof d%s!==\\\"object\\\")\", prop)\n            (\"throw TypeError(%j)\", field.fullName + \": object expected\")\n        (\"m%s={}\", prop)\n        (\"for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){\", prop);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[ks[i]]\")\n        (\"}\")\n    (\"}\");\n\n        // Repeated fields\n        } else if (field.repeated) {\n          gen(\"if(d%s){\", prop);\n          var arrayRef = \"d\" + prop;\n          if (field.useToArray()) {\n            arrayRef = \"array\" + field.id;\n            gen(\"var %s\", arrayRef);\n            gen(\"if (d%s!=null&&d%s.toArray) { %s = d%s.toArray() } else { %s = d%s }\",\n                prop, prop, arrayRef, prop, arrayRef, prop);\n          }\n          gen\n        (\"if(!Array.isArray(%s))\", arrayRef)\n            (\"throw TypeError(%j)\", field.fullName + \": array expected\")\n        (\"m%s=[]\", prop)\n        (\"for(var i=0;i<%s.length;++i){\", arrayRef);\n            genValuePartial_fromObject(gen, field, /* not sorted */ i, prop + \"[i]\", arrayRef + \"[i]\")\n        (\"}\")\n    (\"}\");\n\n        // Non-repeated fields\n        } else {\n            if (!(field.resolvedType instanceof Enum)) gen // no need to test for null/undefined if an enum (uses switch)\n    (\"if(d%s!=null){\", prop); // !== undefined && !== null\n        genValuePartial_fromObject(gen, field, /* not sorted */ i, prop);\n            if (!(field.resolvedType instanceof Enum)) gen\n    (\"}\");\n        }\n    } return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n\n/**\n * Generates a partial value toObject converter.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} prop Property reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genValuePartial_toObject(gen, field, fieldIndex, prop) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) gen\n            (\"d%s=o.enums===String?types[%i].values[m%s]:m%s\", prop, fieldIndex, prop, prop);\n        else gen\n            (\"d%s=types[%i].toObject(m%s,o)\", prop, fieldIndex, prop);\n    } else {\n        var isUnsigned = false;\n        switch (field.type) {\n            case \"double\":\n            case \"float\": gen\n            (\"d%s=o.json&&!isFinite(m%s)?String(m%s):m%s\", prop, prop, prop, prop);\n                break;\n            case \"uint64\":\n                isUnsigned = true;\n                // eslint-disable-line no-fallthrough\n            case \"int64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n            (\"if(typeof m%s===\\\"number\\\")\", prop)\n                (\"d%s=o.longs===String?String(m%s):m%s\", prop, prop, prop)\n            (\"else\") // Long-like\n                (\"d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s\", prop, prop, prop, prop, isUnsigned ? \"true\": \"\", prop);\n                break;\n            case \"bytes\": gen\n            (\"d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s\", prop, prop, prop, prop, prop);\n                break;\n            default: gen\n            (\"d%s=m%s\", prop, prop);\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n\n/**\n * Generates a runtime message to plain object converter specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nconverter.toObject = function toObject(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var fields = mtype.fieldsArray.slice().sort(util.compareFieldsById);\n    if (!fields.length)\n        return util.codegen()(\"return {}\");\n    var gen = util.codegen([\"m\", \"o\"], mtype.name + \"$toObject\")\n    (\"if(!o)\")\n        (\"o={}\")\n    (\"var d={}\");\n\n    var repeatedFields = [],\n        mapFields = [],\n        normalFields = [],\n        i = 0;\n    for (; i < fields.length; ++i)\n        if (!fields[i].partOf)\n            ( fields[i].resolve().repeated ? repeatedFields\n            : fields[i].map ? mapFields\n            : normalFields).push(fields[i]);\n\n    if (repeatedFields.length) { gen\n    (\"if(o.arrays||o.defaults){\");\n        for (i = 0; i < repeatedFields.length; ++i) gen\n        (\"d%s=[]\", util.safeProp(repeatedFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (mapFields.length) { gen\n    (\"if(o.objects||o.defaults){\");\n        for (i = 0; i < mapFields.length; ++i) gen\n        (\"d%s={}\", util.safeProp(mapFields[i].name));\n        gen\n    (\"}\");\n    }\n\n    if (normalFields.length) { gen\n    (\"if(o.defaults){\");\n        for (i = 0; i < normalFields.length; ++i) {\n            var field = normalFields[i],\n                prop  = util.safeProp(field.name);\n            if (field.resolvedType instanceof Enum) gen\n        (\"d%s=o.enums===String?%j:%j\", prop, field.resolvedType.valuesById[field.typeDefault], field.typeDefault);\n            else if (field.long) gen\n        (\"if(util.Long){\")\n            (\"var n=new util.Long(%i,%i,%j)\", field.typeDefault.low, field.typeDefault.high, field.typeDefault.unsigned)\n            (\"d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n\", prop)\n        (\"}else\")\n            (\"d%s=o.longs===String?%j:%i\", prop, field.typeDefault.toString(), field.typeDefault.toNumber());\n            else if (field.bytes) {\n                var arrayDefault = \"[\" + Array.prototype.slice.call(field.typeDefault).join(\",\") + \"]\";\n                gen\n        (\"if(o.bytes===String)d%s=%j\", prop, String.fromCharCode.apply(String, field.typeDefault))\n        (\"else{\")\n            (\"d%s=%s\", prop, arrayDefault)\n            (\"if(o.bytes!==Array)d%s=util.newBuffer(d%s)\", prop, prop)\n        (\"}\");\n            } else gen\n        (\"d%s=%j\", prop, field.typeDefault); // also messages (=null)\n        } gen\n    (\"}\");\n    }\n    var hasKs2 = false;\n    for (i = 0; i < fields.length; ++i) {\n        var field = fields[i],\n            index = mtype._fieldsArray.indexOf(field),\n            prop  = util.safeProp(field.name);\n        if (field.map) {\n            if (!hasKs2) { hasKs2 = true; gen\n    (\"var ks2\");\n            } gen\n    (\"if(m%s&&(ks2=Object.keys(m%s)).length){\", prop, prop)\n        (\"d%s={}\", prop)\n        (\"for(var j=0;j<ks2.length;++j){\");\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[ks2[j]]\")\n        (\"}\");\n        } else if (field.repeated) { gen\n    (\"if(m%s&&m%s.length){\", prop, prop)\n        (\"d%s=[]\", prop)\n        (\"for(var j=0;j<m%s.length;++j){\", prop);\n            genValuePartial_toObject(gen, field, /* sorted */ index, prop + \"[j]\")\n        (\"}\");\n        } else { gen\n    (\"if(m%s!=null&&m.hasOwnProperty(%j)){\", prop, field.name); // !== undefined && !== null\n        genValuePartial_toObject(gen, field, /* sorted */ index, prop);\n        if (field.partOf) gen\n        (\"if(o.oneofs)\")\n            (\"d%s=%j\", util.safeProp(field.partOf.name), field.name);\n        }\n        gen\n    (\"}\");\n    }\n    return gen\n    (\"return d\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n};\n", "\"use strict\";\nmodule.exports = decoder;\n\nvar Enum    = require(15),\n    types   = require(36),\n    util    = require(37);\n\nfunction missing(field) {\n    return \"missing required '\" + field.name + \"'\";\n}\n\n/**\n * Generates a decoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction decoder(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"r\", \"l\"], mtype.name + \"$decode\")\n    (\"if(!(r instanceof Reader))\")\n        (\"r=Reader.create(r)\")\n    (\"var c=l===undefined?r.len:r.pos+l,m=new this.ctor\" + (mtype.fieldsArray.filter(function(field) { return field.map; }).length ? \",k\" : \"\"))\n    (\"while(r.pos<c){\")\n        (\"var t=r.uint32()\");\n    if (mtype.group) gen\n        (\"if((t&7)===4)\")\n            (\"break\");\n    gen\n        (\"switch(t>>>3){\");\n\n    var i = 0;\n    for (; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            type  = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            ref   = \"m\" + util.safeProp(field.name); gen\n            (\"case %i:\", field.id);\n\n        // Map fields\n        if (field.map) { gen\n                (\"r.skip().pos++\") // assumes id 1 + key wireType\n                (\"if(%s===util.emptyObject)\", ref)\n                    (\"%s={}\", ref)\n                (\"k=r.%s()\", field.keyType)\n                (\"r.pos++\"); // assumes id 2 + value wireType\n            if (types.long[field.keyType] !== undefined) {\n                if (types.basic[type] === undefined) gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=types[%i].decode(r,r.uint32())\", ref, i); // can't be groups\n                else gen\n                (\"%s[typeof k===\\\"object\\\"?util.longToHash(k):k]=r.%s()\", ref, type);\n            } else {\n                if (types.basic[type] === undefined) gen\n                (\"%s[k]=types[%i].decode(r,r.uint32())\", ref, i); // can't be groups\n                else gen\n                (\"%s[k]=r.%s()\", ref, type);\n            }\n\n        // Repeated fields\n        } else if (field.repeated) { gen\n\n                (\"if(!(%s&&%s.length))\", ref, ref)\n                    (\"%s=[]\", ref);\n\n            // Packable (always check for forward and backward compatiblity)\n            if (types.packed[type] !== undefined) gen\n                (\"if((t&7)===2){\")\n                    (\"var c2=r.uint32()+r.pos\")\n                    (\"while(r.pos<c2)\")\n                        (\"%s.push(r.%s())\", ref, type)\n                (\"}else\");\n\n            // Non-packed\n            if (types.basic[type] === undefined) gen(field.resolvedType.group\n                    ? \"%s.push(types[%i].decode(r))\"\n                    : \"%s.push(types[%i].decode(r,r.uint32()))\", ref, i);\n            else gen\n                    (\"%s.push(r.%s())\", ref, type);\n\n        // Non-repeated\n        } else if (types.basic[type] === undefined) gen(field.resolvedType.group\n                ? \"%s=types[%i].decode(r)\"\n                : \"%s=types[%i].decode(r,r.uint32())\", ref, i);\n        else gen\n                (\"%s=r.%s()\", ref, type);\n        gen\n                (\"break\");\n    // Unknown fields\n    } gen\n            (\"default:\")\n                (\"r.skipType(t&7)\")\n                (\"break\")\n\n        (\"}\")\n    (\"}\");\n\n    // Field presence\n    for (i = 0; i < mtype._fieldsArray.length; ++i) {\n        var rfield = mtype._fieldsArray[i];\n        if (rfield.required) gen\n    (\"if(!m.hasOwnProperty(%j))\", rfield.name)\n        (\"throw util.ProtocolError(%j,{instance:m})\", missing(rfield));\n    }\n\n    return gen\n    (\"return m\");\n    /* eslint-enable no-unexpected-multiline */\n}\n", "\"use strict\";\nmodule.exports = encoder;\n\nvar Enum     = require(15),\n    types    = require(36),\n    util     = require(37);\n\n/**\n * Generates a partial message type encoder.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genTypePartial(gen, field, fieldIndex, ref) {\n  /* eslint-disable no-unexpected-multiline */\n  if (field.resolvedType.group) {\n    gen(\"types[%i].encode(%s,w.uint32(%i)).uint32(%i)\", fieldIndex, ref, (field.id << 3 | 3) >>> 0, (field.id << 3 | 4) >>> 0);\n    return;\n  }\n  var key = (field.id << 3 | 2) >>> 0;\n  if (field.preEncoded()) {\n    gen(\"if (%s instanceof Uint8Array) {\", ref)\n    (\"w.uint32(%i)\", key)\n    (\"w.bytes(%s)\", ref)\n    (\"} else {\");\n  }\n  gen(\"types[%i].encode(%s,w.uint32(%i).fork()).ldelim()\", fieldIndex, ref, key);\n  if (field.preEncoded()) {\n    gen(\"}\")\n  }\n}\n\n/**\n * Generates an encoder specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction encoder(mtype) {\n    /* eslint-disable no-unexpected-multiline, block-scoped-var, no-redeclare */\n    var gen = util.codegen([\"m\", \"w\"], mtype.name + \"$encode\")\n    (\"if(!w)\")\n        (\"w=Writer.create()\");\n\n    var i, ref;\n\n    // \"when a message is serialized its known fields should be written sequentially by field number\"\n    var fields = /* initializes */ mtype.fieldsArray.slice().sort(util.compareFieldsById);\n\n    for (var i = 0; i < fields.length; ++i) {\n        var field    = fields[i].resolve(),\n            index    = mtype._fieldsArray.indexOf(field),\n            type     = field.resolvedType instanceof Enum ? \"int32\" : field.type,\n            wireType = types.basic[type];\n            ref      = \"m\" + util.safeProp(field.name);\n\n        // Map fields\n        if (field.map) {\n            gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j)){\", ref, field.name) // !== undefined && !== null\n        (\"for(var ks=Object.keys(%s),i=0;i<ks.length;++i){\", ref)\n            (\"w.uint32(%i).fork().uint32(%i).%s(ks[i])\", (field.id << 3 | 2) >>> 0, 8 | types.mapKey[field.keyType], field.keyType);\n            if (wireType === undefined) gen\n            (\"types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()\", index, ref); // can't be groups\n            else gen\n            (\".uint32(%i).%s(%s[ks[i]]).ldelim()\", 16 | wireType, type, ref);\n            gen\n        (\"}\")\n    (\"}\");\n\n            // Repeated fields\n        } else if (field.repeated) {\n          var arrayRef = ref;\n          if (field.useToArray()) {\n            arrayRef = \"array\" + field.id;\n            gen(\"var %s\", arrayRef);\n            gen(\"if (%s!=null&&%s.toArray) { %s = %s.toArray() } else { %s = %s }\",\n                ref, ref, arrayRef, ref, arrayRef, ref);\n          }\n          gen(\"if(%s!=null&&%s.length){\", arrayRef, arrayRef); // !== undefined && !== null\n            // Packed repeated\n            if (field.packed && types.packed[type] !== undefined) { gen\n\n        (\"w.uint32(%i).fork()\", (field.id << 3 | 2) >>> 0)\n        (\"for(var i=0;i<%s.length;++i)\", arrayRef)\n            (\"w.%s(%s[i])\", type, arrayRef)\n        (\"w.ldelim()\");\n\n            // Non-packed\n            } else { gen\n\n        (\"for(var i=0;i<%s.length;++i)\", arrayRef);\n                if (wireType === undefined)\n            genTypePartial(gen, field, index, arrayRef + \"[i]\");\n                else gen\n            (\"w.uint32(%i).%s(%s[i])\", (field.id << 3 | wireType) >>> 0, type, arrayRef);\n\n            } gen\n    (\"}\");\n\n        // Non-repeated\n        } else {\n            if (field.optional) gen\n    (\"if(%s!=null&&Object.hasOwnProperty.call(m,%j))\", ref, field.name); // !== undefined && !== null\n\n            if (wireType === undefined)\n        genTypePartial(gen, field, index, ref);\n            else gen\n        (\"w.uint32(%i).%s(%s)\", (field.id << 3 | wireType) >>> 0, type, ref);\n\n        }\n    }\n\n    return gen\n    (\"return w\");\n    /* eslint-enable no-unexpected-multiline, block-scoped-var, no-redeclare */\n}\n", "\"use strict\";\nmodule.exports = Enum;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Enum.prototype = Object.create(ReflectionObject.prototype)).constructor = Enum).className = \"Enum\";\n\nvar Namespace = require(23),\n    util = require(37);\n\n/**\n * Constructs a new enum instance.\n * @classdesc Reflected enum.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {Object.<string,number>} [values] Enum values as an object, by name\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this enum\n * @param {Object.<string,string>} [comments] The value comments for this enum\n */\nfunction Enum(name, values, options, comment, comments) {\n    ReflectionObject.call(this, name, options);\n\n    if (values && typeof values !== \"object\")\n        throw TypeError(\"values must be an object\");\n\n    /**\n     * Enum values by id.\n     * @type {Object.<number,string>}\n     */\n    this.valuesById = {};\n\n    /**\n     * Enum values by name.\n     * @type {Object.<string,number>}\n     */\n    this.values = Object.create(this.valuesById); // toJSON, marker\n\n    /**\n     * Enum comment text.\n     * @type {string|null}\n     */\n    this.comment = comment;\n\n    /**\n     * Value comment texts, if any.\n     * @type {Object.<string,string>}\n     */\n    this.comments = comments || {};\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    // Note that values inherit valuesById on their prototype which makes them a TypeScript-\n    // compatible enum. This is used by pbts to write actual enum definitions that work for\n    // static and reflection code alike instead of emitting generic object definitions.\n\n    if (values)\n        for (var keys = Object.keys(values), i = 0; i < keys.length; ++i)\n            if (typeof values[keys[i]] === \"number\") // use forward entries only\n                this.valuesById[ this.values[keys[i]] = values[keys[i]] ] = keys[i];\n}\n\n/**\n * Enum descriptor.\n * @interface IEnum\n * @property {Object.<string,number>} values Enum values\n * @property {Object.<string,*>} [options] Enum options\n */\n\n/**\n * Constructs an enum from an enum descriptor.\n * @param {string} name Enum name\n * @param {IEnum} json Enum descriptor\n * @returns {Enum} Created enum\n * @throws {TypeError} If arguments are invalid\n */\nEnum.fromJSON = function fromJSON(name, json) {\n    var enm = new Enum(name, json.values, json.options, json.comment, json.comments);\n    enm.reserved = json.reserved;\n    return enm;\n};\n\n/**\n * Converts this enum to an enum descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IEnum} Enum descriptor\n */\nEnum.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"  , this.options,\n        \"values\"   , this.values,\n        \"reserved\" , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"comment\"  , keepComments ? this.comment : undefined,\n        \"comments\" , keepComments ? this.comments : undefined\n    ]);\n};\n\n/**\n * Adds a value to this enum.\n * @param {string} name Value name\n * @param {number} id Value id\n * @param {string} [comment] Comment, if any\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a value with this name or id\n */\nEnum.prototype.add = function add(name, id, comment) {\n    // utilized by the parser but not by .fromJSON\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (!util.isInteger(id))\n        throw TypeError(\"id must be an integer\");\n\n    if (this.values[name] !== undefined)\n        throw Error(\"duplicate name '\" + name + \"' in \" + this);\n\n    if (this.isReservedId(id))\n        throw Error(\"id \" + id + \" is reserved in \" + this);\n\n    if (this.isReservedName(name))\n        throw Error(\"name '\" + name + \"' is reserved in \" + this);\n\n    if (this.valuesById[id] !== undefined) {\n        if (!(this.options && this.options.allow_alias))\n            throw Error(\"duplicate id \" + id + \" in \" + this);\n        this.values[name] = id;\n    } else\n        this.valuesById[this.values[name] = id] = name;\n\n    this.comments[name] = comment || null;\n    return this;\n};\n\n/**\n * Removes a value from this enum\n * @param {string} name Value name\n * @returns {Enum} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `name` is not a name of this enum\n */\nEnum.prototype.remove = function remove(name) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    var val = this.values[name];\n    if (val == null)\n        throw Error(\"name '\" + name + \"' does not exist in \" + this);\n\n    delete this.valuesById[val];\n    delete this.values[name];\n    delete this.comments[name];\n\n    return this;\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nEnum.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n", "\"use strict\";\nmodule.exports = Field;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Field.prototype = Object.create(ReflectionObject.prototype)).constructor = Field).className = \"Field\";\n\nvar Enum  = require(15),\n    types = require(36),\n    util  = require(37);\n\nvar Type; // cyclic\n\nvar ruleRe = /^required|optional|repeated$/;\n\n/**\n * Constructs a new message field instance. Note that {@link MapField|map fields} have their own class.\n * @name Field\n * @classdesc Reflected message field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a field from a field descriptor.\n * @param {string} name Field name\n * @param {IField} json Field descriptor\n * @returns {Field} Created field\n * @throws {TypeError} If arguments are invalid\n */\nField.fromJSON = function fromJSON(name, json) {\n    return new Field(name, json.id, json.type, json.rule, json.extend, json.options, json.comment);\n};\n\n/**\n * Not an actual constructor. Use {@link Field} instead.\n * @classdesc Base class of all reflected message fields. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports FieldBase\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} type Value type\n * @param {string|Object.<string,*>} [rule=\"optional\"] Field rule\n * @param {string|Object.<string,*>} [extend] Extended type if different from parent\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction Field(name, id, type, rule, extend, options, comment) {\n\n    if (util.isObject(rule)) {\n        comment = extend;\n        options = rule;\n        rule = extend = undefined;\n    } else if (util.isObject(extend)) {\n        comment = options;\n        options = extend;\n        extend = undefined;\n    }\n\n    ReflectionObject.call(this, name, options);\n\n    if (!util.isInteger(id) || id < 0)\n        throw TypeError(\"id must be a non-negative integer\");\n\n    if (!util.isString(type))\n        throw TypeError(\"type must be a string\");\n\n    if (rule !== undefined && !ruleRe.test(rule = rule.toString().toLowerCase()))\n        throw TypeError(\"rule must be a string rule\");\n\n    if (extend !== undefined && !util.isString(extend))\n        throw TypeError(\"extend must be a string\");\n\n    /**\n     * Field rule, if any.\n     * @type {string|undefined}\n     */\n    this.rule = rule && rule !== \"optional\" ? rule : undefined; // toJSON\n\n    /**\n     * Field type.\n     * @type {string}\n     */\n    this.type = type; // toJSON\n\n    /**\n     * Unique field id.\n     * @type {number}\n     */\n    this.id = id; // toJSON, marker\n\n    /**\n     * Extended type if different from parent.\n     * @type {string|undefined}\n     */\n    this.extend = extend || undefined; // toJSON\n\n    /**\n     * Whether this field is required.\n     * @type {boolean}\n     */\n    this.required = rule === \"required\";\n\n    /**\n     * Whether this field is optional.\n     * @type {boolean}\n     */\n    this.optional = !this.required;\n\n    /**\n     * Whether this field is repeated.\n     * @type {boolean}\n     */\n    this.repeated = rule === \"repeated\";\n\n    /**\n     * Whether this field is a map or not.\n     * @type {boolean}\n     */\n    this.map = false;\n\n    /**\n     * Message this field belongs to.\n     * @type {Type|null}\n     */\n    this.message = null;\n\n    /**\n     * OneOf this field belongs to, if any,\n     * @type {OneOf|null}\n     */\n    this.partOf = null;\n\n    /**\n     * The field type's default value.\n     * @type {*}\n     */\n    this.typeDefault = null;\n\n    /**\n     * The field's default value on prototypes.\n     * @type {*}\n     */\n    this.defaultValue = null;\n\n    /**\n     * Whether this field's value should be treated as a long.\n     * @type {boolean}\n     */\n    this.long = util.Long ? types.long[type] !== undefined : /* istanbul ignore next */ false;\n\n    /**\n     * Whether this field's value is a buffer.\n     * @type {boolean}\n     */\n    this.bytes = type === \"bytes\";\n\n    /**\n     * Resolved type if not a basic type.\n     * @type {Type|Enum|null}\n     */\n    this.resolvedType = null;\n\n    /**\n     * Sister-field within the extended type if a declaring extension field.\n     * @type {Field|null}\n     */\n    this.extensionField = null;\n\n    /**\n     * Sister-field within the declaring namespace if an extended field.\n     * @type {Field|null}\n     */\n    this.declaringField = null;\n\n    /**\n     * Internally remembers whether this field is packed.\n     * @type {boolean|null}\n     * @private\n     */\n    this._packed = null;\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Determines whether this field is packed. Only relevant when repeated and working with proto2.\n * @name Field#packed\n * @type {boolean}\n * @readonly\n */\nObject.defineProperty(Field.prototype, \"packed\", {\n    get: function() {\n        // defaults to packed=true if not explicity set to false\n        if (this._packed === null)\n            this._packed = this.getOption(\"packed\") !== false;\n        return this._packed;\n    }\n});\n\n/**\n * @override\n */\nField.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (name === \"packed\") // clear cached before setting\n        this._packed = null;\n    return ReflectionObject.prototype.setOption.call(this, name, value, ifNotSet);\n};\n\n/**\n * Field descriptor.\n * @interface IField\n * @property {string} [rule=\"optional\"] Field rule\n * @property {string} type Field type\n * @property {number} id Field id\n * @property {Object.<string,*>} [options] Field options\n */\n\n/**\n * Extension field descriptor.\n * @interface IExtensionField\n * @extends IField\n * @property {string} extend Extended type\n */\n\n/**\n * Converts this field to a field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IField} Field descriptor\n */\nField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"rule\"    , this.rule !== \"optional\" && this.rule || undefined,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Resolves this field's type references.\n * @returns {Field} `this`\n * @throws {Error} If any reference cannot be resolved\n */\nField.prototype.resolve = function resolve() {\n\n    if (this.resolved)\n        return this;\n\n    if ((this.typeDefault = types.defaults[this.type]) === undefined) { // if not a basic type, resolve it\n        this.resolvedType = (this.declaringField ? this.declaringField.parent : this.parent).lookupTypeOrEnum(this.type);\n        if (this.resolvedType instanceof Type)\n            this.typeDefault = null;\n        else // instanceof Enum\n            this.typeDefault = this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]; // first defined\n    }\n\n    // use explicitly set default value if present\n    if (this.options && this.options[\"default\"] != null) {\n        this.typeDefault = this.options[\"default\"];\n        if (this.resolvedType instanceof Enum && typeof this.typeDefault === \"string\")\n            this.typeDefault = this.resolvedType.values[this.typeDefault];\n    }\n\n    // remove unnecessary options\n    if (this.options) {\n        if (this.options.packed === true || this.options.packed !== undefined && this.resolvedType && !(this.resolvedType instanceof Enum))\n            delete this.options.packed;\n        if (!Object.keys(this.options).length)\n            this.options = undefined;\n    }\n\n    // convert to internal data type if necesssary\n    if (this.long) {\n        this.typeDefault = util.Long.fromNumber(this.typeDefault, this.type.charAt(0) === \"u\");\n\n        /* istanbul ignore else */\n        if (Object.freeze)\n            Object.freeze(this.typeDefault); // long instances are meant to be immutable anyway (i.e. use small int cache that even requires it)\n\n    } else if (this.bytes && typeof this.typeDefault === \"string\") {\n        var buf;\n        if (util.base64.test(this.typeDefault))\n            util.base64.decode(this.typeDefault, buf = util.newBuffer(util.base64.length(this.typeDefault)), 0);\n        else\n            util.utf8.write(this.typeDefault, buf = util.newBuffer(util.utf8.length(this.typeDefault)), 0);\n        this.typeDefault = buf;\n    }\n\n    // take special care of maps and repeated fields\n    if (this.map)\n        this.defaultValue = util.emptyObject;\n    else if (this.repeated)\n        this.defaultValue = util.emptyArray;\n    else\n        this.defaultValue = this.typeDefault;\n\n    // ensure proper value on prototype\n    if (this.parent instanceof Type)\n        this.parent.ctor.prototype[this.name] = this.defaultValue;\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n\nField.prototype.useToArray = function useToArray() {\n    return !!this.getOption(\"(js_use_toArray)\");\n};\n\nField.prototype.preEncoded = function preEncoded() {\n    return !!this.getOption(\"(js_preEncoded)\");\n};\n\n/**\n * Decorator function as returned by {@link Field.d} and {@link MapField.d} (TypeScript).\n * @typedef FieldDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} fieldName Field name\n * @returns {undefined}\n */\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"string\"|\"bool\"|\"bytes\"|Object} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @param {T} [defaultValue] Default value\n * @returns {FieldDecorator} Decorator function\n * @template T extends number | number[] | string | string[] | boolean | boolean[] | Uint8Array | Uint8Array[] | Buffer | Buffer[]\n */\nField.d = function decorateField(fieldId, fieldType, fieldRule, defaultValue) {\n\n    // submessage: decorate the submessage and use its name as the type\n    if (typeof fieldType === \"function\")\n        fieldType = util.decorateType(fieldType).name;\n\n    // enum reference: create a reflected copy of the enum and keep reuseing it\n    else if (fieldType && typeof fieldType === \"object\")\n        fieldType = util.decorateEnum(fieldType).name;\n\n    return function fieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new Field(fieldName, fieldId, fieldType, fieldRule, { \"default\": defaultValue }));\n    };\n};\n\n/**\n * Field decorator (TypeScript).\n * @name Field.d\n * @function\n * @param {number} fieldId Field id\n * @param {Constructor<T>|string} fieldType Field type\n * @param {\"optional\"|\"required\"|\"repeated\"} [fieldRule=\"optional\"] Field rule\n * @returns {FieldDecorator} Decorator function\n * @template T extends Message<T>\n * @variation 2\n */\n// like Field.d but without a default value\n\n// Sets up cyclic dependencies (called in index-light)\nField._configure = function configure(Type_) {\n    Type = Type_;\n};\n", "\"use strict\";\nvar protobuf = module.exports = require(18);\n\nprotobuf.build = \"light\";\n\n/**\n * A node-style callback as used by {@link load} and {@link Root#load}.\n * @typedef LoadCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Root} [root] Root, if there hasn't been an error\n * @returns {undefined}\n */\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} root Root namespace, defaults to create a new one if omitted.\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n */\nfunction load(filename, root, callback) {\n    if (typeof root === \"function\") {\n        callback = root;\n        root = new protobuf.Root();\n    } else if (!root)\n        root = new protobuf.Root();\n    return root.load(filename, callback);\n}\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and calls the callback.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @see {@link Root#load}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into a common root namespace and returns a promise.\n * @name load\n * @function\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Promise<Root>} Promise\n * @see {@link Root#load}\n * @variation 3\n */\n// function load(filename:string, [root:Root]):Promise<Root>\n\nprotobuf.load = load;\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into a common root namespace (node only).\n * @param {string|string[]} filename One or multiple files to load\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n * @see {@link Root#loadSync}\n */\nfunction loadSync(filename, root) {\n    if (!root)\n        root = new protobuf.Root();\n    return root.loadSync(filename);\n}\n\nprotobuf.loadSync = loadSync;\n\n// Serialization\nprotobuf.encoder          = require(14);\nprotobuf.decoder          = require(13);\nprotobuf.verifier         = require(40);\nprotobuf.converter        = require(12);\n\n// Reflection\nprotobuf.ReflectionObject = require(24);\nprotobuf.Namespace        = require(23);\nprotobuf.Root             = require(29);\nprotobuf.Enum             = require(15);\nprotobuf.Type             = require(35);\nprotobuf.Field            = require(16);\nprotobuf.OneOf            = require(25);\nprotobuf.MapField         = require(20);\nprotobuf.Service          = require(33);\nprotobuf.Method           = require(22);\n\n// Runtime\nprotobuf.Message          = require(21);\nprotobuf.wrappers         = require(41);\n\n// Utility\nprotobuf.types            = require(36);\nprotobuf.util             = require(37);\n\n// Set up possibly cyclic reflection dependencies\nprotobuf.ReflectionObject._configure(protobuf.Root);\nprotobuf.Namespace._configure(protobuf.Type, protobuf.Service, protobuf.Enum);\nprotobuf.Root._configure(protobuf.Type);\nprotobuf.Field._configure(protobuf.Type);\n", "\"use strict\";\nvar protobuf = exports;\n\n/**\n * Build type, one of `\"full\"`, `\"light\"` or `\"minimal\"`.\n * @name build\n * @type {string}\n * @const\n */\nprotobuf.build = \"minimal\";\n\n// Serialization\nprotobuf.Writer       = require(42);\nprotobuf.BufferWriter = require(43);\nprotobuf.Reader       = require(27);\nprotobuf.BufferReader = require(28);\n\n// Utility\nprotobuf.util         = require(39);\nprotobuf.rpc          = require(31);\nprotobuf.roots        = require(30);\nprotobuf.configure    = configure;\n\n/* istanbul ignore next */\n/**\n * Reconfigures the library according to the environment.\n * @returns {undefined}\n */\nfunction configure() {\n    protobuf.Reader._configure(protobuf.BufferReader);\n    protobuf.util._configure();\n}\n\n// Set up buffer utility according to the environment\nprotobuf.Writer._configure(protobuf.BufferWriter);\nconfigure();\n", "\"use strict\";\nvar protobuf = module.exports = require(17);\n\nprotobuf.build = \"full\";\n\n// Parser\nprotobuf.tokenize         = require(34);\nprotobuf.parse            = require(26);\nprotobuf.common           = require(11);\n\n// Configure parser\nprotobuf.Root._configure(protobuf.Type, protobuf.parse, protobuf.common);\n", "\"use strict\";\nmodule.exports = MapField;\n\n// extends Field\nvar Field = require(16);\n((MapField.prototype = Object.create(Field.prototype)).constructor = MapField).className = \"MapField\";\n\nvar types   = require(36),\n    util    = require(37);\n\n/**\n * Constructs a new map field instance.\n * @classdesc Reflected map field.\n * @extends FieldBase\n * @constructor\n * @param {string} name Unique name within its namespace\n * @param {number} id Unique id within its namespace\n * @param {string} keyType Key type\n * @param {string} type Value type\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction MapField(name, id, keyType, type, options, comment) {\n    Field.call(this, name, id, type, undefined, undefined, options, comment);\n\n    /* istanbul ignore if */\n    if (!util.isString(keyType))\n        throw TypeError(\"keyType must be a string\");\n\n    /**\n     * Key type.\n     * @type {string}\n     */\n    this.keyType = keyType; // toJSON, marker\n\n    /**\n     * Resolved key type if not a basic type.\n     * @type {ReflectionObject|null}\n     */\n    this.resolvedKeyType = null;\n\n    // Overrides Field#map\n    this.map = true;\n}\n\n/**\n * Map field descriptor.\n * @interface IMapField\n * @extends {IField}\n * @property {string} keyType Key type\n */\n\n/**\n * Extension map field descriptor.\n * @interface IExtensionMapField\n * @extends IMapField\n * @property {string} extend Extended type\n */\n\n/**\n * Constructs a map field from a map field descriptor.\n * @param {string} name Field name\n * @param {IMapField} json Map field descriptor\n * @returns {MapField} Created map field\n * @throws {TypeError} If arguments are invalid\n */\nMapField.fromJSON = function fromJSON(name, json) {\n    return new MapField(name, json.id, json.keyType, json.type, json.options, json.comment);\n};\n\n/**\n * Converts this map field to a map field descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMapField} Map field descriptor\n */\nMapField.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"keyType\" , this.keyType,\n        \"type\"    , this.type,\n        \"id\"      , this.id,\n        \"extend\"  , this.extend,\n        \"options\" , this.options,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMapField.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n\n    // Besides a value type, map fields have a key type that may be \"any scalar type except for floating point types and bytes\"\n    if (types.mapKey[this.keyType] === undefined)\n        throw Error(\"invalid key type: \" + this.keyType);\n\n    return Field.prototype.resolve.call(this);\n};\n\n/**\n * Map field decorator (TypeScript).\n * @name MapField.d\n * @function\n * @param {number} fieldId Field id\n * @param {\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"} fieldKeyType Field key type\n * @param {\"double\"|\"float\"|\"int32\"|\"uint32\"|\"sint32\"|\"fixed32\"|\"sfixed32\"|\"int64\"|\"uint64\"|\"sint64\"|\"fixed64\"|\"sfixed64\"|\"bool\"|\"string\"|\"bytes\"|Object|Constructor<{}>} fieldValueType Field value type\n * @returns {FieldDecorator} Decorator function\n * @template T extends { [key: string]: number | string | boolean | Uint8Array | Buffer | number[] | Message<{}> }\n */\nMapField.d = function decorateMapField(fieldId, fieldKeyType, fieldValueType) {\n\n    // submessage value: decorate the submessage and use its name as the type\n    if (typeof fieldValueType === \"function\")\n        fieldValueType = util.decorateType(fieldValueType).name;\n\n    // enum reference value: create a reflected copy of the enum and keep reuseing it\n    else if (fieldValueType && typeof fieldValueType === \"object\")\n        fieldValueType = util.decorateEnum(fieldValueType).name;\n\n    return function mapFieldDecorator(prototype, fieldName) {\n        util.decorateType(prototype.constructor)\n            .add(new MapField(fieldName, fieldId, fieldKeyType, fieldValueType));\n    };\n};\n", "\"use strict\";\nmodule.exports = Message;\n\nvar util = require(39);\n\n/**\n * Constructs a new message instance.\n * @classdesc Abstract runtime message.\n * @constructor\n * @param {Properties<T>} [properties] Properties to set\n * @template T extends object = object\n */\nfunction Message(properties) {\n    // not used internally\n    if (properties)\n        for (var keys = Object.keys(properties), i = 0; i < keys.length; ++i)\n            this[keys[i]] = properties[keys[i]];\n}\n\n/**\n * Reference to the reflected type.\n * @name Message.$type\n * @type {Type}\n * @readonly\n */\n\n/**\n * Reference to the reflected type.\n * @name Message#$type\n * @type {Type}\n * @readonly\n */\n\n/*eslint-disable valid-jsdoc*/\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<T>} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.create = function create(properties) {\n    return this.$type.create(properties);\n};\n\n/**\n * Encodes a message of this type.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encode = function encode(message, writer) {\n    return this.$type.encode(message, writer);\n};\n\n/**\n * Encodes a message of this type preceeded by its length as a varint.\n * @param {T|Object.<string,*>} message Message to encode\n * @param {Writer} [writer] Writer to use\n * @returns {Writer} Writer\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.$type.encodeDelimited(message, writer);\n};\n\n/**\n * Decodes a message of this type.\n * @name Message.decode\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decode = function decode(reader) {\n    return this.$type.decode(reader);\n};\n\n/**\n * Decodes a message of this type preceeded by its length as a varint.\n * @name Message.decodeDelimited\n * @function\n * @param {Reader|Uint8Array} reader Reader or buffer to decode\n * @returns {T} Decoded message\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.decodeDelimited = function decodeDelimited(reader) {\n    return this.$type.decodeDelimited(reader);\n};\n\n/**\n * Verifies a message of this type.\n * @name Message.verify\n * @function\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {string|null} `null` if valid, otherwise the reason why it is not\n */\nMessage.verify = function verify(message) {\n    return this.$type.verify(message);\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object\n * @returns {T} Message instance\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.fromObject = function fromObject(object) {\n    return this.$type.fromObject(object);\n};\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {T} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @template T extends Message<T>\n * @this Constructor<T>\n */\nMessage.toObject = function toObject(message, options) {\n    return this.$type.toObject(message, options);\n};\n\n/**\n * Converts this message to JSON.\n * @returns {Object.<string,*>} JSON object\n */\nMessage.prototype.toJSON = function toJSON() {\n    return this.$type.toObject(this, util.toJSONOptions);\n};\n\n/*eslint-enable valid-jsdoc*/", "\"use strict\";\nmodule.exports = Method;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Method.prototype = Object.create(ReflectionObject.prototype)).constructor = Method).className = \"Method\";\n\nvar util = require(37);\n\n/**\n * Constructs a new service method instance.\n * @classdesc Reflected service method.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Method name\n * @param {string|undefined} type Method type, usually `\"rpc\"`\n * @param {string} requestType Request message type\n * @param {string} responseType Response message type\n * @param {boolean|Object.<string,*>} [requestStream] Whether the request is streamed\n * @param {boolean|Object.<string,*>} [responseStream] Whether the response is streamed\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] The comment for this method\n */\nfunction Method(name, type, requestType, responseType, requestStream, responseStream, options, comment) {\n\n    /* istanbul ignore next */\n    if (util.isObject(requestStream)) {\n        options = requestStream;\n        requestStream = responseStream = undefined;\n    } else if (util.isObject(responseStream)) {\n        options = responseStream;\n        responseStream = undefined;\n    }\n\n    /* istanbul ignore if */\n    if (!(type === undefined || util.isString(type)))\n        throw TypeError(\"type must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(requestType))\n        throw TypeError(\"requestType must be a string\");\n\n    /* istanbul ignore if */\n    if (!util.isString(responseType))\n        throw TypeError(\"responseType must be a string\");\n\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Method type.\n     * @type {string}\n     */\n    this.type = type || \"rpc\"; // toJSON\n\n    /**\n     * Request type.\n     * @type {string}\n     */\n    this.requestType = requestType; // toJSON, marker\n\n    /**\n     * Whether requests are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.requestStream = requestStream ? true : undefined; // toJSON\n\n    /**\n     * Response type.\n     * @type {string}\n     */\n    this.responseType = responseType; // toJSON\n\n    /**\n     * Whether responses are streamed or not.\n     * @type {boolean|undefined}\n     */\n    this.responseStream = responseStream ? true : undefined; // toJSON\n\n    /**\n     * Resolved request type.\n     * @type {Type|null}\n     */\n    this.resolvedRequestType = null;\n\n    /**\n     * Resolved response type.\n     * @type {Type|null}\n     */\n    this.resolvedResponseType = null;\n\n    /**\n     * Comment for this method\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Method descriptor.\n * @interface IMethod\n * @property {string} [type=\"rpc\"] Method type\n * @property {string} requestType Request type\n * @property {string} responseType Response type\n * @property {boolean} [requestStream=false] Whether requests are streamed\n * @property {boolean} [responseStream=false] Whether responses are streamed\n * @property {Object.<string,*>} [options] Method options\n */\n\n/**\n * Constructs a method from a method descriptor.\n * @param {string} name Method name\n * @param {IMethod} json Method descriptor\n * @returns {Method} Created method\n * @throws {TypeError} If arguments are invalid\n */\nMethod.fromJSON = function fromJSON(name, json) {\n    return new Method(name, json.type, json.requestType, json.responseType, json.requestStream, json.responseStream, json.options, json.comment);\n};\n\n/**\n * Converts this method to a method descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IMethod} Method descriptor\n */\nMethod.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"type\"           , this.type !== \"rpc\" && /* istanbul ignore next */ this.type || undefined,\n        \"requestType\"    , this.requestType,\n        \"requestStream\"  , this.requestStream,\n        \"responseType\"   , this.responseType,\n        \"responseStream\" , this.responseStream,\n        \"options\"        , this.options,\n        \"comment\"        , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nMethod.prototype.resolve = function resolve() {\n\n    /* istanbul ignore if */\n    if (this.resolved)\n        return this;\n\n    this.resolvedRequestType = this.parent.lookupType(this.requestType);\n    this.resolvedResponseType = this.parent.lookupType(this.responseType);\n\n    return ReflectionObject.prototype.resolve.call(this);\n};\n", "\"use strict\";\nmodule.exports = Namespace;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((Namespace.prototype = Object.create(ReflectionObject.prototype)).constructor = Namespace).className = \"Namespace\";\n\nvar Field    = require(16),\n    util     = require(37);\n\nvar Type,    // cyclic\n    Service,\n    Enum;\n\n/**\n * Constructs a new namespace instance.\n * @name Namespace\n * @classdesc Reflected namespace.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n */\n\n/**\n * Constructs a namespace from JSON.\n * @memberof Namespace\n * @function\n * @param {string} name Namespace name\n * @param {Object.<string,*>} json JSON object\n * @returns {Namespace} Created namespace\n * @throws {TypeError} If arguments are invalid\n */\nNamespace.fromJSON = function fromJSON(name, json) {\n    return new Namespace(name, json.options).addJSON(json.nested);\n};\n\n/**\n * Converts an array of reflection objects to JSON.\n * @memberof Namespace\n * @param {ReflectionObject[]} array Object array\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {Object.<string,*>|undefined} JSON object or `undefined` when array is empty\n */\nfunction arrayToJSON(array, toJSONOptions) {\n    if (!(array && array.length))\n        return undefined;\n    var obj = {};\n    for (var i = 0; i < array.length; ++i)\n        obj[array[i].name] = array[i].toJSON(toJSONOptions);\n    return obj;\n}\n\nNamespace.arrayToJSON = arrayToJSON;\n\n/**\n * Tests if the specified id is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedId = function isReservedId(reserved, id) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (typeof reserved[i] !== \"string\" && reserved[i][0] <= id && reserved[i][1] > id)\n                return true;\n    return false;\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {Array.<number[]|string>|undefined} reserved Array of reserved ranges and names\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nNamespace.isReservedName = function isReservedName(reserved, name) {\n    if (reserved)\n        for (var i = 0; i < reserved.length; ++i)\n            if (reserved[i] === name)\n                return true;\n    return false;\n};\n\n/**\n * Not an actual constructor. Use {@link Namespace} instead.\n * @classdesc Base class of all reflection objects containing nested objects. This is not an actual class but here for the sake of having consistent type definitions.\n * @exports NamespaceBase\n * @extends ReflectionObject\n * @abstract\n * @constructor\n * @param {string} name Namespace name\n * @param {Object.<string,*>} [options] Declared options\n * @see {@link Namespace}\n */\nfunction Namespace(name, options) {\n    ReflectionObject.call(this, name, options);\n\n    /**\n     * Nested objects by name.\n     * @type {Object.<string,ReflectionObject>|undefined}\n     */\n    this.nested = undefined; // toJSON\n\n    /**\n     * Cached nested objects as an array.\n     * @type {ReflectionObject[]|null}\n     * @private\n     */\n    this._nestedArray = null;\n}\n\nfunction clearCache(namespace) {\n    namespace._nestedArray = null;\n    return namespace;\n}\n\n/**\n * Nested objects of this namespace as an array for iteration.\n * @name NamespaceBase#nestedArray\n * @type {ReflectionObject[]}\n * @readonly\n */\nObject.defineProperty(Namespace.prototype, \"nestedArray\", {\n    get: function() {\n        return this._nestedArray || (this._nestedArray = util.toArray(this.nested));\n    }\n});\n\n/**\n * Namespace descriptor.\n * @interface INamespace\n * @property {Object.<string,*>} [options] Namespace options\n * @property {Object.<string,AnyNestedObject>} [nested] Nested object descriptors\n */\n\n/**\n * Any extension field descriptor.\n * @typedef AnyExtensionField\n * @type {IExtensionField|IExtensionMapField}\n */\n\n/**\n * Any nested object descriptor.\n * @typedef AnyNestedObject\n * @type {IEnum|IType|IService|AnyExtensionField|INamespace}\n */\n// ^ BEWARE: VSCode hangs forever when using more than 5 types (that's why AnyExtensionField exists in the first place)\n\n/**\n * Converts this namespace to a namespace descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {INamespace} Namespace descriptor\n */\nNamespace.prototype.toJSON = function toJSON(toJSONOptions) {\n    return util.toObject([\n        \"options\" , this.options,\n        \"nested\"  , arrayToJSON(this.nestedArray, toJSONOptions)\n    ]);\n};\n\n/**\n * Adds nested objects to this namespace from nested object descriptors.\n * @param {Object.<string,AnyNestedObject>} nestedJson Any nested object descriptors\n * @returns {Namespace} `this`\n */\nNamespace.prototype.addJSON = function addJSON(nestedJson) {\n    var ns = this;\n    /* istanbul ignore else */\n    if (nestedJson) {\n        for (var names = Object.keys(nestedJson), i = 0, nested; i < names.length; ++i) {\n            nested = nestedJson[names[i]];\n            ns.add( // most to least likely\n                ( nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : nested.id !== undefined\n                ? Field.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    }\n    return this;\n};\n\n/**\n * Gets the nested object of the specified name.\n * @param {string} name Nested object name\n * @returns {ReflectionObject|null} The reflection object or `null` if it doesn't exist\n */\nNamespace.prototype.get = function get(name) {\n    return this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Gets the values of the nested {@link Enum|enum} of the specified name.\n * This methods differs from {@link Namespace#get|get} in that it returns an enum's values directly and throws instead of returning `null`.\n * @param {string} name Nested enum name\n * @returns {Object.<string,number>} Enum values\n * @throws {Error} If there is no such enum\n */\nNamespace.prototype.getEnum = function getEnum(name) {\n    if (this.nested && this.nested[name] instanceof Enum)\n        return this.nested[name].values;\n    throw Error(\"no such enum: \" + name);\n};\n\n/**\n * Adds a nested object to this namespace.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name\n */\nNamespace.prototype.add = function add(object) {\n\n    if (!(object instanceof Field && object.extend !== undefined || object instanceof Type || object instanceof Enum || object instanceof Service || object instanceof Namespace))\n        throw TypeError(\"object must be a valid nested object\");\n\n    if (!this.nested)\n        this.nested = {};\n    else {\n        var prev = this.get(object.name);\n        if (prev) {\n            if (prev instanceof Namespace && object instanceof Namespace && !(prev instanceof Type || prev instanceof Service)) {\n                // replace plain namespace but keep existing nested elements and options\n                var nested = prev.nestedArray;\n                for (var i = 0; i < nested.length; ++i)\n                    object.add(nested[i]);\n                this.remove(prev);\n                if (!this.nested)\n                    this.nested = {};\n                object.setOptions(prev.options, true);\n\n            } else\n                throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n        }\n    }\n    this.nested[object.name] = object;\n    object.onAdd(this);\n    return clearCache(this);\n};\n\n/**\n * Removes a nested object from this namespace.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Namespace} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this namespace\n */\nNamespace.prototype.remove = function remove(object) {\n\n    if (!(object instanceof ReflectionObject))\n        throw TypeError(\"object must be a ReflectionObject\");\n    if (object.parent !== this)\n        throw Error(object + \" is not a member of \" + this);\n\n    delete this.nested[object.name];\n    if (!Object.keys(this.nested).length)\n        this.nested = undefined;\n\n    object.onRemove(this);\n    return clearCache(this);\n};\n\n/**\n * Defines additial namespaces within this one if not yet existing.\n * @param {string|string[]} path Path to create\n * @param {*} [json] Nested types to create from JSON\n * @returns {Namespace} Pointer to the last namespace created or `this` if path is empty\n */\nNamespace.prototype.define = function define(path, json) {\n\n    if (util.isString(path))\n        path = path.split(\".\");\n    else if (!Array.isArray(path))\n        throw TypeError(\"illegal path\");\n    if (path && path.length && path[0] === \"\")\n        throw Error(\"path must be relative\");\n\n    var ptr = this;\n    while (path.length > 0) {\n        var part = path.shift();\n        if (ptr.nested && ptr.nested[part]) {\n            ptr = ptr.nested[part];\n            if (!(ptr instanceof Namespace))\n                throw Error(\"path conflicts with non-namespace objects\");\n        } else\n            ptr.add(ptr = new Namespace(part));\n    }\n    if (json)\n        ptr.addJSON(json);\n    return ptr;\n};\n\n/**\n * Resolves this namespace's and all its nested objects' type references. Useful to validate a reflection tree, but comes at a cost.\n * @returns {Namespace} `this`\n */\nNamespace.prototype.resolveAll = function resolveAll() {\n    var nested = this.nestedArray, i = 0;\n    while (i < nested.length)\n        if (nested[i] instanceof Namespace)\n            nested[i++].resolveAll();\n        else\n            nested[i++].resolve();\n    return this.resolve();\n};\n\n/**\n * Recursively looks up the reflection object matching the specified path in the scope of this namespace.\n * @param {string|string[]} path Path to look up\n * @param {*|Array.<*>} filterTypes Filter types, any combination of the constructors of `protobuf.Type`, `protobuf.Enum`, `protobuf.Service` etc.\n * @param {boolean} [parentAlreadyChecked=false] If known, whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n */\nNamespace.prototype.lookup = function lookup(path, filterTypes, parentAlreadyChecked) {\n\n    /* istanbul ignore next */\n    if (typeof filterTypes === \"boolean\") {\n        parentAlreadyChecked = filterTypes;\n        filterTypes = undefined;\n    } else if (filterTypes && !Array.isArray(filterTypes))\n        filterTypes = [ filterTypes ];\n\n    if (util.isString(path) && path.length) {\n        if (path === \".\")\n            return this.root;\n        path = path.split(\".\");\n    } else if (!path.length)\n        return this;\n\n    // Start at root if path is absolute\n    if (path[0] === \"\")\n        return this.root.lookup(path.slice(1), filterTypes);\n\n    // Test if the first part matches any nested object, and if so, traverse if path contains more\n    var found = this.get(path[0]);\n    if (found) {\n        if (path.length === 1) {\n            if (!filterTypes || filterTypes.indexOf(found.constructor) > -1)\n                return found;\n        } else if (found instanceof Namespace && (found = found.lookup(path.slice(1), filterTypes, true)))\n            return found;\n\n    // Otherwise try each nested namespace\n    } else\n        for (var i = 0; i < this.nestedArray.length; ++i)\n            if (this._nestedArray[i] instanceof Namespace && (found = this._nestedArray[i].lookup(path, filterTypes, true)))\n                return found;\n\n    // If there hasn't been a match, try again at the parent\n    if (this.parent === null || parentAlreadyChecked)\n        return null;\n    return this.parent.lookup(path, filterTypes);\n};\n\n/**\n * Looks up the reflection object at the specified path, relative to this namespace.\n * @name NamespaceBase#lookup\n * @function\n * @param {string|string[]} path Path to look up\n * @param {boolean} [parentAlreadyChecked=false] Whether the parent has already been checked\n * @returns {ReflectionObject|null} Looked up object or `null` if none could be found\n * @variation 2\n */\n// lookup(path: string, [parentAlreadyChecked: boolean])\n\n/**\n * Looks up the {@link Type|type} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type\n * @throws {Error} If `path` does not point to a type\n */\nNamespace.prototype.lookupType = function lookupType(path) {\n    var found = this.lookup(path, [ Type ]);\n    if (!found)\n        throw Error(\"no such type: \" + path);\n    return found;\n};\n\n/**\n * Looks up the values of the {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Enum} Looked up enum\n * @throws {Error} If `path` does not point to an enum\n */\nNamespace.prototype.lookupEnum = function lookupEnum(path) {\n    var found = this.lookup(path, [ Enum ]);\n    if (!found)\n        throw Error(\"no such Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Type|type} or {@link Enum|enum} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Type} Looked up type or enum\n * @throws {Error} If `path` does not point to a type or enum\n */\nNamespace.prototype.lookupTypeOrEnum = function lookupTypeOrEnum(path) {\n    var found = this.lookup(path, [ Type, Enum ]);\n    if (!found)\n        throw Error(\"no such Type or Enum '\" + path + \"' in \" + this);\n    return found;\n};\n\n/**\n * Looks up the {@link Service|service} at the specified path, relative to this namespace.\n * Besides its signature, this methods differs from {@link Namespace#lookup|lookup} in that it throws instead of returning `null`.\n * @param {string|string[]} path Path to look up\n * @returns {Service} Looked up service\n * @throws {Error} If `path` does not point to a service\n */\nNamespace.prototype.lookupService = function lookupService(path) {\n    var found = this.lookup(path, [ Service ]);\n    if (!found)\n        throw Error(\"no such Service '\" + path + \"' in \" + this);\n    return found;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nNamespace._configure = function(Type_, Service_, Enum_) {\n    Type    = Type_;\n    Service = Service_;\n    Enum    = Enum_;\n};\n", "\"use strict\";\nmodule.exports = ReflectionObject;\n\nReflectionObject.className = \"ReflectionObject\";\n\nvar util = require(37);\n\nvar Root; // cyclic\n\n/**\n * Constructs a new reflection object instance.\n * @classdesc Base class of all reflection objects.\n * @constructor\n * @param {string} name Object name\n * @param {Object.<string,*>} [options] Declared options\n * @abstract\n */\nfunction ReflectionObject(name, options) {\n\n    if (!util.isString(name))\n        throw TypeError(\"name must be a string\");\n\n    if (options && !util.isObject(options))\n        throw TypeError(\"options must be an object\");\n\n    /**\n     * Options.\n     * @type {Object.<string,*>|undefined}\n     */\n    this.options = options; // toJSON\n\n    /**\n     * Unique name within its namespace.\n     * @type {string}\n     */\n    this.name = name;\n\n    /**\n     * Parent namespace.\n     * @type {Namespace|null}\n     */\n    this.parent = null;\n\n    /**\n     * Whether already resolved or not.\n     * @type {boolean}\n     */\n    this.resolved = false;\n\n    /**\n     * Comment text, if any.\n     * @type {string|null}\n     */\n    this.comment = null;\n\n    /**\n     * Defining file name.\n     * @type {string|null}\n     */\n    this.filename = null;\n}\n\nObject.defineProperties(ReflectionObject.prototype, {\n\n    /**\n     * Reference to the root namespace.\n     * @name ReflectionObject#root\n     * @type {Root}\n     * @readonly\n     */\n    root: {\n        get: function() {\n            var ptr = this;\n            while (ptr.parent !== null)\n                ptr = ptr.parent;\n            return ptr;\n        }\n    },\n\n    /**\n     * Full name including leading dot.\n     * @name ReflectionObject#fullName\n     * @type {string}\n     * @readonly\n     */\n    fullName: {\n        get: function() {\n            var path = [ this.name ],\n                ptr = this.parent;\n            while (ptr) {\n                path.unshift(ptr.name);\n                ptr = ptr.parent;\n            }\n            return path.join(\".\");\n        }\n    }\n});\n\n/**\n * Converts this reflection object to its descriptor representation.\n * @returns {Object.<string,*>} Descriptor\n * @abstract\n */\nReflectionObject.prototype.toJSON = /* istanbul ignore next */ function toJSON() {\n    throw Error(); // not implemented, shouldn't happen\n};\n\n/**\n * Called when this object is added to a parent.\n * @param {ReflectionObject} parent Parent added to\n * @returns {undefined}\n */\nReflectionObject.prototype.onAdd = function onAdd(parent) {\n    if (this.parent && this.parent !== parent)\n        this.parent.remove(this);\n    this.parent = parent;\n    this.resolved = false;\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleAdd(this);\n};\n\n/**\n * Called when this object is removed from a parent.\n * @param {ReflectionObject} parent Parent removed from\n * @returns {undefined}\n */\nReflectionObject.prototype.onRemove = function onRemove(parent) {\n    var root = parent.root;\n    if (root instanceof Root)\n        root._handleRemove(this);\n    this.parent = null;\n    this.resolved = false;\n};\n\n/**\n * Resolves this objects type references.\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.resolve = function resolve() {\n    if (this.resolved)\n        return this;\n    if (this.root instanceof Root)\n        this.resolved = true; // only if part of a root\n    return this;\n};\n\n/**\n * Gets an option value.\n * @param {string} name Option name\n * @returns {*} Option value or `undefined` if not set\n */\nReflectionObject.prototype.getOption = function getOption(name) {\n    if (this.options)\n        return this.options[name];\n    return undefined;\n};\n\n/**\n * Sets an option.\n * @param {string} name Option name\n * @param {*} value Option value\n * @param {boolean} [ifNotSet] Sets the option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOption = function setOption(name, value, ifNotSet) {\n    if (!ifNotSet || !this.options || this.options[name] === undefined)\n        (this.options || (this.options = {}))[name] = value;\n    return this;\n};\n\n/**\n * Sets multiple options.\n * @param {Object.<string,*>} options Options to set\n * @param {boolean} [ifNotSet] Sets an option only if it isn't currently set\n * @returns {ReflectionObject} `this`\n */\nReflectionObject.prototype.setOptions = function setOptions(options, ifNotSet) {\n    if (options)\n        for (var keys = Object.keys(options), i = 0; i < keys.length; ++i)\n            this.setOption(keys[i], options[keys[i]], ifNotSet);\n    return this;\n};\n\n/**\n * Converts this instance to its string representation.\n * @returns {string} Class name[, space, full name]\n */\nReflectionObject.prototype.toString = function toString() {\n    var className = this.constructor.className,\n        fullName  = this.fullName;\n    if (fullName.length)\n        return className + \" \" + fullName;\n    return className;\n};\n\n// Sets up cyclic dependencies (called in index-light)\nReflectionObject._configure = function(Root_) {\n    Root = Root_;\n};\n", "\"use strict\";\nmodule.exports = OneOf;\n\n// extends ReflectionObject\nvar ReflectionObject = require(24);\n((OneOf.prototype = Object.create(ReflectionObject.prototype)).constructor = OneOf).className = \"OneOf\";\n\nvar Field = require(16),\n    util  = require(37);\n\n/**\n * Constructs a new oneof instance.\n * @classdesc Reflected oneof.\n * @extends ReflectionObject\n * @constructor\n * @param {string} name Oneof name\n * @param {string[]|Object.<string,*>} [fieldNames] Field names\n * @param {Object.<string,*>} [options] Declared options\n * @param {string} [comment] Comment associated with this field\n */\nfunction OneOf(name, fieldNames, options, comment) {\n    if (!Array.isArray(fieldNames)) {\n        options = fieldNames;\n        fieldNames = undefined;\n    }\n    ReflectionObject.call(this, name, options);\n\n    /* istanbul ignore if */\n    if (!(fieldNames === undefined || Array.isArray(fieldNames)))\n        throw TypeError(\"fieldNames must be an Array\");\n\n    /**\n     * Field names that belong to this oneof.\n     * @type {string[]}\n     */\n    this.oneof = fieldNames || []; // toJSON, marker\n\n    /**\n     * Fields that belong to this oneof as an array for iteration.\n     * @type {Field[]}\n     * @readonly\n     */\n    this.fieldsArray = []; // declared readonly for conformance, possibly not yet added to parent\n\n    /**\n     * Comment for this field.\n     * @type {string|null}\n     */\n    this.comment = comment;\n}\n\n/**\n * Oneof descriptor.\n * @interface IOneOf\n * @property {Array.<string>} oneof Oneof field names\n * @property {Object.<string,*>} [options] Oneof options\n */\n\n/**\n * Constructs a oneof from a oneof descriptor.\n * @param {string} name Oneof name\n * @param {IOneOf} json Oneof descriptor\n * @returns {OneOf} Created oneof\n * @throws {TypeError} If arguments are invalid\n */\nOneOf.fromJSON = function fromJSON(name, json) {\n    return new OneOf(name, json.oneof, json.options, json.comment);\n};\n\n/**\n * Converts this oneof to a oneof descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IOneOf} Oneof descriptor\n */\nOneOf.prototype.toJSON = function toJSON(toJSONOptions) {\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , this.options,\n        \"oneof\"   , this.oneof,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Adds the fields of the specified oneof to the parent if not already done so.\n * @param {OneOf} oneof The oneof\n * @returns {undefined}\n * @inner\n * @ignore\n */\nfunction addFieldsToParent(oneof) {\n    if (oneof.parent)\n        for (var i = 0; i < oneof.fieldsArray.length; ++i)\n            if (!oneof.fieldsArray[i].parent)\n                oneof.parent.add(oneof.fieldsArray[i]);\n}\n\n/**\n * Adds a field to this oneof and removes it from its current parent, if any.\n * @param {Field} field Field to add\n * @returns {OneOf} `this`\n */\nOneOf.prototype.add = function add(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    if (field.parent && field.parent !== this.parent)\n        field.parent.remove(field);\n    this.oneof.push(field.name);\n    this.fieldsArray.push(field);\n    field.partOf = this; // field.parent remains null\n    addFieldsToParent(this);\n    return this;\n};\n\n/**\n * Removes a field from this oneof and puts it back to the oneof's parent.\n * @param {Field} field Field to remove\n * @returns {OneOf} `this`\n */\nOneOf.prototype.remove = function remove(field) {\n\n    /* istanbul ignore if */\n    if (!(field instanceof Field))\n        throw TypeError(\"field must be a Field\");\n\n    var index = this.fieldsArray.indexOf(field);\n\n    /* istanbul ignore if */\n    if (index < 0)\n        throw Error(field + \" is not a member of \" + this);\n\n    this.fieldsArray.splice(index, 1);\n    index = this.oneof.indexOf(field.name);\n\n    /* istanbul ignore else */\n    if (index > -1) // theoretical\n        this.oneof.splice(index, 1);\n\n    field.partOf = null;\n    return this;\n};\n\n/**\n * @override\n */\nOneOf.prototype.onAdd = function onAdd(parent) {\n    ReflectionObject.prototype.onAdd.call(this, parent);\n    var self = this;\n    // Collect present fields\n    for (var i = 0; i < this.oneof.length; ++i) {\n        var field = parent.get(this.oneof[i]);\n        if (field && !field.partOf) {\n            field.partOf = self;\n            self.fieldsArray.push(field);\n        }\n    }\n    // Add not yet present fields\n    addFieldsToParent(this);\n};\n\n/**\n * @override\n */\nOneOf.prototype.onRemove = function onRemove(parent) {\n    for (var i = 0, field; i < this.fieldsArray.length; ++i)\n        if ((field = this.fieldsArray[i]).parent)\n            field.parent.remove(field);\n    ReflectionObject.prototype.onRemove.call(this, parent);\n};\n\n/**\n * Decorator function as returned by {@link OneOf.d} (TypeScript).\n * @typedef OneOfDecorator\n * @type {function}\n * @param {Object} prototype Target prototype\n * @param {string} oneofName OneOf name\n * @returns {undefined}\n */\n\n/**\n * OneOf decorator (TypeScript).\n * @function\n * @param {...string} fieldNames Field names\n * @returns {OneOfDecorator} Decorator function\n * @template T extends string\n */\nOneOf.d = function decorateOneOf() {\n    var fieldNames = new Array(arguments.length),\n        index = 0;\n    while (index < arguments.length)\n        fieldNames[index] = arguments[index++];\n    return function oneOfDecorator(prototype, oneofName) {\n        util.decorateType(prototype.constructor)\n            .add(new OneOf(oneofName, fieldNames));\n        Object.defineProperty(prototype, oneofName, {\n            get: util.oneOfGetter(fieldNames),\n            set: util.oneOfSetter(fieldNames)\n        });\n    };\n};\n", "\"use strict\";\nmodule.exports = parse;\n\nparse.filename = null;\nparse.defaults = { keepCase: false };\n\nvar tokenize  = require(34),\n    Root      = require(29),\n    Type      = require(35),\n    Field     = require(16),\n    MapField  = require(20),\n    OneOf     = require(25),\n    Enum      = require(15),\n    Service   = require(33),\n    Method    = require(22),\n    types     = require(36),\n    util      = require(37);\n\nvar base10Re    = /^[1-9][0-9]*$/,\n    base10NegRe = /^-?[1-9][0-9]*$/,\n    base16Re    = /^0[x][0-9a-fA-F]+$/,\n    base16NegRe = /^-?0[x][0-9a-fA-F]+$/,\n    base8Re     = /^0[0-7]+$/,\n    base8NegRe  = /^-?0[0-7]+$/,\n    numberRe    = /^(?![eE])[0-9]*(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,\n    nameRe      = /^[a-zA-Z_][a-zA-Z_0-9]*$/,\n    typeRefRe   = /^(?:\\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,\n    fqTypeRefRe = /^(?:\\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;\n\n/**\n * Result object returned from {@link parse}.\n * @interface IParserResult\n * @property {string|undefined} package Package name, if declared\n * @property {string[]|undefined} imports Imports, if any\n * @property {string[]|undefined} weakImports Weak imports, if any\n * @property {string|undefined} syntax Syntax, if specified (either `\"proto2\"` or `\"proto3\"`)\n * @property {Root} root Populated root instance\n */\n\n/**\n * Options modifying the behavior of {@link parse}.\n * @interface IParseOptions\n * @property {boolean} [keepCase=false] Keeps field casing instead of converting to camel case\n * @property {boolean} [alternateCommentMode=false] Recognize double-slash comments in addition to doc-block comments.\n */\n\n/**\n * Options modifying the behavior of JSON serialization.\n * @interface IToJSONOptions\n * @property {boolean} [keepComments=false] Serializes comments.\n */\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @param {string} source Source contents\n * @param {Root} root Root to populate\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n */\nfunction parse(source, root, options) {\n    /* eslint-disable callback-return */\n    if (!(root instanceof Root)) {\n        options = root;\n        root = new Root();\n    }\n    if (!options)\n        options = parse.defaults;\n\n    var tn = tokenize(source, options.alternateCommentMode || false),\n        next = tn.next,\n        push = tn.push,\n        peek = tn.peek,\n        skip = tn.skip,\n        cmnt = tn.cmnt;\n\n    var head = true,\n        pkg,\n        imports,\n        weakImports,\n        syntax,\n        isProto3 = false;\n\n    var ptr = root;\n\n    var applyCase = options.keepCase ? function(name) { return name; } : util.camelCase;\n\n    /* istanbul ignore next */\n    function illegal(token, name, insideTryCatch) {\n        var filename = parse.filename;\n        if (!insideTryCatch)\n            parse.filename = null;\n        return Error(\"illegal \" + (name || \"token\") + \" '\" + token + \"' (\" + (filename ? filename + \", \" : \"\") + \"line \" + tn.line + \")\");\n    }\n\n    function readString() {\n        var values = [],\n            token;\n        do {\n            /* istanbul ignore if */\n            if ((token = next()) !== \"\\\"\" && token !== \"'\")\n                throw illegal(token);\n\n            values.push(next());\n            skip(token);\n            token = peek();\n        } while (token === \"\\\"\" || token === \"'\");\n        return values.join(\"\");\n    }\n\n    function readValue(acceptTypeRef) {\n        var token = next();\n        switch (token) {\n            case \"'\":\n            case \"\\\"\":\n                push(token);\n                return readString();\n            case \"true\": case \"TRUE\":\n                return true;\n            case \"false\": case \"FALSE\":\n                return false;\n        }\n        try {\n            return parseNumber(token, /* insideTryCatch */ true);\n        } catch (e) {\n\n            /* istanbul ignore else */\n            if (acceptTypeRef && typeRefRe.test(token))\n                return token;\n\n            /* istanbul ignore next */\n            throw illegal(token, \"value\");\n        }\n    }\n\n    function readRanges(target, acceptStrings) {\n        var token, start;\n        do {\n            if (acceptStrings && ((token = peek()) === \"\\\"\" || token === \"'\"))\n                target.push(readString());\n            else\n                target.push([ start = parseId(next()), skip(\"to\", true) ? parseId(next()) : start ]);\n        } while (skip(\",\", true));\n        skip(\";\");\n    }\n\n    function parseNumber(token, insideTryCatch) {\n        var sign = 1;\n        if (token.charAt(0) === \"-\") {\n            sign = -1;\n            token = token.substring(1);\n        }\n        switch (token) {\n            case \"inf\": case \"INF\": case \"Inf\":\n                return sign * Infinity;\n            case \"nan\": case \"NAN\": case \"Nan\": case \"NaN\":\n                return NaN;\n            case \"0\":\n                return 0;\n        }\n        if (base10Re.test(token))\n            return sign * parseInt(token, 10);\n        if (base16Re.test(token))\n            return sign * parseInt(token, 16);\n        if (base8Re.test(token))\n            return sign * parseInt(token, 8);\n\n        /* istanbul ignore else */\n        if (numberRe.test(token))\n            return sign * parseFloat(token);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"number\", insideTryCatch);\n    }\n\n    function parseId(token, acceptNegative) {\n        switch (token) {\n            case \"max\": case \"MAX\": case \"Max\":\n                return 536870911;\n            case \"0\":\n                return 0;\n        }\n\n        /* istanbul ignore if */\n        if (!acceptNegative && token.charAt(0) === \"-\")\n            throw illegal(token, \"id\");\n\n        if (base10NegRe.test(token))\n            return parseInt(token, 10);\n        if (base16NegRe.test(token))\n            return parseInt(token, 16);\n\n        /* istanbul ignore else */\n        if (base8NegRe.test(token))\n            return parseInt(token, 8);\n\n        /* istanbul ignore next */\n        throw illegal(token, \"id\");\n    }\n\n    function parsePackage() {\n\n        /* istanbul ignore if */\n        if (pkg !== undefined)\n            throw illegal(\"package\");\n\n        pkg = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(pkg))\n            throw illegal(pkg, \"name\");\n\n        ptr = ptr.define(pkg);\n        skip(\";\");\n    }\n\n    function parseImport() {\n        var token = peek();\n        var whichImports;\n        switch (token) {\n            case \"weak\":\n                whichImports = weakImports || (weakImports = []);\n                next();\n                break;\n            case \"public\":\n                next();\n                // eslint-disable-line no-fallthrough\n            default:\n                whichImports = imports || (imports = []);\n                break;\n        }\n        token = readString();\n        skip(\";\");\n        whichImports.push(token);\n    }\n\n    function parseSyntax() {\n        skip(\"=\");\n        syntax = readString();\n        isProto3 = syntax === \"proto3\";\n\n        /* istanbul ignore if */\n        if (!isProto3 && syntax !== \"proto2\")\n            throw illegal(syntax, \"syntax\");\n\n        skip(\";\");\n    }\n\n    function parseCommon(parent, token) {\n        switch (token) {\n\n            case \"option\":\n                parseOption(parent, token);\n                skip(\";\");\n                return true;\n\n            case \"message\":\n                parseType(parent, token);\n                return true;\n\n            case \"enum\":\n                parseEnum(parent, token);\n                return true;\n\n            case \"service\":\n                parseService(parent, token);\n                return true;\n\n            case \"extend\":\n                parseExtension(parent, token);\n                return true;\n        }\n        return false;\n    }\n\n    function ifBlock(obj, fnIf, fnElse) {\n        var trailingLine = tn.line;\n        if (obj) {\n            if(typeof obj.comment !== \"string\") {\n              obj.comment = cmnt(); // try block-type comment\n            }\n            obj.filename = parse.filename;\n        }\n        if (skip(\"{\", true)) {\n            var token;\n            while ((token = next()) !== \"}\")\n                fnIf(token);\n            skip(\";\", true);\n        } else {\n            if (fnElse)\n                fnElse();\n            skip(\";\");\n            if (obj && typeof obj.comment !== \"string\")\n                obj.comment = cmnt(trailingLine); // try line-type comment if no block\n        }\n    }\n\n    function parseType(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"type name\");\n\n        var type = new Type(token);\n        ifBlock(type, function parseType_block(token) {\n            if (parseCommon(type, token))\n                return;\n\n            switch (token) {\n\n                case \"map\":\n                    parseMapField(type, token);\n                    break;\n\n                case \"required\":\n                case \"optional\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                case \"oneof\":\n                    parseOneOf(type, token);\n                    break;\n\n                case \"extensions\":\n                    readRanges(type.extensions || (type.extensions = []));\n                    break;\n\n                case \"reserved\":\n                    readRanges(type.reserved || (type.reserved = []), true);\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (!isProto3 || !typeRefRe.test(token))\n                        throw illegal(token);\n\n                    push(token);\n                    parseField(type, \"optional\");\n                    break;\n            }\n        });\n        parent.add(type);\n    }\n\n    function parseField(parent, rule, extend) {\n        var type = next();\n        if (type === \"group\") {\n            parseGroup(parent, rule);\n            return;\n        }\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(type))\n            throw illegal(type, \"type\");\n\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        name = applyCase(name);\n        skip(\"=\");\n\n        var field = new Field(name, parseId(next()), type, rule, extend);\n        ifBlock(field, function parseField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseField_line() {\n            parseInlineOptions(field);\n        });\n        parent.add(field);\n\n        // JSON defaults to packed=true if not set so we have to set packed=false explicity when\n        // parsing proto2 descriptors without the option, where applicable. This must be done for\n        // all known packable types and anything that could be an enum (= is not a basic type).\n        if (!isProto3 && field.repeated && (types.packed[type] !== undefined || types.basic[type] === undefined))\n            field.setOption(\"packed\", false, /* ifNotSet */ true);\n    }\n\n    function parseGroup(parent, rule) {\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        var fieldName = util.lcFirst(name);\n        if (name === fieldName)\n            name = util.ucFirst(name);\n        skip(\"=\");\n        var id = parseId(next());\n        var type = new Type(name);\n        type.group = true;\n        var field = new Field(fieldName, id, name, rule);\n        field.filename = parse.filename;\n        ifBlock(type, function parseGroup_block(token) {\n            switch (token) {\n\n                case \"option\":\n                    parseOption(type, token);\n                    skip(\";\");\n                    break;\n\n                case \"required\":\n                case \"optional\":\n                case \"repeated\":\n                    parseField(type, token);\n                    break;\n\n                /* istanbul ignore next */\n                default:\n                    throw illegal(token); // there are no groups with proto3 semantics\n            }\n        });\n        parent.add(type)\n              .add(field);\n    }\n\n    function parseMapField(parent) {\n        skip(\"<\");\n        var keyType = next();\n\n        /* istanbul ignore if */\n        if (types.mapKey[keyType] === undefined)\n            throw illegal(keyType, \"type\");\n\n        skip(\",\");\n        var valueType = next();\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(valueType))\n            throw illegal(valueType, \"type\");\n\n        skip(\">\");\n        var name = next();\n\n        /* istanbul ignore if */\n        if (!nameRe.test(name))\n            throw illegal(name, \"name\");\n\n        skip(\"=\");\n        var field = new MapField(applyCase(name), parseId(next()), keyType, valueType);\n        ifBlock(field, function parseMapField_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(field, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseMapField_line() {\n            parseInlineOptions(field);\n        });\n        parent.add(field);\n    }\n\n    function parseOneOf(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var oneof = new OneOf(applyCase(token));\n        ifBlock(oneof, function parseOneOf_block(token) {\n            if (token === \"option\") {\n                parseOption(oneof, token);\n                skip(\";\");\n            } else {\n                push(token);\n                parseField(oneof, \"optional\");\n            }\n        });\n        parent.add(oneof);\n    }\n\n    function parseEnum(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var enm = new Enum(token);\n        ifBlock(enm, function parseEnum_block(token) {\n          switch(token) {\n            case \"option\":\n              parseOption(enm, token);\n              skip(\";\");\n              break;\n\n            case \"reserved\":\n              readRanges(enm.reserved || (enm.reserved = []), true);\n              break;\n\n            default:\n              parseEnumValue(enm, token);\n          }\n        });\n        parent.add(enm);\n    }\n\n    function parseEnumValue(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token))\n            throw illegal(token, \"name\");\n\n        skip(\"=\");\n        var value = parseId(next(), true),\n            dummy = {};\n        ifBlock(dummy, function parseEnumValue_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(dummy, token); // skip\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        }, function parseEnumValue_line() {\n            parseInlineOptions(dummy); // skip\n        });\n        parent.add(token, value, dummy.comment);\n    }\n\n    function parseOption(parent, token) {\n        var isCustom = skip(\"(\", true);\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token;\n        if (isCustom) {\n            skip(\")\");\n            name = \"(\" + name + \")\";\n            token = peek();\n            if (fqTypeRefRe.test(token)) {\n                name += token;\n                next();\n            }\n        }\n        skip(\"=\");\n        parseOptionValue(parent, name);\n    }\n\n    function parseOptionValue(parent, name) {\n        if (skip(\"{\", true)) { // { a: \"foo\" b { c: \"bar\" } }\n            do {\n                /* istanbul ignore if */\n                if (!nameRe.test(token = next()))\n                    throw illegal(token, \"name\");\n\n                if (peek() === \"{\")\n                    parseOptionValue(parent, name + \".\" + token);\n                else {\n                    skip(\":\");\n                    if (peek() === \"{\")\n                        parseOptionValue(parent, name + \".\" + token);\n                    else\n                        setOption(parent, name + \".\" + token, readValue(true));\n                }\n                skip(\",\", true);\n            } while (!skip(\"}\", true));\n        } else\n            setOption(parent, name, readValue(true));\n        // Does not enforce a delimiter to be universal\n    }\n\n    function setOption(parent, name, value) {\n        if (parent.setOption)\n            parent.setOption(name, value);\n    }\n\n    function parseInlineOptions(parent) {\n        if (skip(\"[\", true)) {\n            do {\n                parseOption(parent, \"option\");\n            } while (skip(\",\", true));\n            skip(\"]\");\n        }\n        return parent;\n    }\n\n    function parseService(parent, token) {\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"service name\");\n\n        var service = new Service(token);\n        ifBlock(service, function parseService_block(token) {\n            if (parseCommon(service, token))\n                return;\n\n            /* istanbul ignore else */\n            if (token === \"rpc\")\n                parseMethod(service, token);\n            else\n                throw illegal(token);\n        });\n        parent.add(service);\n    }\n\n    function parseMethod(parent, token) {\n        // Get the comment of the preceding line now (if one exists) in case the\n        // method is defined across multiple lines.\n        var commentText = cmnt();\n\n        var type = token;\n\n        /* istanbul ignore if */\n        if (!nameRe.test(token = next()))\n            throw illegal(token, \"name\");\n\n        var name = token,\n            requestType, requestStream,\n            responseType, responseStream;\n\n        skip(\"(\");\n        if (skip(\"stream\", true))\n            requestStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        requestType = token;\n        skip(\")\"); skip(\"returns\"); skip(\"(\");\n        if (skip(\"stream\", true))\n            responseStream = true;\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token);\n\n        responseType = token;\n        skip(\")\");\n\n        var method = new Method(name, type, requestType, responseType, requestStream, responseStream);\n        method.comment = commentText;\n        ifBlock(method, function parseMethod_block(token) {\n\n            /* istanbul ignore else */\n            if (token === \"option\") {\n                parseOption(method, token);\n                skip(\";\");\n            } else\n                throw illegal(token);\n\n        });\n        parent.add(method);\n    }\n\n    function parseExtension(parent, token) {\n\n        /* istanbul ignore if */\n        if (!typeRefRe.test(token = next()))\n            throw illegal(token, \"reference\");\n\n        var reference = token;\n        ifBlock(null, function parseExtension_block(token) {\n            switch (token) {\n\n                case \"required\":\n                case \"repeated\":\n                case \"optional\":\n                    parseField(parent, token, reference);\n                    break;\n\n                default:\n                    /* istanbul ignore if */\n                    if (!isProto3 || !typeRefRe.test(token))\n                        throw illegal(token);\n                    push(token);\n                    parseField(parent, \"optional\", reference);\n                    break;\n            }\n        });\n    }\n\n    var token;\n    while ((token = next()) !== null) {\n        switch (token) {\n\n            case \"package\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parsePackage();\n                break;\n\n            case \"import\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseImport();\n                break;\n\n            case \"syntax\":\n\n                /* istanbul ignore if */\n                if (!head)\n                    throw illegal(token);\n\n                parseSyntax();\n                break;\n\n            case \"option\":\n\n                parseOption(ptr, token);\n                skip(\";\");\n                break;\n\n            default:\n\n                /* istanbul ignore else */\n                if (parseCommon(ptr, token)) {\n                    head = false;\n                    continue;\n                }\n\n                /* istanbul ignore next */\n                throw illegal(token);\n        }\n    }\n\n    parse.filename = null;\n    return {\n        \"package\"     : pkg,\n        \"imports\"     : imports,\n         weakImports  : weakImports,\n         syntax       : syntax,\n         root         : root\n    };\n}\n\n/**\n * Parses the given .proto source and returns an object with the parsed contents.\n * @name parse\n * @function\n * @param {string} source Source contents\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {IParserResult} Parser result\n * @property {string} filename=null Currently processing file name for error reporting, if known\n * @property {IParseOptions} defaults Default {@link IParseOptions}\n * @variation 2\n */\n", "\"use strict\";\nmodule.exports = Reader;\n\nvar util      = require(39);\n\nvar BufferReader; // cyclic\n\nvar LongBits  = util.LongBits,\n    utf8      = util.utf8;\n\n/* istanbul ignore next */\nfunction indexOutOfRange(reader, writeLength) {\n    return RangeError(\"index out of range: \" + reader.pos + \" + \" + (writeLength || 1) + \" > \" + reader.len);\n}\n\n/**\n * Constructs a new reader instance using the specified buffer.\n * @classdesc Wire format reader using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n * @param {Uint8Array} buffer Buffer to read from\n */\nfunction Reader(buffer) {\n\n    /**\n     * Read buffer.\n     * @type {Uint8Array}\n     */\n    this.buf = buffer;\n\n    /**\n     * Read buffer position.\n     * @type {number}\n     */\n    this.pos = 0;\n\n    /**\n     * Read buffer length.\n     * @type {number}\n     */\n    this.len = buffer.length;\n}\n\nvar create_array = typeof Uint8Array !== \"undefined\"\n    ? function create_typed_array(buffer) {\n        if (buffer instanceof Uint8Array || Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    }\n    /* istanbul ignore next */\n    : function create_array(buffer) {\n        if (Array.isArray(buffer))\n            return new Reader(buffer);\n        throw Error(\"illegal buffer\");\n    };\n\n/**\n * Creates a new reader using the specified buffer.\n * @function\n * @param {Uint8Array|Buffer} buffer Buffer to read from\n * @returns {Reader|BufferReader} A {@link BufferReader} if `buffer` is a Buffer, otherwise a {@link Reader}\n * @throws {Error} If `buffer` is not a valid buffer\n */\nReader.create = util.Buffer\n    ? function create_buffer_setup(buffer) {\n        return (Reader.create = function create_buffer(buffer) {\n            return util.Buffer.isBuffer(buffer)\n                ? new BufferReader(buffer)\n                /* istanbul ignore next */\n                : create_array(buffer);\n        })(buffer);\n    }\n    /* istanbul ignore next */\n    : create_array;\n\nReader.prototype._slice = util.Array.prototype.subarray || /* istanbul ignore next */ util.Array.prototype.slice;\n\n/**\n * Reads a varint as an unsigned 32 bit value.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.uint32 = (function read_uint32_setup() {\n    var value = 4294967295; // optimizer type-hint, tends to deopt otherwise (?!)\n    return function read_uint32() {\n        value = (         this.buf[this.pos] & 127       ) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) <<  7) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 14) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] & 127) << 21) >>> 0; if (this.buf[this.pos++] < 128) return value;\n        value = (value | (this.buf[this.pos] &  15) << 28) >>> 0; if (this.buf[this.pos++] < 128) return value;\n\n        /* istanbul ignore if */\n        if ((this.pos += 5) > this.len) {\n            this.pos = this.len;\n            throw indexOutOfRange(this, 10);\n        }\n        return value;\n    };\n})();\n\n/**\n * Reads a varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.int32 = function read_int32() {\n    return this.uint32() | 0;\n};\n\n/**\n * Reads a zig-zag encoded varint as a signed 32 bit value.\n * @returns {number} Value read\n */\nReader.prototype.sint32 = function read_sint32() {\n    var value = this.uint32();\n    return value >>> 1 ^ -(value & 1) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readLongVarint() {\n    // tends to deopt with local vars for octet etc.\n    var bits = new LongBits(0, 0);\n    var i = 0;\n    if (this.len - this.pos > 4) { // fast route (lo)\n        for (; i < 4; ++i) {\n            // 1st..4th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 5th\n        bits.lo = (bits.lo | (this.buf[this.pos] & 127) << 28) >>> 0;\n        bits.hi = (bits.hi | (this.buf[this.pos] & 127) >>  4) >>> 0;\n        if (this.buf[this.pos++] < 128)\n            return bits;\n        i = 0;\n    } else {\n        for (; i < 3; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 1st..3th\n            bits.lo = (bits.lo | (this.buf[this.pos] & 127) << i * 7) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n        // 4th\n        bits.lo = (bits.lo | (this.buf[this.pos++] & 127) << i * 7) >>> 0;\n        return bits;\n    }\n    if (this.len - this.pos > 4) { // fast route (hi)\n        for (; i < 5; ++i) {\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    } else {\n        for (; i < 5; ++i) {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n            // 6th..10th\n            bits.hi = (bits.hi | (this.buf[this.pos] & 127) << i * 7 + 3) >>> 0;\n            if (this.buf[this.pos++] < 128)\n                return bits;\n        }\n    }\n    /* istanbul ignore next */\n    throw Error(\"invalid varint encoding\");\n}\n\n/* eslint-enable no-invalid-this */\n\n/*\n * Reads a varint as a signed 64 bit value.\n * @name Reader#int64\n * @function\n * @returns {Long} Value read\n */\n\n/*\n * Reads a varint as an unsigned 64 bit value.\n * @name Reader#uint64\n * @function\n * @returns {Long} Value read\n */\n\n/*\n * Reads a zig-zag encoded varint as a signed 64 bit value.\n * @name Reader#sint64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a varint as a boolean.\n * @returns {boolean} Value read\n */\nReader.prototype.bool = function read_bool() {\n    return this.uint32() !== 0;\n};\n\nfunction readFixed32_end(buf, end) { // note that this uses `end`, not `pos`\n    return (buf[end - 4]\n          | buf[end - 3] << 8\n          | buf[end - 2] << 16\n          | buf[end - 1] << 24) >>> 0;\n}\n\n/**\n * Reads fixed 32 bits as an unsigned 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.fixed32 = function read_fixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4);\n};\n\n/**\n * Reads fixed 32 bits as a signed 32 bit integer.\n * @returns {number} Value read\n */\nReader.prototype.sfixed32 = function read_sfixed32() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    return readFixed32_end(this.buf, this.pos += 4) | 0;\n};\n\n/* eslint-disable no-invalid-this */\n\nfunction readFixed64(/* this: Reader */) {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 8);\n\n    return new LongBits(readFixed32_end(this.buf, this.pos += 4), readFixed32_end(this.buf, this.pos += 4));\n}\n\n/* eslint-enable no-invalid-this */\n\n/*\n * Reads fixed 64 bits.\n * @name Reader#fixed64\n * @function\n * @returns {Long} Value read\n */\n\n/*\n * Reads zig-zag encoded fixed 64 bits.\n * @name Reader#sfixed64\n * @function\n * @returns {Long} Value read\n */\n\n/**\n * Reads a float (32 bit) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.float = function read_float() {\n\n    /* istanbul ignore if */\n    if (this.pos + 4 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readFloatLE(this.buf, this.pos);\n    this.pos += 4;\n    return value;\n};\n\n/**\n * Reads a double (64 bit float) as a number.\n * @function\n * @returns {number} Value read\n */\nReader.prototype.double = function read_double() {\n\n    /* istanbul ignore if */\n    if (this.pos + 8 > this.len)\n        throw indexOutOfRange(this, 4);\n\n    var value = util.float.readDoubleLE(this.buf, this.pos);\n    this.pos += 8;\n    return value;\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @returns {Uint8Array} Value read\n */\nReader.prototype.bytes = function read_bytes() {\n    var length = this.uint32(),\n        start  = this.pos,\n        end    = this.pos + length;\n\n    /* istanbul ignore if */\n    if (end > this.len)\n        throw indexOutOfRange(this, length);\n\n    this.pos += length;\n    if (Array.isArray(this.buf)) // plain array\n        return this.buf.slice(start, end);\n    return start === end // fix for IE 10/Win8 and others' subarray returning array of size 1\n        ? new this.buf.constructor(0)\n        : this._slice.call(this.buf, start, end);\n};\n\n/**\n * Reads a string preceeded by its byte length as a varint.\n * @returns {string} Value read\n */\nReader.prototype.string = function read_string() {\n    var bytes = this.bytes();\n    return utf8.read(bytes, 0, bytes.length);\n};\n\n/**\n * Skips the specified number of bytes if specified, otherwise skips a varint.\n * @param {number} [length] Length if known, otherwise a varint is assumed\n * @returns {Reader} `this`\n */\nReader.prototype.skip = function skip(length) {\n    if (typeof length === \"number\") {\n        /* istanbul ignore if */\n        if (this.pos + length > this.len)\n            throw indexOutOfRange(this, length);\n        this.pos += length;\n    } else {\n        do {\n            /* istanbul ignore if */\n            if (this.pos >= this.len)\n                throw indexOutOfRange(this);\n        } while (this.buf[this.pos++] & 128);\n    }\n    return this;\n};\n\n/**\n * Skips the next element of the specified wire type.\n * @param {number} wireType Wire type received\n * @returns {Reader} `this`\n */\nReader.prototype.skipType = function(wireType) {\n    switch (wireType) {\n        case 0:\n            this.skip();\n            break;\n        case 1:\n            this.skip(8);\n            break;\n        case 2:\n            this.skip(this.uint32());\n            break;\n        case 3:\n            while ((wireType = this.uint32() & 7) !== 4) {\n                this.skipType(wireType);\n            }\n            break;\n        case 5:\n            this.skip(4);\n            break;\n\n        /* istanbul ignore next */\n        default:\n            throw Error(\"invalid wire type \" + wireType + \" at offset \" + this.pos);\n    }\n    return this;\n};\n\nReader._configure = function(BufferReader_) {\n    BufferReader = BufferReader_;\n\n    var fn = util.Long ? \"toLong\" : /* istanbul ignore next */ \"toNumber\";\n    util.merge(Reader.prototype, {\n\n        int64: function read_int64() {\n            return readLongVarint.call(this)[fn](false);\n        },\n\n        uint64: function read_uint64() {\n            return readLongVarint.call(this)[fn](true);\n        },\n\n        sint64: function read_sint64() {\n            return readLongVarint.call(this).zzDecode()[fn](false);\n        },\n\n        fixed64: function read_fixed64() {\n            return readFixed64.call(this)[fn](true);\n        },\n\n        sfixed64: function read_sfixed64() {\n            return readFixed64.call(this)[fn](false);\n        }\n\n    });\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erReader;\n\n// extends Reader\nvar Reader = require(27);\n(BufferReader.prototype = Object.create(Reader.prototype)).constructor = BufferReader;\n\nvar util = require(39);\n\n/**\n * Constructs a new buffer reader instance.\n * @classdesc Wire format reader using node buffers.\n * @extends Reader\n * @constructor\n * @param {Buffer} buffer Buffer to read from\n */\nfunction BufferReader(buffer) {\n    Reader.call(this, buffer);\n\n    /**\n     * Read buffer.\n     * @name BufferReader#buf\n     * @type {Buffer}\n     */\n}\n\n/* istanbul ignore else */\nif (util.Buffer)\n    BufferReader.prototype._slice = util.Buffer.prototype.slice;\n\n/**\n * @override\n */\nBufferReader.prototype.string = function read_string_buffer() {\n    var len = this.uint32(); // modifies pos\n    return this.buf.utf8Slice(this.pos, this.pos = Math.min(this.pos + len, this.len));\n};\n\n/**\n * Reads a sequence of bytes preceeded by its length as a varint.\n * @name BufferReader#bytes\n * @function\n * @returns {<PERSON><PERSON>er} Value read\n */\n", "\"use strict\";\nmodule.exports = Root;\n\n// extends Namespace\nvar Namespace = require(23);\n((Root.prototype = Object.create(Namespace.prototype)).constructor = Root).className = \"Root\";\n\nvar Field   = require(16),\n    Enum    = require(15),\n    OneOf   = require(25),\n    util    = require(37);\n\nvar Type,   // cyclic\n    parse,  // might be excluded\n    common; // \"\n\n/**\n * Constructs a new root namespace instance.\n * @classdesc Root namespace wrapping all types, enums, services, sub-namespaces etc. that belong together.\n * @extends NamespaceBase\n * @constructor\n * @param {Object.<string,*>} [options] Top level options\n */\nfunction Root(options) {\n    Namespace.call(this, \"\", options);\n\n    /**\n     * Deferred extension fields.\n     * @type {Field[]}\n     */\n    this.deferred = [];\n\n    /**\n     * Resolved file names of loaded files.\n     * @type {string[]}\n     */\n    this.files = [];\n}\n\n/**\n * Loads a namespace descriptor into a root namespace.\n * @param {INamespace} json Nameespace descriptor\n * @param {Root} [root] Root namespace, defaults to create a new one if omitted\n * @returns {Root} Root namespace\n */\nRoot.fromJSON = function fromJSON(json, root) {\n    if (!root)\n        root = new Root();\n    if (json.options)\n        root.setOptions(json.options);\n    return root.addJSON(json.nested);\n};\n\n/**\n * Resolves the path of an imported file, relative to the importing origin.\n * This method exists so you can override it with your own logic in case your imports are scattered over multiple directories.\n * @function\n * @param {string} origin The file name of the importing file\n * @param {string} target The file name being imported\n * @returns {string|null} Resolved path to `target` or `null` to skip the file\n */\nRoot.prototype.resolvePath = util.path.resolve;\n\n// A symbol-like function to safely signal synchronous loading\n/* istanbul ignore next */\nfunction SYNC() {} // eslint-disable-line no-empty-function\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} options Parse options\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n */\nRoot.prototype.load = function load(filename, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = undefined;\n    }\n    var self = this;\n    if (!callback)\n        return util.asPromise(load, self, filename, options);\n\n    var sync = callback === SYNC; // undocumented\n\n    // Finishes loading by calling the callback (exactly once)\n    function finish(err, root) {\n        /* istanbul ignore if */\n        if (!callback)\n            return;\n        var cb = callback;\n        callback = null;\n        if (sync)\n            throw err;\n        cb(err, root);\n    }\n\t\n    // Bundled definition existence checking\n    function getBundledFileName(filename) {\n        var idx = filename.lastIndexOf(\"google/protobuf/\");\n        if (idx > -1) {\n            var altname = filename.substring(idx);\n            if (altname in common) return altname; \n        }\n        return null;\n    }\n\n    // Processes a single file\n    function process(filename, source) {\n        try {\n            if (util.isString(source) && source.charAt(0) === \"{\")\n                source = JSON.parse(source);\n            if (!util.isString(source))\n                self.setOptions(source.options).addJSON(source.nested);\n            else {\n                parse.filename = filename;\n                var parsed = parse(source, self, options),\n                    resolved,\n                    i = 0;\n                if (parsed.imports)\n                    for (; i < parsed.imports.length; ++i)\n                        if (resolved = (getBundledFileName(parsed.imports[i]) || self.resolvePath(filename, parsed.imports[i])))\n                            fetch(resolved);\n                if (parsed.weakImports)\n                    for (i = 0; i < parsed.weakImports.length; ++i)\n                        if (resolved = (getBundledFileName(parsed.weakImports[i]) || self.resolvePath(filename, parsed.weakImports[i])))\n                            fetch(resolved, true);\n            }\n        } catch (err) {\n            finish(err);\n        }\n        if (!sync && !queued)\n            finish(null, self); // only once anyway\n    }\n\n    // Fetches a single file\n    function fetch(filename, weak) {\n\n        // Skip if already loaded / attempted\n        if (self.files.indexOf(filename) > -1)\n            return;\n        self.files.push(filename);\n\n        // Shortcut bundled definitions\n        if (filename in common) {\n            if (sync)\n                process(filename, common[filename]);\n            else {\n                ++queued;\n                setTimeout(function() {\n                    --queued;\n                    process(filename, common[filename]);\n                });\n            }\n            return;\n        }\n\n        // Otherwise fetch from disk or network\n        if (sync) {\n            var source;\n            try {\n                source = util.fs.readFileSync(filename).toString(\"utf8\");\n            } catch (err) {\n                if (!weak)\n                    finish(err);\n                return;\n            }\n            process(filename, source);\n        } else {\n            ++queued;\n            util.fetch(filename, function(err, source) {\n                --queued;\n                /* istanbul ignore if */\n                if (!callback)\n                    return; // terminated meanwhile\n                if (err) {\n                    /* istanbul ignore else */\n                    if (!weak)\n                        finish(err);\n                    else if (!queued) // can't be covered reliably\n                        finish(null, self);\n                    return;\n                }\n                process(filename, source);\n            });\n        }\n    }\n    var queued = 0;\n\n    // Assembling the root namespace doesn't require working type\n    // references anymore, so we can load everything in parallel\n    if (util.isString(filename))\n        filename = [ filename ];\n    for (var i = 0, resolved; i < filename.length; ++i)\n        if (resolved = self.resolvePath(\"\", filename[i]))\n            fetch(resolved);\n\n    if (sync)\n        return self;\n    if (!queued)\n        finish(null, self);\n    return undefined;\n};\n// function load(filename:string, options:IParseOptions, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and calls the callback.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {LoadCallback} callback Callback function\n * @returns {undefined}\n * @variation 2\n */\n// function load(filename:string, callback:LoadCallback):undefined\n\n/**\n * Loads one or multiple .proto or preprocessed .json files into this root namespace and returns a promise.\n * @function Root#load\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Promise<Root>} Promise\n * @variation 3\n */\n// function load(filename:string, [options:IParseOptions]):Promise<Root>\n\n/**\n * Synchronously loads one or multiple .proto or preprocessed .json files into this root namespace (node only).\n * @function Root#loadSync\n * @param {string|string[]} filename Names of one or multiple files to load\n * @param {IParseOptions} [options] Parse options. Defaults to {@link parse.defaults} when omitted.\n * @returns {Root} Root namespace\n * @throws {Error} If synchronous fetching is not supported (i.e. in browsers) or if a file's syntax is invalid\n */\nRoot.prototype.loadSync = function loadSync(filename, options) {\n    if (!util.isNode)\n        throw Error(\"not supported\");\n    return this.load(filename, options, SYNC);\n};\n\n/**\n * @override\n */\nRoot.prototype.resolveAll = function resolveAll() {\n    if (this.deferred.length)\n        throw Error(\"unresolvable extensions: \" + this.deferred.map(function(field) {\n            return \"'extend \" + field.extend + \"' in \" + field.parent.fullName;\n        }).join(\", \"));\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n// only uppercased (and thus conflict-free) children are exposed, see below\nvar exposeRe = /^[A-Z]/;\n\n/**\n * Handles a deferred declaring extension field by creating a sister field to represent it within its extended type.\n * @param {Root} root Root instance\n * @param {Field} field Declaring extension field witin the declaring type\n * @returns {boolean} `true` if successfully added to the extended type, `false` otherwise\n * @inner\n * @ignore\n */\nfunction tryHandleExtension(root, field) {\n    var extendedType = field.parent.lookup(field.extend);\n    if (extendedType) {\n        var sisterField = new Field(field.fullName, field.id, field.type, field.rule, undefined, field.options);\n        sisterField.declaringField = field;\n        field.extensionField = sisterField;\n        extendedType.add(sisterField);\n        return true;\n    }\n    return false;\n}\n\n/**\n * Called when any object is added to this root or its sub-namespaces.\n * @param {ReflectionObject} object Object added\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleAdd = function _handleAdd(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field (implies not part of a oneof) */ object.extend !== undefined && /* not already handled */ !object.extensionField)\n            if (!tryHandleExtension(this, object))\n                this.deferred.push(object);\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object.values; // expose enum values as property of its parent\n\n    } else if (!(object instanceof OneOf)) /* everything else is a namespace */ {\n\n        if (object instanceof Type) // Try to handle any deferred extensions\n            for (var i = 0; i < this.deferred.length;)\n                if (tryHandleExtension(this, this.deferred[i]))\n                    this.deferred.splice(i, 1);\n                else\n                    ++i;\n        for (var j = 0; j < /* initializes */ object.nestedArray.length; ++j) // recurse into the namespace\n            this._handleAdd(object._nestedArray[j]);\n        if (exposeRe.test(object.name))\n            object.parent[object.name] = object; // expose namespace as property of its parent\n    }\n\n    // The above also adds uppercased (and thus conflict-free) nested types, services and enums as\n    // properties of namespaces just like static code does. This allows using a .d.ts generated for\n    // a static module with reflection-based solutions where the condition is met.\n};\n\n/**\n * Called when any object is removed from this root or its sub-namespaces.\n * @param {ReflectionObject} object Object removed\n * @returns {undefined}\n * @private\n */\nRoot.prototype._handleRemove = function _handleRemove(object) {\n    if (object instanceof Field) {\n\n        if (/* an extension field */ object.extend !== undefined) {\n            if (/* already handled */ object.extensionField) { // remove its sister field\n                object.extensionField.parent.remove(object.extensionField);\n                object.extensionField = null;\n            } else { // cancel the extension\n                var index = this.deferred.indexOf(object);\n                /* istanbul ignore else */\n                if (index > -1)\n                    this.deferred.splice(index, 1);\n            }\n        }\n\n    } else if (object instanceof Enum) {\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose enum values\n\n    } else if (object instanceof Namespace) {\n\n        for (var i = 0; i < /* initializes */ object.nestedArray.length; ++i) // recurse into the namespace\n            this._handleRemove(object._nestedArray[i]);\n\n        if (exposeRe.test(object.name))\n            delete object.parent[object.name]; // unexpose namespaces\n\n    }\n};\n\n// Sets up cyclic dependencies (called in index-light)\nRoot._configure = function(Type_, parse_, common_) {\n    Type   = Type_;\n    parse  = parse_;\n    common = common_;\n};\n", "\"use strict\";\nmodule.exports = {};\n\n/**\n * Named roots.\n * This is where pbjs stores generated structures (the option `-r, --root` specifies a name).\n * Can also be used manually to make roots available accross modules.\n * @name roots\n * @type {Object.<string,Root>}\n * @example\n * // pbjs -r myroot -o compiled.js ...\n *\n * // in another module:\n * require(\"./compiled.js\");\n *\n * // in any subsequent module:\n * var root = protobuf.roots[\"myroot\"];\n */\n", "\"use strict\";\n\n/**\n * Streaming RPC helpers.\n * @namespace\n */\nvar rpc = exports;\n\n/**\n * RPC implementation passed to {@link Service#create} performing a service request on network level, i.e. by utilizing http requests or websockets.\n * @typedef RPCImpl\n * @type {function}\n * @param {Method|rpc.ServiceMethod<Message<{}>,Message<{}>>} method Reflected or static method being called\n * @param {Uint8Array} requestData Request data\n * @param {RPCImplCallback} callback Callback function\n * @returns {undefined}\n * @example\n * function rpcImpl(method, requestData, callback) {\n *     if (protobuf.util.lcFirst(method.name) !== \"myMethod\") // compatible with static code\n *         throw Error(\"no such method\");\n *     asynchronouslyObtainAResponse(requestData, function(err, responseData) {\n *         callback(err, responseData);\n *     });\n * }\n */\n\n/**\n * Node-style callback as used by {@link RPCImpl}.\n * @typedef RPCImplCallback\n * @type {function}\n * @param {Error|null} error Error, if any, otherwise `null`\n * @param {Uint8Array|null} [response] Response data or `null` to signal end of stream, if there hasn't been an error\n * @returns {undefined}\n */\n\nrpc.Service = require(32);\n", "\"use strict\";\nmodule.exports = Service;\n\nvar util = require(39);\n\n// Extends EventEmitter\n(Service.prototype = Object.create(util.EventEmitter.prototype)).constructor = Service;\n\n/**\n * A service method callback as used by {@link rpc.ServiceMethod|ServiceMethod}.\n *\n * Differs from {@link RPCImplCallback} in that it is an actual callback of a service method which may not return `response = null`.\n * @typedef rpc.ServiceMethodCallback\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {Error|null} error Error, if any\n * @param {TRes} [response] Response message\n * @returns {undefined}\n */\n\n/**\n * A service method part of a {@link rpc.Service} as created by {@link Service.create}.\n * @typedef rpc.ServiceMethod\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n * @type {function}\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} [callback] Node-style callback called with the error, if any, and the response message\n * @returns {Promise<Message<TRes>>} Promise if `callback` has been omitted, otherwise `undefined`\n */\n\n/**\n * Constructs a new RPC service instance.\n * @classdesc An RPC service as returned by {@link Service#create}.\n * @exports rpc.Service\n * @extends util.EventEmitter\n * @constructor\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n */\nfunction Service(rpcImpl, requestDelimited, responseDelimited) {\n\n    if (typeof rpcImpl !== \"function\")\n        throw TypeError(\"rpcImpl must be a function\");\n\n    util.EventEmitter.call(this);\n\n    /**\n     * RPC implementation. Becomes `null` once the service is ended.\n     * @type {RPCImpl|null}\n     */\n    this.rpcImpl = rpcImpl;\n\n    /**\n     * Whether requests are length-delimited.\n     * @type {boolean}\n     */\n    this.requestDelimited = Boolean(requestDelimited);\n\n    /**\n     * Whether responses are length-delimited.\n     * @type {boolean}\n     */\n    this.responseDelimited = Boolean(responseDelimited);\n}\n\n/**\n * Calls a service method through {@link rpc.Service#rpcImpl|rpcImpl}.\n * @param {Method|rpc.ServiceMethod<TReq,TRes>} method Reflected or static method\n * @param {Constructor<TReq>} requestCtor Request constructor\n * @param {Constructor<TRes>} responseCtor Response constructor\n * @param {TReq|Properties<TReq>} request Request message or plain object\n * @param {rpc.ServiceMethodCallback<TRes>} callback Service callback\n * @returns {undefined}\n * @template TReq extends Message<TReq>\n * @template TRes extends Message<TRes>\n */\nService.prototype.rpcCall = function rpcCall(method, requestCtor, responseCtor, request, callback) {\n\n    if (!request)\n        throw TypeError(\"request must be specified\");\n\n    var self = this;\n    if (!callback)\n        return util.asPromise(rpcCall, self, method, requestCtor, responseCtor, request);\n\n    if (!self.rpcImpl) {\n        setTimeout(function() { callback(Error(\"already ended\")); }, 0);\n        return undefined;\n    }\n\n    try {\n        return self.rpcImpl(\n            method,\n            requestCtor[self.requestDelimited ? \"encodeDelimited\" : \"encode\"](request).finish(),\n            function rpcCallback(err, response) {\n\n                if (err) {\n                    self.emit(\"error\", err, method);\n                    return callback(err);\n                }\n\n                if (response === null) {\n                    self.end(/* endedByRPC */ true);\n                    return undefined;\n                }\n\n                if (!(response instanceof responseCtor)) {\n                    try {\n                        response = responseCtor[self.responseDelimited ? \"decodeDelimited\" : \"decode\"](response);\n                    } catch (err) {\n                        self.emit(\"error\", err, method);\n                        return callback(err);\n                    }\n                }\n\n                self.emit(\"data\", response, method);\n                return callback(null, response);\n            }\n        );\n    } catch (err) {\n        self.emit(\"error\", err, method);\n        setTimeout(function() { callback(err); }, 0);\n        return undefined;\n    }\n};\n\n/**\n * Ends this service and emits the `end` event.\n * @param {boolean} [endedByRPC=false] Whether the service has been ended by the RPC implementation.\n * @returns {rpc.Service} `this`\n */\nService.prototype.end = function end(endedByRPC) {\n    if (this.rpcImpl) {\n        if (!endedByRPC) // signal end to rpcImpl\n            this.rpcImpl(null, null, null);\n        this.rpcImpl = null;\n        this.emit(\"end\").off();\n    }\n    return this;\n};\n", "\"use strict\";\nmodule.exports = Service;\n\n// extends Namespace\nvar Namespace = require(23);\n((Service.prototype = Object.create(Namespace.prototype)).constructor = Service).className = \"Service\";\n\nvar Method = require(22),\n    util   = require(37),\n    rpc    = require(31);\n\n/**\n * Constructs a new service instance.\n * @classdesc Reflected service.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Service name\n * @param {Object.<string,*>} [options] Service options\n * @throws {TypeError} If arguments are invalid\n */\nfunction Service(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Service methods.\n     * @type {Object.<string,Method>}\n     */\n    this.methods = {}; // toJSON, marker\n\n    /**\n     * Cached methods as an array.\n     * @type {Method[]|null}\n     * @private\n     */\n    this._methodsArray = null;\n}\n\n/**\n * Service descriptor.\n * @interface IService\n * @extends INamespace\n * @property {Object.<string,IMethod>} methods Method descriptors\n */\n\n/**\n * Constructs a service from a service descriptor.\n * @param {string} name Service name\n * @param {IService} json Service descriptor\n * @returns {Service} Created service\n * @throws {TypeError} If arguments are invalid\n */\nService.fromJSON = function fromJSON(name, json) {\n    var service = new Service(name, json.options);\n    /* istanbul ignore else */\n    if (json.methods)\n        for (var names = Object.keys(json.methods), i = 0; i < names.length; ++i)\n            service.add(Method.fromJSON(names[i], json.methods[names[i]]));\n    if (json.nested)\n        service.addJSON(json.nested);\n    service.comment = json.comment;\n    return service;\n};\n\n/**\n * Converts this service to a service descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IService} Service descriptor\n */\nService.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\" , inherited && inherited.options || undefined,\n        \"methods\" , Namespace.arrayToJSON(this.methodsArray, toJSONOptions) || /* istanbul ignore next */ {},\n        \"nested\"  , inherited && inherited.nested || undefined,\n        \"comment\" , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * Methods of this service as an array for iteration.\n * @name Service#methodsArray\n * @type {Method[]}\n * @readonly\n */\nObject.defineProperty(Service.prototype, \"methodsArray\", {\n    get: function() {\n        return this._methodsArray || (this._methodsArray = util.toArray(this.methods));\n    }\n});\n\nfunction clearCache(service) {\n    service._methodsArray = null;\n    return service;\n}\n\n/**\n * @override\n */\nService.prototype.get = function get(name) {\n    return this.methods[name]\n        || Namespace.prototype.get.call(this, name);\n};\n\n/**\n * @override\n */\nService.prototype.resolveAll = function resolveAll() {\n    var methods = this.methodsArray;\n    for (var i = 0; i < methods.length; ++i)\n        methods[i].resolve();\n    return Namespace.prototype.resolve.call(this);\n};\n\n/**\n * @override\n */\nService.prototype.add = function add(object) {\n\n    /* istanbul ignore if */\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Method) {\n        this.methods[object.name] = object;\n        object.parent = this;\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * @override\n */\nService.prototype.remove = function remove(object) {\n    if (object instanceof Method) {\n\n        /* istanbul ignore if */\n        if (this.methods[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.methods[object.name];\n        object.parent = null;\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Creates a runtime service using the specified rpc implementation.\n * @param {RPCImpl} rpcImpl RPC implementation\n * @param {boolean} [requestDelimited=false] Whether requests are length-delimited\n * @param {boolean} [responseDelimited=false] Whether responses are length-delimited\n * @returns {rpc.Service} RPC service. Useful where requests and/or responses are streamed.\n */\nService.prototype.create = function create(rpcImpl, requestDelimited, responseDelimited) {\n    var rpcService = new rpc.Service(rpcImpl, requestDelimited, responseDelimited);\n    for (var i = 0, method; i < /* initializes */ this.methodsArray.length; ++i) {\n        var methodName = util.lcFirst((method = this._methodsArray[i]).resolve().name).replace(/[^$\\w_]/g, \"\");\n        rpcService[methodName] = util.codegen([\"r\",\"c\"], util.isReserved(methodName) ? methodName + \"_\" : methodName)(\"return this.rpcCall(m,q,s,r,c)\")({\n            m: method,\n            q: method.resolvedRequestType.ctor,\n            s: method.resolvedResponseType.ctor\n        });\n    }\n    return rpcService;\n};\n", "\"use strict\";\nmodule.exports = tokenize;\n\nvar delimRe        = /[\\s{}=;:[\\],'\"()<>]/g,\n    stringDoubleRe = /(?:\"([^\"\\\\]*(?:\\\\.[^\"\\\\]*)*)\")/g,\n    stringSingleRe = /(?:'([^'\\\\]*(?:\\\\.[^'\\\\]*)*)')/g;\n\nvar setCommentRe = /^ *[*/]+ */,\n    setCommentAltRe = /^\\s*\\*?\\/*/,\n    setCommentSplitRe = /\\n/g,\n    whitespaceRe = /\\s/,\n    unescapeRe = /\\\\(.?)/g;\n\nvar unescapeMap = {\n    \"0\": \"\\0\",\n    \"r\": \"\\r\",\n    \"n\": \"\\n\",\n    \"t\": \"\\t\"\n};\n\n/**\n * Unescapes a string.\n * @param {string} str String to unescape\n * @returns {string} Unescaped string\n * @property {Object.<string,string>} map Special characters map\n * @memberof tokenize\n */\nfunction unescape(str) {\n    return str.replace(unescapeRe, function($0, $1) {\n        switch ($1) {\n            case \"\\\\\":\n            case \"\":\n                return $1;\n            default:\n                return unescapeMap[$1] || \"\";\n        }\n    });\n}\n\ntokenize.unescape = unescape;\n\n/**\n * Gets the next token and advances.\n * @typedef TokenizerHandleNext\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Peeks for the next token.\n * @typedef TokenizerHandlePeek\n * @type {function}\n * @returns {string|null} Next token or `null` on eof\n */\n\n/**\n * Pushes a token back to the stack.\n * @typedef TokenizerHandlePush\n * @type {function}\n * @param {string} token Token\n * @returns {undefined}\n */\n\n/**\n * Skips the next token.\n * @typedef TokenizerHandleSkip\n * @type {function}\n * @param {string} expected Expected token\n * @param {boolean} [optional=false] If optional\n * @returns {boolean} Whether the token matched\n * @throws {Error} If the token didn't match and is not optional\n */\n\n/**\n * Gets the comment on the previous line or, alternatively, the line comment on the specified line.\n * @typedef TokenizerHandleCmnt\n * @type {function}\n * @param {number} [line] Line number\n * @returns {string|null} Comment text or `null` if none\n */\n\n/**\n * Handle object returned from {@link tokenize}.\n * @interface ITokenizerHandle\n * @property {TokenizerHandleNext} next Gets the next token and advances (`null` on eof)\n * @property {TokenizerHandlePeek} peek Peeks for the next token (`null` on eof)\n * @property {TokenizerHandlePush} push Pushes a token back to the stack\n * @property {TokenizerHandleSkip} skip Skips a token, returns its presence and advances or, if non-optional and not present, throws\n * @property {TokenizerHandleCmnt} cmnt Gets the comment on the previous line or the line comment on the specified line, if any\n * @property {number} line Current line number\n */\n\n/**\n * Tokenizes the given .proto source and returns an object with useful utility functions.\n * @param {string} source Source contents\n * @param {boolean} alternateCommentMode Whether we should activate alternate comment parsing mode.\n * @returns {ITokenizerHandle} Tokenizer handle\n */\nfunction tokenize(source, alternateCommentMode) {\n    /* eslint-disable callback-return */\n    source = source.toString();\n\n    var offset = 0,\n        length = source.length,\n        line = 1,\n        commentType = null,\n        commentText = null,\n        commentLine = 0,\n        commentLineEmpty = false;\n\n    var stack = [];\n\n    var stringDelim = null;\n\n    /* istanbul ignore next */\n    /**\n     * Creates an error for illegal syntax.\n     * @param {string} subject Subject\n     * @returns {Error} Error created\n     * @inner\n     */\n    function illegal(subject) {\n        return Error(\"illegal \" + subject + \" (line \" + line + \")\");\n    }\n\n    /**\n     * Reads a string till its end.\n     * @returns {string} String read\n     * @inner\n     */\n    function readString() {\n        var re = stringDelim === \"'\" ? stringSingleRe : stringDoubleRe;\n        re.lastIndex = offset - 1;\n        var match = re.exec(source);\n        if (!match)\n            throw illegal(\"string\");\n        offset = re.lastIndex;\n        push(stringDelim);\n        stringDelim = null;\n        return unescape(match[1]);\n    }\n\n    /**\n     * Gets the character at `pos` within the source.\n     * @param {number} pos Position\n     * @returns {string} Character\n     * @inner\n     */\n    function charAt(pos) {\n        return source.charAt(pos);\n    }\n\n    /**\n     * Sets the current comment text.\n     * @param {number} start Start offset\n     * @param {number} end End offset\n     * @returns {undefined}\n     * @inner\n     */\n    function setComment(start, end) {\n        commentType = source.charAt(start++);\n        commentLine = line;\n        commentLineEmpty = false;\n        var lookback;\n        if (alternateCommentMode) {\n            lookback = 2;  // alternate comment parsing: \"//\" or \"/*\"\n        } else {\n            lookback = 3;  // \"///\" or \"/**\"\n        }\n        var commentOffset = start - lookback,\n            c;\n        do {\n            if (--commentOffset < 0 ||\n                    (c = source.charAt(commentOffset)) === \"\\n\") {\n                commentLineEmpty = true;\n                break;\n            }\n        } while (c === \" \" || c === \"\\t\");\n        var lines = source\n            .substring(start, end)\n            .split(setCommentSplitRe);\n        for (var i = 0; i < lines.length; ++i)\n            lines[i] = lines[i]\n                .replace(alternateCommentMode ? setCommentAltRe : setCommentRe, \"\")\n                .trim();\n        commentText = lines\n            .join(\"\\n\")\n            .trim();\n    }\n\n    function isDoubleSlashCommentLine(startOffset) {\n        var endOffset = findEndOfLine(startOffset);\n\n        // see if remaining line matches comment pattern\n        var lineText = source.substring(startOffset, endOffset);\n        // look for 1 or 2 slashes since startOffset would already point past\n        // the first slash that started the comment.\n        var isComment = /^\\s*\\/{1,2}/.test(lineText);\n        return isComment;\n    }\n\n    function findEndOfLine(cursor) {\n        // find end of cursor's line\n        var endOffset = cursor;\n        while (endOffset < length && charAt(endOffset) !== \"\\n\") {\n            endOffset++;\n        }\n        return endOffset;\n    }\n\n    /**\n     * Obtains the next token.\n     * @returns {string|null} Next token or `null` on eof\n     * @inner\n     */\n    function next() {\n        if (stack.length > 0)\n            return stack.shift();\n        if (stringDelim)\n            return readString();\n        var repeat,\n            prev,\n            curr,\n            start,\n            isDoc;\n        do {\n            if (offset === length)\n                return null;\n            repeat = false;\n            while (whitespaceRe.test(curr = charAt(offset))) {\n                if (curr === \"\\n\")\n                    ++line;\n                if (++offset === length)\n                    return null;\n            }\n\n            if (charAt(offset) === \"/\") {\n                if (++offset === length) {\n                    throw illegal(\"comment\");\n                }\n                if (charAt(offset) === \"/\") { // Line\n                    if (!alternateCommentMode) {\n                        // check for triple-slash comment\n                        isDoc = charAt(start = offset + 1) === \"/\";\n\n                        while (charAt(++offset) !== \"\\n\") {\n                            if (offset === length) {\n                                return null;\n                            }\n                        }\n                        ++offset;\n                        if (isDoc) {\n                            setComment(start, offset - 1);\n                        }\n                        ++line;\n                        repeat = true;\n                    } else {\n                        // check for double-slash comments, consolidating consecutive lines\n                        start = offset;\n                        isDoc = false;\n                        if (isDoubleSlashCommentLine(offset)) {\n                            isDoc = true;\n                            do {\n                                offset = findEndOfLine(offset);\n                                if (offset === length) {\n                                    break;\n                                }\n                                offset++;\n                            } while (isDoubleSlashCommentLine(offset));\n                        } else {\n                            offset = Math.min(length, findEndOfLine(offset) + 1);\n                        }\n                        if (isDoc) {\n                            setComment(start, offset);\n                        }\n                        line++;\n                        repeat = true;\n                    }\n                } else if ((curr = charAt(offset)) === \"*\") { /* Block */\n                    // check for /** (regular comment mode) or /* (alternate comment mode)\n                    start = offset + 1;\n                    isDoc = alternateCommentMode || charAt(start) === \"*\";\n                    do {\n                        if (curr === \"\\n\") {\n                            ++line;\n                        }\n                        if (++offset === length) {\n                            throw illegal(\"comment\");\n                        }\n                        prev = curr;\n                        curr = charAt(offset);\n                    } while (prev !== \"*\" || curr !== \"/\");\n                    ++offset;\n                    if (isDoc) {\n                        setComment(start, offset - 2);\n                    }\n                    repeat = true;\n                } else {\n                    return \"/\";\n                }\n            }\n        } while (repeat);\n\n        // offset !== length if we got here\n\n        var end = offset;\n        delimRe.lastIndex = 0;\n        var delim = delimRe.test(charAt(end++));\n        if (!delim)\n            while (end < length && !delimRe.test(charAt(end)))\n                ++end;\n        var token = source.substring(offset, offset = end);\n        if (token === \"\\\"\" || token === \"'\")\n            stringDelim = token;\n        return token;\n    }\n\n    /**\n     * Pushes a token back to the stack.\n     * @param {string} token Token\n     * @returns {undefined}\n     * @inner\n     */\n    function push(token) {\n        stack.push(token);\n    }\n\n    /**\n     * Peeks for the next token.\n     * @returns {string|null} Token or `null` on eof\n     * @inner\n     */\n    function peek() {\n        if (!stack.length) {\n            var token = next();\n            if (token === null)\n                return null;\n            push(token);\n        }\n        return stack[0];\n    }\n\n    /**\n     * Skips a token.\n     * @param {string} expected Expected token\n     * @param {boolean} [optional=false] Whether the token is optional\n     * @returns {boolean} `true` when skipped, `false` if not\n     * @throws {Error} When a required token is not present\n     * @inner\n     */\n    function skip(expected, optional) {\n        var actual = peek(),\n            equals = actual === expected;\n        if (equals) {\n            next();\n            return true;\n        }\n        if (!optional)\n            throw illegal(\"token '\" + actual + \"', '\" + expected + \"' expected\");\n        return false;\n    }\n\n    /**\n     * Gets a comment.\n     * @param {number} [trailingLine] Line number if looking for a trailing comment\n     * @returns {string|null} Comment text\n     * @inner\n     */\n    function cmnt(trailingLine) {\n        var ret = null;\n        if (trailingLine === undefined) {\n            if (commentLine === line - 1 && (alternateCommentMode || commentType === \"*\" || commentLineEmpty)) {\n                ret = commentText;\n            }\n        } else {\n            /* istanbul ignore else */\n            if (commentLine < trailingLine) {\n                peek();\n            }\n            if (commentLine === trailingLine && !commentLineEmpty && (alternateCommentMode || commentType === \"/\")) {\n                ret = commentText;\n            }\n        }\n        return ret;\n    }\n\n    return Object.defineProperty({\n        next: next,\n        peek: peek,\n        push: push,\n        skip: skip,\n        cmnt: cmnt\n    }, \"line\", {\n        get: function() { return line; }\n    });\n    /* eslint-enable callback-return */\n}\n", "\"use strict\";\nmodule.exports = Type;\n\n// extends Namespace\nvar Namespace = require(23);\n((Type.prototype = Object.create(Namespace.prototype)).constructor = Type).className = \"Type\";\n\nvar Enum      = require(15),\n    OneOf     = require(25),\n    Field     = require(16),\n    MapField  = require(20),\n    Service   = require(33),\n    Message   = require(21),\n    Reader    = require(27),\n    Writer    = require(42),\n    util      = require(37),\n    encoder   = require(14),\n    decoder   = require(13),\n    verifier  = require(40),\n    converter = require(12),\n    wrappers  = require(41);\n\n/**\n * Constructs a new reflected message type instance.\n * @classdesc Reflected message type.\n * @extends NamespaceBase\n * @constructor\n * @param {string} name Message name\n * @param {Object.<string,*>} [options] Declared options\n */\nfunction Type(name, options) {\n    Namespace.call(this, name, options);\n\n    /**\n     * Message fields.\n     * @type {Object.<string,Field>}\n     */\n    this.fields = {};  // toJSON, marker\n\n    /**\n     * Oneofs declared within this namespace, if any.\n     * @type {Object.<string,OneOf>}\n     */\n    this.oneofs = undefined; // toJSON\n\n    /**\n     * Extension ranges, if any.\n     * @type {number[][]}\n     */\n    this.extensions = undefined; // toJSON\n\n    /**\n     * Reserved ranges, if any.\n     * @type {Array.<number[]|string>}\n     */\n    this.reserved = undefined; // toJSON\n\n    /*?\n     * Whether this type is a legacy group.\n     * @type {boolean|undefined}\n     */\n    this.group = undefined; // toJSON\n\n    /**\n     * Cached fields by id.\n     * @type {Object.<number,Field>|null}\n     * @private\n     */\n    this._fieldsById = null;\n\n    /**\n     * Cached fields as an array.\n     * @type {Field[]|null}\n     * @private\n     */\n    this._fieldsArray = null;\n\n    /**\n     * Cached oneofs as an array.\n     * @type {OneOf[]|null}\n     * @private\n     */\n    this._oneofsArray = null;\n\n    /**\n     * Cached constructor.\n     * @type {Constructor<{}>}\n     * @private\n     */\n    this._ctor = null;\n}\n\nObject.defineProperties(Type.prototype, {\n\n    /**\n     * Message fields by id.\n     * @name Type#fieldsById\n     * @type {Object.<number,Field>}\n     * @readonly\n     */\n    fieldsById: {\n        get: function() {\n\n            /* istanbul ignore if */\n            if (this._fieldsById)\n                return this._fieldsById;\n\n            this._fieldsById = {};\n            for (var names = Object.keys(this.fields), i = 0; i < names.length; ++i) {\n                var field = this.fields[names[i]],\n                    id = field.id;\n\n                /* istanbul ignore if */\n                if (this._fieldsById[id])\n                    throw Error(\"duplicate id \" + id + \" in \" + this);\n\n                this._fieldsById[id] = field;\n            }\n            return this._fieldsById;\n        }\n    },\n\n    /**\n     * Fields of this message as an array for iteration.\n     * @name Type#fieldsArray\n     * @type {Field[]}\n     * @readonly\n     */\n    fieldsArray: {\n        get: function() {\n            return this._fieldsArray || (this._fieldsArray = util.toArray(this.fields));\n        }\n    },\n\n    /**\n     * Oneofs of this message as an array for iteration.\n     * @name Type#oneofsArray\n     * @type {OneOf[]}\n     * @readonly\n     */\n    oneofsArray: {\n        get: function() {\n            return this._oneofsArray || (this._oneofsArray = util.toArray(this.oneofs));\n        }\n    },\n\n    /**\n     * The registered constructor, if any registered, otherwise a generic constructor.\n     * Assigning a function replaces the internal constructor. If the function does not extend {@link Message} yet, its prototype will be setup accordingly and static methods will be populated. If it already extends {@link Message}, it will just replace the internal constructor.\n     * @name Type#ctor\n     * @type {Constructor<{}>}\n     */\n    ctor: {\n        get: function() {\n            return this._ctor || (this.ctor = Type.generateConstructor(this)());\n        },\n        set: function(ctor) {\n\n            // Ensure proper prototype\n            var prototype = ctor.prototype;\n            if (!(prototype instanceof Message)) {\n                (ctor.prototype = new Message()).constructor = ctor;\n                util.merge(ctor.prototype, prototype);\n            }\n\n            // Classes and messages reference their reflected type\n            ctor.$type = ctor.prototype.$type = this;\n\n            // Mix in static methods\n            util.merge(ctor, Message, true);\n\n            this._ctor = ctor;\n\n            // Messages have non-enumerable default values on their prototype\n            var i = 0;\n            for (; i < /* initializes */ this.fieldsArray.length; ++i)\n                this._fieldsArray[i].resolve(); // ensures a proper value\n\n            // Messages have non-enumerable getters and setters for each virtual oneof field\n            var ctorProperties = {};\n            for (i = 0; i < /* initializes */ this.oneofsArray.length; ++i)\n                ctorProperties[this._oneofsArray[i].resolve().name] = {\n                    get: util.oneOfGetter(this._oneofsArray[i].oneof),\n                    set: util.oneOfSetter(this._oneofsArray[i].oneof)\n                };\n            if (i)\n                Object.defineProperties(ctor.prototype, ctorProperties);\n        }\n    }\n});\n\n/**\n * Generates a constructor function for the specified type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nType.generateConstructor = function generateConstructor(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n    var gen = util.codegen([\"p\"], mtype.name);\n    // explicitly initialize mutable object/array fields so that these aren't just inherited from the prototype\n    for (var i = 0, field; i < mtype.fieldsArray.length; ++i)\n        if ((field = mtype._fieldsArray[i]).map) gen\n            (\"this%s={}\", util.safeProp(field.name));\n        else if (field.repeated) gen\n            (\"this%s=[]\", util.safeProp(field.name));\n    return gen\n    (\"if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)\") // omit undefined or null\n        (\"this[ks[i]]=p[ks[i]]\");\n    /* eslint-enable no-unexpected-multiline */\n};\n\nfunction clearCache(type) {\n    type._fieldsById = type._fieldsArray = type._oneofsArray = null;\n    delete type.encode;\n    delete type.decode;\n    delete type.verify;\n    return type;\n}\n\n/**\n * Message type descriptor.\n * @interface IType\n * @extends INamespace\n * @property {Object.<string,IOneOf>} [oneofs] Oneof descriptors\n * @property {Object.<string,IField>} fields Field descriptors\n * @property {number[][]} [extensions] Extension ranges\n * @property {number[][]} [reserved] Reserved ranges\n * @property {boolean} [group=false] Whether a legacy group or not\n */\n\n/**\n * Creates a message type from a message type descriptor.\n * @param {string} name Message name\n * @param {IType} json Message type descriptor\n * @returns {Type} Created message type\n */\nType.fromJSON = function fromJSON(name, json) {\n    var type = new Type(name, json.options);\n    type.extensions = json.extensions;\n    type.reserved = json.reserved;\n    var names = Object.keys(json.fields),\n        i = 0;\n    for (; i < names.length; ++i)\n        type.add(\n            ( typeof json.fields[names[i]].keyType !== \"undefined\"\n            ? MapField.fromJSON\n            : Field.fromJSON )(names[i], json.fields[names[i]])\n        );\n    if (json.oneofs)\n        for (names = Object.keys(json.oneofs), i = 0; i < names.length; ++i)\n            type.add(OneOf.fromJSON(names[i], json.oneofs[names[i]]));\n    if (json.nested)\n        for (names = Object.keys(json.nested), i = 0; i < names.length; ++i) {\n            var nested = json.nested[names[i]];\n            type.add( // most to least likely\n                ( nested.id !== undefined\n                ? Field.fromJSON\n                : nested.fields !== undefined\n                ? Type.fromJSON\n                : nested.values !== undefined\n                ? Enum.fromJSON\n                : nested.methods !== undefined\n                ? Service.fromJSON\n                : Namespace.fromJSON )(names[i], nested)\n            );\n        }\n    if (json.extensions && json.extensions.length)\n        type.extensions = json.extensions;\n    if (json.reserved && json.reserved.length)\n        type.reserved = json.reserved;\n    if (json.group)\n        type.group = true;\n    if (json.comment)\n        type.comment = json.comment;\n    return type;\n};\n\n/**\n * Converts this message type to a message type descriptor.\n * @param {IToJSONOptions} [toJSONOptions] JSON conversion options\n * @returns {IType} Message type descriptor\n */\nType.prototype.toJSON = function toJSON(toJSONOptions) {\n    var inherited = Namespace.prototype.toJSON.call(this, toJSONOptions);\n    var keepComments = toJSONOptions ? Boolean(toJSONOptions.keepComments) : false;\n    return util.toObject([\n        \"options\"    , inherited && inherited.options || undefined,\n        \"oneofs\"     , Namespace.arrayToJSON(this.oneofsArray, toJSONOptions),\n        \"fields\"     , Namespace.arrayToJSON(this.fieldsArray.filter(function(obj) { return !obj.declaringField; }), toJSONOptions) || {},\n        \"extensions\" , this.extensions && this.extensions.length ? this.extensions : undefined,\n        \"reserved\"   , this.reserved && this.reserved.length ? this.reserved : undefined,\n        \"group\"      , this.group || undefined,\n        \"nested\"     , inherited && inherited.nested || undefined,\n        \"comment\"    , keepComments ? this.comment : undefined\n    ]);\n};\n\n/**\n * @override\n */\nType.prototype.resolveAll = function resolveAll() {\n    var fields = this.fieldsArray, i = 0;\n    while (i < fields.length)\n        fields[i++].resolve();\n    var oneofs = this.oneofsArray; i = 0;\n    while (i < oneofs.length)\n        oneofs[i++].resolve();\n    return Namespace.prototype.resolveAll.call(this);\n};\n\n/**\n * @override\n */\nType.prototype.get = function get(name) {\n    return this.fields[name]\n        || this.oneofs && this.oneofs[name]\n        || this.nested && this.nested[name]\n        || null;\n};\n\n/**\n * Adds a nested object to this type.\n * @param {ReflectionObject} object Nested object to add\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If there is already a nested object with this name or, if a field, when there is already a field with this id\n */\nType.prototype.add = function add(object) {\n\n    if (this.get(object.name))\n        throw Error(\"duplicate name '\" + object.name + \"' in \" + this);\n\n    if (object instanceof Field && object.extend === undefined) {\n        // NOTE: Extension fields aren't actual fields on the declaring type, but nested objects.\n        // The root object takes care of adding distinct sister-fields to the respective extended\n        // type instead.\n\n        // avoids calling the getter if not absolutely necessary because it's called quite frequently\n        if (this._fieldsById ? /* istanbul ignore next */ this._fieldsById[object.id] : this.fieldsById[object.id])\n            throw Error(\"duplicate id \" + object.id + \" in \" + this);\n        if (this.isReservedId(object.id))\n            throw Error(\"id \" + object.id + \" is reserved in \" + this);\n        if (this.isReservedName(object.name))\n            throw Error(\"name '\" + object.name + \"' is reserved in \" + this);\n\n        if (object.parent)\n            object.parent.remove(object);\n        this.fields[object.name] = object;\n        object.message = this;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n        if (!this.oneofs)\n            this.oneofs = {};\n        this.oneofs[object.name] = object;\n        object.onAdd(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.add.call(this, object);\n};\n\n/**\n * Removes a nested object from this type.\n * @param {ReflectionObject} object Nested object to remove\n * @returns {Type} `this`\n * @throws {TypeError} If arguments are invalid\n * @throws {Error} If `object` is not a member of this type\n */\nType.prototype.remove = function remove(object) {\n    if (object instanceof Field && object.extend === undefined) {\n        // See Type#add for the reason why extension fields are excluded here.\n\n        /* istanbul ignore if */\n        if (!this.fields || this.fields[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.fields[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    if (object instanceof OneOf) {\n\n        /* istanbul ignore if */\n        if (!this.oneofs || this.oneofs[object.name] !== object)\n            throw Error(object + \" is not a member of \" + this);\n\n        delete this.oneofs[object.name];\n        object.parent = null;\n        object.onRemove(this);\n        return clearCache(this);\n    }\n    return Namespace.prototype.remove.call(this, object);\n};\n\n/**\n * Tests if the specified id is reserved.\n * @param {number} id Id to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedId = function isReservedId(id) {\n    return Namespace.isReservedId(this.reserved, id);\n};\n\n/**\n * Tests if the specified name is reserved.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nType.prototype.isReservedName = function isReservedName(name) {\n    return Namespace.isReservedName(this.reserved, name);\n};\n\n/**\n * Creates a new message of this type using the specified properties.\n * @param {Object.<string,*>} [properties] Properties to set\n * @returns {Message<{}>} Message instance\n */\nType.prototype.create = function create(properties) {\n    return new this.ctor(properties);\n};\n\n/**\n * Sets up {@link Type#encode|encode}, {@link Type#decode|decode} and {@link Type#verify|verify}.\n * @returns {Type} `this`\n */\nType.prototype.setup = function setup() {\n    // Sets up everything at once so that the prototype chain does not have to be re-evaluated\n    // multiple times (V8, soft-deopt prototype-check).\n\n    var fullName = this.fullName,\n        types    = [];\n    for (var i = 0; i < /* initializes */ this.fieldsArray.length; ++i)\n        types.push(this._fieldsArray[i].resolve().resolvedType);\n\n    // Replace setup methods with type-specific generated functions\n    this.encode = encoder(this)({\n        Writer : Writer,\n        types  : types,\n        util   : util\n    });\n    this.decode = decoder(this)({\n        Reader : Reader,\n        types  : types,\n        util   : util\n    });\n    this.verify = verifier(this)({\n        types : types,\n        util  : util\n    });\n    this.fromObject = converter.fromObject(this)({\n        types : types,\n        util  : util\n    });\n    this.toObject = converter.toObject(this)({\n        types : types,\n        util  : util\n    });\n\n    // Inject custom wrappers for common types\n    var wrapper = wrappers[fullName];\n    if (wrapper) {\n        var originalThis = Object.create(this);\n        // if (wrapper.fromObject) {\n            originalThis.fromObject = this.fromObject;\n            this.fromObject = wrapper.fromObject.bind(originalThis);\n        // }\n        // if (wrapper.toObject) {\n            originalThis.toObject = this.toObject;\n            this.toObject = wrapper.toObject.bind(originalThis);\n        // }\n    }\n\n    return this;\n};\n\n/**\n * Encodes a message of this type. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encode = function encode_setup(message, writer) {\n    return this.setup().encode(message, writer); // overrides this method\n};\n\n/**\n * Encodes a message of this type preceeded by its byte length as a varint. Does not implicitly {@link Type#verify|verify} messages.\n * @param {Message<{}>|Object.<string,*>} message Message instance or plain object\n * @param {Writer} [writer] Writer to encode to\n * @returns {Writer} writer\n */\nType.prototype.encodeDelimited = function encodeDelimited(message, writer) {\n    return this.encode(message, writer && writer.len ? writer.fork() : writer).ldelim();\n};\n\n/**\n * Decodes a message of this type.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @param {number} [length] Length of the message, if known beforehand\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError<{}>} If required fields are missing\n */\nType.prototype.decode = function decode_setup(reader, length) {\n    return this.setup().decode(reader, length); // overrides this method\n};\n\n/**\n * Decodes a message of this type preceeded by its byte length as a varint.\n * @param {Reader|Uint8Array} reader Reader or buffer to decode from\n * @returns {Message<{}>} Decoded message\n * @throws {Error} If the payload is not a reader or valid buffer\n * @throws {util.ProtocolError} If required fields are missing\n */\nType.prototype.decodeDelimited = function decodeDelimited(reader) {\n    if (!(reader instanceof Reader))\n        reader = Reader.create(reader);\n    return this.decode(reader, reader.uint32());\n};\n\n/**\n * Verifies that field values are valid and that required fields are present.\n * @param {Object.<string,*>} message Plain object to verify\n * @returns {null|string} `null` if valid, otherwise the reason why it is not\n */\nType.prototype.verify = function verify_setup(message) {\n    return this.setup().verify(message); // overrides this method\n};\n\n/**\n * Creates a new message of this type from a plain object. Also converts values to their respective internal types.\n * @param {Object.<string,*>} object Plain object to convert\n * @returns {Message<{}>} Message instance\n */\nType.prototype.fromObject = function fromObject(object) {\n    return this.setup().fromObject(object);\n};\n\n/**\n * Conversion options as used by {@link Type#toObject} and {@link Message.toObject}.\n * @interface IConversionOptions\n * @property {Function} [longs] Long conversion type.\n * Valid values are `String` and `Number` (the global types).\n * Defaults to copy the present value, which is a possibly unsafe number without and a {@link Long} with a long library.\n * @property {Function} [enums] Enum value conversion type.\n * Only valid value is `String` (the global type).\n * Defaults to copy the present value, which is the numeric id.\n * @property {Function} [bytes] Bytes value conversion type.\n * Valid values are `Array` and (a base64 encoded) `String` (the global types).\n * Defaults to copy the present value, which usually is a Buffer under node and an Uint8Array in the browser.\n * @property {boolean} [defaults=false] Also sets default values on the resulting object\n * @property {boolean} [arrays=false] Sets empty arrays for missing repeated fields even if `defaults=false`\n * @property {boolean} [objects=false] Sets empty objects for missing map fields even if `defaults=false`\n * @property {boolean} [oneofs=false] Includes virtual oneof properties set to the present field's name, if any\n * @property {boolean} [json=false] Performs additional JSON compatibility conversions, i.e. NaN and Infinity to strings\n */\n\n/**\n * Creates a plain object from a message of this type. Also converts values to other types if specified.\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n */\nType.prototype.toObject = function toObject(message, options) {\n    return this.setup().toObject(message, options);\n};\n\n/**\n * Decorator function as returned by {@link Type.d} (TypeScript).\n * @typedef TypeDecorator\n * @type {function}\n * @param {Constructor<T>} target Target constructor\n * @returns {undefined}\n * @template T extends Message<T>\n */\n\n/**\n * Type decorator (TypeScript).\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {TypeDecorator<T>} Decorator function\n * @template T extends Message<T>\n */\nType.d = function decorateType(typeName) {\n    return function typeDecorator(target) {\n        util.decorateType(target, typeName);\n    };\n};\n", "\"use strict\";\n\n/**\n * Common type constants.\n * @namespace\n */\nvar types = exports;\n\nvar util = require(37);\n\nvar s = [\n    \"double\",   // 0\n    \"float\",    // 1\n    \"int32\",    // 2\n    \"uint32\",   // 3\n    \"sint32\",   // 4\n    \"fixed32\",  // 5\n    \"sfixed32\", // 6\n    \"int64\",    // 7\n    \"uint64\",   // 8\n    \"sint64\",   // 9\n    \"fixed64\",  // 10\n    \"sfixed64\", // 11\n    \"bool\",     // 12\n    \"string\",   // 13\n    \"bytes\"     // 14\n];\n\nfunction bake(values, offset) {\n    var i = 0, o = {};\n    offset |= 0;\n    while (i < values.length) o[s[i + offset]] = values[i++];\n    return o;\n}\n\n/**\n * Basic type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n * @property {number} bytes=2 Ldelim wire type\n */\ntypes.basic = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2,\n    /* bytes    */ 2\n]);\n\n/**\n * Basic type defaults.\n * @type {Object.<string,*>}\n * @const\n * @property {number} double=0 Double default\n * @property {number} float=0 Float default\n * @property {number} int32=0 Int32 default\n * @property {number} uint32=0 Uint32 default\n * @property {number} sint32=0 Sint32 default\n * @property {number} fixed32=0 Fixed32 default\n * @property {number} sfixed32=0 Sfixed32 default\n * @property {number} int64=0 Int64 default\n * @property {number} uint64=0 Uint64 default\n * @property {number} sint64=0 Sint32 default\n * @property {number} fixed64=0 Fixed64 default\n * @property {number} sfixed64=0 Sfixed64 default\n * @property {boolean} bool=false Bool default\n * @property {string} string=\"\" String default\n * @property {Array.<number>} bytes=Array(0) Bytes default\n * @property {null} message=null Message default\n */\ntypes.defaults = bake([\n    /* double   */ 0,\n    /* float    */ 0,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 0,\n    /* sfixed32 */ 0,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 0,\n    /* sfixed64 */ 0,\n    /* bool     */ false,\n    /* string   */ \"\",\n    /* bytes    */ util.emptyArray,\n    /* message  */ null\n]);\n\n/**\n * Basic long type wire types.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n */\ntypes.long = bake([\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1\n], 7);\n\n/**\n * Allowed types for map keys with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n * @property {number} string=2 Ldelim wire type\n */\ntypes.mapKey = bake([\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0,\n    /* string   */ 2\n], 2);\n\n/**\n * Allowed types for packed repeated fields with their associated wire type.\n * @type {Object.<string,number>}\n * @const\n * @property {number} double=1 Fixed64 wire type\n * @property {number} float=5 Fixed32 wire type\n * @property {number} int32=0 Varint wire type\n * @property {number} uint32=0 Varint wire type\n * @property {number} sint32=0 Varint wire type\n * @property {number} fixed32=5 Fixed32 wire type\n * @property {number} sfixed32=5 Fixed32 wire type\n * @property {number} int64=0 Varint wire type\n * @property {number} uint64=0 Varint wire type\n * @property {number} sint64=0 Varint wire type\n * @property {number} fixed64=1 Fixed64 wire type\n * @property {number} sfixed64=1 Fixed64 wire type\n * @property {number} bool=0 Varint wire type\n */\ntypes.packed = bake([\n    /* double   */ 1,\n    /* float    */ 5,\n    /* int32    */ 0,\n    /* uint32   */ 0,\n    /* sint32   */ 0,\n    /* fixed32  */ 5,\n    /* sfixed32 */ 5,\n    /* int64    */ 0,\n    /* uint64   */ 0,\n    /* sint64   */ 0,\n    /* fixed64  */ 1,\n    /* sfixed64 */ 1,\n    /* bool     */ 0\n]);\n", "\"use strict\";\n\n/**\n * Various utility functions.\n * @namespace\n */\nvar util = module.exports = require(39);\n\nvar roots = require(30);\n\nvar Type, // cyclic\n    Enum;\n\nutil.codegen = require(3);\nutil.fetch   = require(5);\nutil.path    = require(8);\n\n/**\n * Node's fs module if available.\n * @type {Object.<string,*>}\n */\nutil.fs = util.inquire(\"fs\");\n\n/**\n * Converts an object's values to an array.\n * @param {Object.<string,*>} object Object to convert\n * @returns {Array.<*>} Converted array\n */\nutil.toArray = function toArray(object) {\n    if (object) {\n        var keys  = Object.keys(object),\n            array = new Array(keys.length),\n            index = 0;\n        while (index < keys.length)\n            array[index] = object[keys[index++]];\n        return array;\n    }\n    return [];\n};\n\n/**\n * Converts an array of keys immediately followed by their respective value to an object, omitting undefined values.\n * @param {Array.<*>} array Array to convert\n * @returns {Object.<string,*>} Converted object\n */\nutil.toObject = function toObject(array) {\n    var object = {},\n        index  = 0;\n    while (index < array.length) {\n        var key = array[index++],\n            val = array[index++];\n        if (val !== undefined)\n            object[key] = val;\n    }\n    return object;\n};\n\nvar safePropBackslashRe = /\\\\/g,\n    safePropQuoteRe     = /\"/g;\n\n/**\n * Tests whether the specified name is a reserved word in JS.\n * @param {string} name Name to test\n * @returns {boolean} `true` if reserved, otherwise `false`\n */\nutil.isReserved = function isReserved(name) {\n    return /^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(name);\n};\n\n/**\n * Returns a safe property accessor for the specified property name.\n * @param {string} prop Property name\n * @returns {string} Safe accessor\n */\nutil.safeProp = function safeProp(prop) {\n    if (!/^[$\\w_]+$/.test(prop) || util.isReserved(prop))\n        return \"[\\\"\" + prop.replace(safePropBackslashRe, \"\\\\\\\\\").replace(safePropQuoteRe, \"\\\\\\\"\") + \"\\\"]\";\n    return \".\" + prop;\n};\n\n/**\n * Converts the first character of a string to upper case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.ucFirst = function ucFirst(str) {\n    return str.charAt(0).toUpperCase() + str.substring(1);\n};\n\nvar camelCaseRe = /_([a-z])/g;\n\n/**\n * Converts a string to camel case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.camelCase = function camelCase(str) {\n    return str.substring(0, 1)\n         + str.substring(1)\n               .replace(camelCaseRe, function($0, $1) { return $1.toUpperCase(); });\n};\n\n/**\n * Compares reflected fields by id.\n * @param {Field} a First field\n * @param {Field} b Second field\n * @returns {number} Comparison value\n */\nutil.compareFieldsById = function compareFieldsById(a, b) {\n    return a.id - b.id;\n};\n\n/**\n * Decorator helper for types (TypeScript).\n * @param {Constructor<T>} ctor Constructor function\n * @param {string} [typeName] Type name, defaults to the constructor's name\n * @returns {Type} Reflected type\n * @template T extends Message<T>\n * @property {Root} root Decorators root\n */\nutil.decorateType = function decorateType(ctor, typeName) {\n\n    /* istanbul ignore if */\n    if (ctor.$type) {\n        if (typeName && ctor.$type.name !== typeName) {\n            util.decorateRoot.remove(ctor.$type);\n            ctor.$type.name = typeName;\n            util.decorateRoot.add(ctor.$type);\n        }\n        return ctor.$type;\n    }\n\n    /* istanbul ignore next */\n    if (!Type)\n        Type = require(35);\n\n    var type = new Type(typeName || ctor.name);\n    util.decorateRoot.add(type);\n    type.ctor = ctor; // sets up .encode, .decode etc.\n    Object.defineProperty(ctor, \"$type\", { value: type, enumerable: false });\n    Object.defineProperty(ctor.prototype, \"$type\", { value: type, enumerable: false });\n    return type;\n};\n\nvar decorateEnumIndex = 0;\n\n/**\n * Decorator helper for enums (TypeScript).\n * @param {Object} object Enum object\n * @returns {Enum} Reflected enum\n */\nutil.decorateEnum = function decorateEnum(object) {\n\n    /* istanbul ignore if */\n    if (object.$type)\n        return object.$type;\n\n    /* istanbul ignore next */\n    if (!Enum)\n        Enum = require(15);\n\n    var enm = new Enum(\"Enum\" + decorateEnumIndex++, object);\n    util.decorateRoot.add(enm);\n    Object.defineProperty(object, \"$type\", { value: enm, enumerable: false });\n    return enm;\n};\n\n/**\n * Decorator root (TypeScript).\n * @name util.decorateRoot\n * @type {Root}\n * @readonly\n */\nObject.defineProperty(util, \"decorateRoot\", {\n    get: function() {\n        return roots[\"decorated\"] || (roots[\"decorated\"] = new (require(29))());\n    }\n});\n", "\"use strict\";\nmodule.exports = LongBits;\n\nvar util = require(39);\n\n/**\n * Constructs new long bits.\n * @classdesc Helper class for working with the low and high bits of a 64 bit value.\n * @memberof util\n * @constructor\n * @param {number} lo Low 32 bits, unsigned\n * @param {number} hi High 32 bits, unsigned\n */\nfunction LongBits(lo, hi) {\n\n    // note that the casts below are theoretically unnecessary as of today, but older statically\n    // generated converter code might still call the ctor with signed 32bits. kept for compat.\n\n    /**\n     * Low bits.\n     * @type {number}\n     */\n    this.lo = lo >>> 0;\n\n    /**\n     * High bits.\n     * @type {number}\n     */\n    this.hi = hi >>> 0;\n}\n\n/**\n * Zero bits.\n * @memberof util.LongBits\n * @type {util.LongBits}\n */\nvar zero = LongBits.zero = new LongBits(0, 0);\n\nzero.toNumber = function() { return 0; };\nzero.zzEncode = zero.zzDecode = function() { return this; };\nzero.length = function() { return 1; };\n\n/**\n * Zero hash.\n * @memberof util.LongBits\n * @type {string}\n */\nvar zeroHash = LongBits.zeroHash = \"\\0\\0\\0\\0\\0\\0\\0\\0\";\n\n/**\n * Constructs new long bits from the specified number.\n * @param {number} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.fromNumber = function fromNumber(value) {\n    if (value === 0)\n        return zero;\n    var sign = value < 0;\n    if (sign)\n        value = -value;\n    var lo = value >>> 0,\n        hi = (value - lo) / 4294967296 >>> 0;\n    if (sign) {\n        hi = ~hi >>> 0;\n        lo = ~lo >>> 0;\n        if (++lo > 4294967295) {\n            lo = 0;\n            if (++hi > 4294967295)\n                hi = 0;\n        }\n    }\n    return new LongBits(lo, hi);\n};\n\n/**\n * Constructs new long bits from a number, long or string.\n * @param {number|string} value Value\n * @returns {util.LongBits} Instance\n */\nLongBits.from = function from(value) {\n    if (typeof value === \"number\")\n        return LongBits.fromNumber(value);\n    if (util.isString(value)) {\n        /* istanbul ignore else */\n        if (util.Long)\n            value = util.Long.fromString(value);\n        else\n            return LongBits.fromNumber(parseInt(value, 10));\n    }\n    return value.low || value.high ? new LongBits(value.low >>> 0, value.high >>> 0) : zero;\n};\n\n/**\n * Converts this long bits to a possibly unsafe JavaScript number.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {number} Possibly unsafe number\n */\nLongBits.prototype.toNumber = function toNumber(unsigned) {\n    if (!unsigned && this.hi >>> 31) {\n        var lo = ~this.lo + 1 >>> 0,\n            hi = ~this.hi     >>> 0;\n        if (!lo)\n            hi = hi + 1 >>> 0;\n        return -(lo + hi * 4294967296);\n    }\n    return this.lo + this.hi * 4294967296;\n};\n\n/*\n * Converts this long bits to a long.\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long} Long\n */\nLongBits.prototype.toLong = function toLong(unsigned) {\n    return util.Long\n        ? new util.Long(this.lo | 0, this.hi | 0, Boolean(unsigned))\n        /* istanbul ignore next */\n        : { low: this.lo | 0, high: this.hi | 0, unsigned: Boolean(unsigned) };\n};\n\nvar charCodeAt = String.prototype.charCodeAt;\n\n/**\n * Constructs new long bits from the specified 8 characters long hash.\n * @param {string} hash Hash\n * @returns {util.LongBits} Bits\n */\nLongBits.fromHash = function fromHash(hash) {\n    if (hash === zeroHash)\n        return zero;\n    return new LongBits(\n        ( charCodeAt.call(hash, 0)\n        | charCodeAt.call(hash, 1) << 8\n        | charCodeAt.call(hash, 2) << 16\n        | charCodeAt.call(hash, 3) << 24) >>> 0\n    ,\n        ( charCodeAt.call(hash, 4)\n        | charCodeAt.call(hash, 5) << 8\n        | charCodeAt.call(hash, 6) << 16\n        | charCodeAt.call(hash, 7) << 24) >>> 0\n    );\n};\n\n/**\n * Converts this long bits to a 8 characters long hash.\n * @returns {string} Hash\n */\nLongBits.prototype.toHash = function toHash() {\n    return String.fromCharCode(\n        this.lo        & 255,\n        this.lo >>> 8  & 255,\n        this.lo >>> 16 & 255,\n        this.lo >>> 24      ,\n        this.hi        & 255,\n        this.hi >>> 8  & 255,\n        this.hi >>> 16 & 255,\n        this.hi >>> 24\n    );\n};\n\n/**\n * Zig-zag encodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzEncode = function zzEncode() {\n    var mask =   this.hi >> 31;\n    this.hi  = ((this.hi << 1 | this.lo >>> 31) ^ mask) >>> 0;\n    this.lo  = ( this.lo << 1                   ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Zig-zag decodes this long bits.\n * @returns {util.LongBits} `this`\n */\nLongBits.prototype.zzDecode = function zzDecode() {\n    var mask = -(this.lo & 1);\n    this.lo  = ((this.lo >>> 1 | this.hi << 31) ^ mask) >>> 0;\n    this.hi  = ( this.hi >>> 1                  ^ mask) >>> 0;\n    return this;\n};\n\n/**\n * Calculates the length of this longbits when encoded as a varint.\n * @returns {number} Length\n */\nLongBits.prototype.length = function length() {\n    var part0 =  this.lo,\n        part1 = (this.lo >>> 28 | this.hi << 4) >>> 0,\n        part2 =  this.hi >>> 24;\n    return part2 === 0\n         ? part1 === 0\n           ? part0 < 16384\n             ? part0 < 128 ? 1 : 2\n             : part0 < 2097152 ? 3 : 4\n           : part1 < 16384\n             ? part1 < 128 ? 5 : 6\n             : part1 < 2097152 ? 7 : 8\n         : part2 < 128 ? 9 : 10;\n};\n", "\"use strict\";\nvar util = exports;\n\n// used to return a Promise where callback is omitted\nutil.asPromise = require(1);\n\n// converts to / from base64 encoded strings\nutil.base64 = require(2);\n\n// base class of rpc.Service\nutil.EventEmitter = require(4);\n\n// float handling accross browsers\nutil.float = require(6);\n\n// requires modules optionally and hides the call from bundlers\nutil.inquire = require(7);\n\n// converts to / from utf8 encoded strings\nutil.utf8 = require(10);\n\n// provides a node-like buffer pool in the browser\nutil.pool = require(9);\n\n// utility to work with the low and high bits of a 64 bit value\nutil.LongBits = require(38);\n\n// global object reference\nutil.global = typeof window !== \"undefined\" && window\n           || typeof global !== \"undefined\" && global\n           || typeof self   !== \"undefined\" && self\n           || this; // eslint-disable-line no-invalid-this\n\n/**\n * An immuable empty array.\n * @memberof util\n * @type {Array.<*>}\n * @const\n */\nutil.emptyArray = Object.freeze ? Object.freeze([]) : /* istanbul ignore next */ []; // used on prototypes\n\n/**\n * An immutable empty object.\n * @type {Object}\n * @const\n */\nutil.emptyObject = Object.freeze ? Object.freeze({}) : /* istanbul ignore next */ {}; // used on prototypes\n\n/**\n * Whether running within node or not.\n * @memberof util\n * @type {boolean}\n * @const\n */\nutil.isNode = Boolean(util.global.process && util.global.process.versions && util.global.process.versions.node);\n\n/**\n * Tests if the specified value is an integer.\n * @function\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is an integer\n */\nutil.isInteger = Number.isInteger || /* istanbul ignore next */ function isInteger(value) {\n    return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n};\n\n/**\n * Tests if the specified value is a string.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a string\n */\nutil.isString = function isString(value) {\n    return typeof value === \"string\" || value instanceof String;\n};\n\n/**\n * Tests if the specified value is a non-null object.\n * @param {*} value Value to test\n * @returns {boolean} `true` if the value is a non-null object\n */\nutil.isObject = function isObject(value) {\n    return value && typeof value === \"object\";\n};\n\n/**\n * Checks if a property on a message is considered to be present.\n * This is an alias of {@link util.isSet}.\n * @function\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isset =\n\n/**\n * Checks if a property on a message is considered to be present.\n * @param {Object} obj Plain object or message instance\n * @param {string} prop Property name\n * @returns {boolean} `true` if considered to be present, otherwise `false`\n */\nutil.isSet = function isSet(obj, prop) {\n    var value = obj[prop];\n    if (value != null && obj.hasOwnProperty(prop)) // eslint-disable-line eqeqeq, no-prototype-builtins\n        return typeof value !== \"object\" || (Array.isArray(value) ? value.length : Object.keys(value).length) > 0;\n    return false;\n};\n\n/**\n * Any compatible Buffer instance.\n * This is a minimal stand-alone definition of a Buffer instance. The actual type is that exported by node's typings.\n * @interface Buffer\n * @extends Uint8Array\n */\n\n/**\n * Node's Buffer class if available.\n * @type {Constructor<Buffer>}\n */\nutil.Buffer = (function() {\n    try {\n        var Buffer = util.inquire(\"buffer\").Buffer;\n        // refuse to use non-node buffers if not explicitly assigned (perf reasons):\n        return Buffer.prototype.utf8Write ? Buffer : /* istanbul ignore next */ null;\n    } catch (e) {\n        /* istanbul ignore next */\n        return null;\n    }\n})();\n\n// Internal alias of or polyfull for Buffer.from.\nutil._Buffer_from = null;\n\n// Internal alias of or polyfill for Buffer.allocUnsafe.\nutil._Buffer_allocUnsafe = null;\n\n/**\n * Creates a new buffer of whatever type supported by the environment.\n * @param {number|number[]} [sizeOrArray=0] Buffer size or number array\n * @returns {Uint8Array|Buffer} Buffer\n */\nutil.newBuffer = function newBuffer(sizeOrArray) {\n    /* istanbul ignore next */\n    return typeof sizeOrArray === \"number\"\n        ? util.Buffer\n            ? util._Buffer_allocUnsafe(sizeOrArray)\n            : new util.Array(sizeOrArray)\n        : util.Buffer\n            ? util._Buffer_from(sizeOrArray)\n            : typeof Uint8Array === \"undefined\"\n                ? sizeOrArray\n                : new Uint8Array(sizeOrArray);\n};\n\n/**\n * Array implementation used in the browser. `Uint8Array` if supported, otherwise `Array`.\n * @type {Constructor<Uint8Array>}\n */\nutil.Array = typeof Uint8Array !== \"undefined\" ? Uint8Array /* istanbul ignore next */ : Array;\n\n/*\n * Long.js's Long class if available and $ENABLE_LONG is set. This lets us leave it on\n * for this package's tests but have it be off in actual usage-reporting-protobuf use.\n * (We leave it on for some mode where there is no `process` that is used by tests.)\n */\nutil.Long = (typeof process === 'undefined' || process.env.ENABLE_LONG) ? (/* istanbul ignore next */ util.global.dcodeIO && /* istanbul ignore next */ util.global.dcodeIO.Long\n         || /* istanbul ignore next */ util.global.Long\n         || util.inquire(\"long\")) : undefined;\n\n/**\n * Regular expression used to verify 2 bit (`bool`) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key2Re = /^true|false|0|1$/;\n\n/**\n * Regular expression used to verify 32 bit (`int32` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key32Re = /^-?(?:0|[1-9][0-9]*)$/;\n\n/**\n * Regular expression used to verify 64 bit (`int64` etc.) map keys.\n * @type {RegExp}\n * @const\n */\nutil.key64Re = /^(?:[\\\\x00-\\\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;\n\n/*\n * Converts a number or long to an 8 characters long hash string.\n * @param {Long|number} value Value to convert\n * @returns {string} Hash\n */\nutil.longToHash = function longToHash(value) {\n    return value\n        ? util.LongBits.from(value).toHash()\n        : util.LongBits.zeroHash;\n};\n\n/*\n * Converts an 8 characters long hash string to a long or number.\n * @param {string} hash Hash\n * @param {boolean} [unsigned=false] Whether unsigned or not\n * @returns {Long|number} Original value\n */\nutil.longFromHash = function longFromHash(hash, unsigned) {\n    var bits = util.LongBits.fromHash(hash);\n    if (util.Long)\n        return util.Long.fromBits(bits.lo, bits.hi, unsigned);\n    return bits.toNumber(Boolean(unsigned));\n};\n\n/**\n * Merges the properties of the source object into the destination object.\n * @memberof util\n * @param {Object.<string,*>} dst Destination object\n * @param {Object.<string,*>} src Source object\n * @param {boolean} [ifNotSet=false] Merges only if the key is not already set\n * @returns {Object.<string,*>} Destination object\n */\nfunction merge(dst, src, ifNotSet) { // used by converters\n    for (var keys = Object.keys(src), i = 0; i < keys.length; ++i)\n        if (dst[keys[i]] === undefined || !ifNotSet)\n            dst[keys[i]] = src[keys[i]];\n    return dst;\n}\n\nutil.merge = merge;\n\n/**\n * Converts the first character of a string to lower case.\n * @param {string} str String to convert\n * @returns {string} Converted string\n */\nutil.lcFirst = function lcFirst(str) {\n    return str.charAt(0).toLowerCase() + str.substring(1);\n};\n\n/**\n * Creates a custom error constructor.\n * @memberof util\n * @param {string} name Error name\n * @returns {Constructor<Error>} Custom error constructor\n */\nfunction newError(name) {\n\n    function CustomError(message, properties) {\n\n        if (!(this instanceof CustomError))\n            return new CustomError(message, properties);\n\n        // Error.call(this, message);\n        // ^ just returns a new error instance because the ctor can be called as a function\n\n        Object.defineProperty(this, \"message\", { get: function() { return message; } });\n\n        /* istanbul ignore next */\n        if (Error.captureStackTrace) // node\n            Error.captureStackTrace(this, CustomError);\n        else\n            Object.defineProperty(this, \"stack\", { value: (new Error()).stack || \"\" });\n\n        if (properties)\n            merge(this, properties);\n    }\n\n    (CustomError.prototype = Object.create(Error.prototype)).constructor = CustomError;\n\n    Object.defineProperty(CustomError.prototype, \"name\", { get: function() { return name; } });\n\n    CustomError.prototype.toString = function toString() {\n        return this.name + \": \" + this.message;\n    };\n\n    return CustomError;\n}\n\nutil.newError = newError;\n\n/**\n * Constructs a new protocol error.\n * @classdesc Error subclass indicating a protocol specifc error.\n * @memberof util\n * @extends Error\n * @template T extends Message<T>\n * @constructor\n * @param {string} message Error message\n * @param {Object.<string,*>} [properties] Additional properties\n * @example\n * try {\n *     MyMessage.decode(someBuffer); // throws if required fields are missing\n * } catch (e) {\n *     if (e instanceof ProtocolError && e.instance)\n *         console.log(\"decoded so far: \" + JSON.stringify(e.instance));\n * }\n */\nutil.ProtocolError = newError(\"ProtocolError\");\n\n/**\n * So far decoded message instance.\n * @name util.ProtocolError#instance\n * @type {Message<T>}\n */\n\n/**\n * A OneOf getter as returned by {@link util.oneOfGetter}.\n * @typedef OneOfGetter\n * @type {function}\n * @returns {string|undefined} Set field name, if any\n */\n\n/**\n * Builds a getter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfGetter} Unbound getter\n */\nutil.oneOfGetter = function getOneOf(fieldNames) {\n    var fieldMap = {};\n    for (var i = 0; i < fieldNames.length; ++i)\n        fieldMap[fieldNames[i]] = 1;\n\n    /**\n     * @returns {string|undefined} Set field name, if any\n     * @this Object\n     * @ignore\n     */\n    return function() { // eslint-disable-line consistent-return\n        for (var keys = Object.keys(this), i = keys.length - 1; i > -1; --i)\n            if (fieldMap[keys[i]] === 1 && this[keys[i]] !== undefined && this[keys[i]] !== null)\n                return keys[i];\n    };\n};\n\n/**\n * A OneOf setter as returned by {@link util.oneOfSetter}.\n * @typedef OneOfSetter\n * @type {function}\n * @param {string|undefined} value Field name\n * @returns {undefined}\n */\n\n/**\n * Builds a setter for a oneof's present field name.\n * @param {string[]} fieldNames Field names\n * @returns {OneOfSetter} Unbound setter\n */\nutil.oneOfSetter = function setOneOf(fieldNames) {\n\n    /**\n     * @param {string} name Field name\n     * @returns {undefined}\n     * @this Object\n     * @ignore\n     */\n    return function(name) {\n        for (var i = 0; i < fieldNames.length; ++i)\n            if (fieldNames[i] !== name)\n                delete this[fieldNames[i]];\n    };\n};\n\n/**\n * Default conversion options used for {@link Message#toJSON} implementations.\n *\n * These options are close to proto3's JSON mapping with the exception that internal types like Any are handled just like messages. More precisely:\n *\n * - Longs become strings\n * - Enums become string keys\n * - Bytes become base64 encoded strings\n * - (Sub-)Messages become plain objects\n * - Maps become plain objects with all string keys\n * - Repeated fields become arrays\n * - NaN and Infinity for float and double fields become strings\n *\n * @type {IConversionOptions}\n * @see https://developers.google.com/protocol-buffers/docs/proto3?hl=en#json\n */\nutil.toJSONOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    json: true\n};\n\n// Sets up buffer utility according to the environment (called in index-minimal)\nutil._configure = function() {\n    var Buffer = util.Buffer;\n    /* istanbul ignore if */\n    if (!Buffer) {\n        util._Buffer_from = util._Buffer_allocUnsafe = null;\n        return;\n    }\n    // because node 4.x buffers are incompatible & immutable\n    // see: https://github.com/dcodeIO/protobuf.js/pull/665\n    util._Buffer_from = Buffer.from !== Uint8Array.from && Buffer.from ||\n        /* istanbul ignore next */\n        function Buffer_from(value, encoding) {\n            return new Buffer(value, encoding);\n        };\n    util._Buffer_allocUnsafe = Buffer.allocUnsafe ||\n        /* istanbul ignore next */\n        function Buffer_allocUnsafe(size) {\n            return new Buffer(size);\n        };\n};\n", "\"use strict\";\nmodule.exports = verifier;\n\nvar Enum      = require(15),\n    util      = require(37);\n\nfunction invalid(field, expected) {\n    return field.name + \": \" + expected + (field.repeated && expected !== \"array\" ? \"[]\" : field.map && expected !== \"object\" ? \"{k:\"+field.keyType+\"}\" : \"\") + \" expected\";\n}\n\n/**\n * Generates a partial value verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {number} fieldIndex Field index\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyValue(gen, field, fieldIndex, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    if (field.resolvedType) {\n        if (field.resolvedType instanceof Enum) { gen\n            (\"switch(%s){\", ref)\n                (\"default:\")\n                    (\"return%j\", invalid(field, \"enum value\"));\n            for (var keys = Object.keys(field.resolvedType.values), j = 0; j < keys.length; ++j) gen\n                (\"case %i:\", field.resolvedType.values[keys[j]]);\n            gen\n                    (\"break\")\n            (\"}\");\n        } else {\n            gen\n            (\"{\")\n                (\"var e=types[%i].verify(%s);\", fieldIndex, ref)\n                (\"if(e)\")\n                    (\"return%j+e\", field.name + \".\")\n            (\"}\");\n        }\n    } else {\n        switch (field.type) {\n            case \"int32\":\n            case \"uint32\":\n            case \"sint32\":\n            case \"fixed32\":\n            case \"sfixed32\": gen\n                (\"if(!util.isInteger(%s))\", ref)\n                    (\"return%j\", invalid(field, \"integer\"));\n                break;\n            case \"int64\":\n            case \"uint64\":\n            case \"sint64\":\n            case \"fixed64\":\n            case \"sfixed64\": gen\n                (\"if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))\", ref, ref, ref, ref)\n                    (\"return%j\", invalid(field, \"integer|Long\"));\n                break;\n            case \"float\":\n            case \"double\": gen\n                (\"if(typeof %s!==\\\"number\\\")\", ref)\n                    (\"return%j\", invalid(field, \"number\"));\n                break;\n            case \"bool\": gen\n                (\"if(typeof %s!==\\\"boolean\\\")\", ref)\n                    (\"return%j\", invalid(field, \"boolean\"));\n                break;\n            case \"string\": gen\n                (\"if(!util.isString(%s))\", ref)\n                    (\"return%j\", invalid(field, \"string\"));\n                break;\n            case \"bytes\": gen\n                (\"if(!(%s&&typeof %s.length===\\\"number\\\"||util.isString(%s)))\", ref, ref, ref)\n                    (\"return%j\", invalid(field, \"buffer\"));\n                break;\n        }\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a partial key verifier.\n * @param {Codegen} gen Codegen instance\n * @param {Field} field Reflected field\n * @param {string} ref Variable reference\n * @returns {Codegen} Codegen instance\n * @ignore\n */\nfunction genVerifyKey(gen, field, ref) {\n    /* eslint-disable no-unexpected-multiline */\n    switch (field.keyType) {\n        case \"int32\":\n        case \"uint32\":\n        case \"sint32\":\n        case \"fixed32\":\n        case \"sfixed32\": gen\n            (\"if(!util.key32Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"integer key\"));\n            break;\n        case \"int64\":\n        case \"uint64\":\n        case \"sint64\":\n        case \"fixed64\":\n        case \"sfixed64\": gen\n            (\"if(!util.key64Re.test(%s))\", ref) // see comment above: x is ok, d is not\n                (\"return%j\", invalid(field, \"integer|Long key\"));\n            break;\n        case \"bool\": gen\n            (\"if(!util.key2Re.test(%s))\", ref)\n                (\"return%j\", invalid(field, \"boolean key\"));\n            break;\n    }\n    return gen;\n    /* eslint-enable no-unexpected-multiline */\n}\n\n/**\n * Generates a verifier specific to the specified message type.\n * @param {Type} mtype Message type\n * @returns {Codegen} Codegen instance\n */\nfunction verifier(mtype) {\n    /* eslint-disable no-unexpected-multiline */\n\n    var gen = util.codegen([\"m\"], mtype.name + \"$verify\")\n    (\"if(typeof m!==\\\"object\\\"||m===null)\")\n        (\"return%j\", \"object expected\");\n    var oneofs = mtype.oneofsArray,\n        seenFirstField = {};\n    if (oneofs.length) gen\n    (\"var p={}\");\n\n    for (var i = 0; i < /* initializes */ mtype.fieldsArray.length; ++i) {\n        var field = mtype._fieldsArray[i].resolve(),\n            ref   = \"m\" + util.safeProp(field.name);\n\n        if (field.optional) gen\n        (\"if(%s!=null&&m.hasOwnProperty(%j)){\", ref, field.name); // !== undefined && !== null\n\n        // map fields\n        if (field.map) { gen\n            (\"if(!util.isObject(%s))\", ref)\n                (\"return%j\", invalid(field, \"object\"))\n            (\"var k=Object.keys(%s)\", ref)\n            (\"for(var i=0;i<k.length;++i){\");\n                genVerifyKey(gen, field, \"k[i]\");\n                genVerifyValue(gen, field, i, ref + \"[k[i]]\")\n            (\"}\");\n\n        // repeated fields\n        } else if (field.repeated) {\n          var arrayRef = ref;\n          if (field.useToArray()) {\n            arrayRef = \"array\" + field.id;\n            gen(\"var %s\", arrayRef);\n            gen(\"if (%s!=null&&%s.toArray) { %s = %s.toArray() } else { %s = %s }\",\n                ref, ref, arrayRef, ref, arrayRef, ref);\n          }\n          gen\n            (\"if(!Array.isArray(%s))\", arrayRef)\n                (\"return%j\", invalid(field, \"array\"))\n            (\"for(var i=0;i<%s.length;++i){\", arrayRef);\n                if (field.preEncoded()) {\n                  gen(\"if (!(%s instanceof Uint8Array)) {\", arrayRef + \"[i]\")\n                }\n                genVerifyValue(gen, field, i, arrayRef + \"[i]\")\n                if (field.preEncoded()) {\n                  gen(\"}\");\n                }\n            gen(\"}\");\n\n        // required or present fields\n        } else {\n            if (field.partOf) {\n                var oneofProp = util.safeProp(field.partOf.name);\n                if (seenFirstField[field.partOf.name] === 1) gen\n            (\"if(p%s===1)\", oneofProp)\n                (\"return%j\", field.partOf.name + \": multiple values\");\n                seenFirstField[field.partOf.name] = 1;\n                gen\n            (\"p%s=1\", oneofProp);\n            }\n            genVerifyValue(gen, field, i, ref);\n        }\n        if (field.optional) gen\n        (\"}\");\n    }\n    return gen\n    (\"return null\");\n    /* eslint-enable no-unexpected-multiline */\n}", "\"use strict\";\n\n/**\n * Wrappers for common types.\n * @type {Object.<string,IWrapper>}\n * @const\n */\nvar wrappers = exports;\n\nvar Message = require(21);\n\n/**\n * From object converter part of an {@link IWrapper}.\n * @typedef WrapperFromObjectConverter\n * @type {function}\n * @param {Object.<string,*>} object Plain object\n * @returns {Message<{}>} Message instance\n * @this Type\n */\n\n/**\n * To object converter part of an {@link IWrapper}.\n * @typedef WrapperToObjectConverter\n * @type {function}\n * @param {Message<{}>} message Message instance\n * @param {IConversionOptions} [options] Conversion options\n * @returns {Object.<string,*>} Plain object\n * @this Type\n */\n\n/**\n * Common type wrapper part of {@link wrappers}.\n * @interface IWrapper\n * @property {WrapperFromObjectConverter} [fromObject] From object converter\n * @property {WrapperToObjectConverter} [toObject] To object converter\n */\n\n// Custom wrapper for Any\nwrappers[\".google.protobuf.Any\"] = {\n\n    fromObject: function(object) {\n\n        // unwrap value type if mapped\n        if (object && object[\"@type\"]) {\n            var type = this.lookup(object[\"@type\"]);\n            /* istanbul ignore else */\n            if (type) {\n                // type_url does not accept leading \".\"\n                var type_url = object[\"@type\"].charAt(0) === \".\" ?\n                    object[\"@type\"].substr(1) : object[\"@type\"];\n                // type_url prefix is optional, but path seperator is required\n                return this.create({\n                    type_url: \"/\" + type_url,\n                    value: type.encode(type.fromObject(object)).finish()\n                });\n            }\n        }\n\n        return this.fromObject(object);\n    },\n\n    toObject: function(message, options) {\n\n        // decode value if requested and unmapped\n        if (options && options.json && message.type_url && message.value) {\n            // Only use fully qualified type name after the last '/'\n            var name = message.type_url.substring(message.type_url.lastIndexOf(\"/\") + 1);\n            var type = this.lookup(name);\n            /* istanbul ignore else */\n            if (type)\n                message = type.decode(message.value);\n        }\n\n        // wrap value if unmapped\n        if (!(message instanceof this.ctor) && message instanceof Message) {\n            var object = message.$type.toObject(message, options);\n            object[\"@type\"] = message.$type.fullName;\n            return object;\n        }\n\n        return this.toObject(message, options);\n    }\n};\n", "\"use strict\";\nmodule.exports = Writer;\n\nvar util      = require(39);\n\nvar BufferWriter; // cyclic\n\nvar LongBits  = util.LongBits,\n    base64    = util.base64,\n    utf8      = util.utf8;\n\n/**\n * Constructs a new writer operation instance.\n * @classdesc Scheduled writer operation.\n * @constructor\n * @param {function(*, Uint8Array, number)} fn Function to call\n * @param {number} len Value byte length\n * @param {*} val Value to write\n * @ignore\n */\nfunction Op(fn, len, val) {\n\n    /**\n     * Function to call.\n     * @type {function(Uint8Array, number, *)}\n     */\n    this.fn = fn;\n\n    /**\n     * Value byte length.\n     * @type {number}\n     */\n    this.len = len;\n\n    /**\n     * Next operation.\n     * @type {Writer.Op|undefined}\n     */\n    this.next = undefined;\n\n    /**\n     * Value to write.\n     * @type {*}\n     */\n    this.val = val; // type varies\n}\n\n/* istanbul ignore next */\nfunction noop() {} // eslint-disable-line no-empty-function\n\n/**\n * Constructs a new writer state instance.\n * @classdesc Copied writer state.\n * @memberof Writer\n * @constructor\n * @param {Writer} writer Writer to copy state from\n * @ignore\n */\nfunction State(writer) {\n\n    /**\n     * Current head.\n     * @type {Writer.Op}\n     */\n    this.head = writer.head;\n\n    /**\n     * Current tail.\n     * @type {Writer.Op}\n     */\n    this.tail = writer.tail;\n\n    /**\n     * Current buffer length.\n     * @type {number}\n     */\n    this.len = writer.len;\n\n    /**\n     * Next state.\n     * @type {State|null}\n     */\n    this.next = writer.states;\n}\n\n/**\n * Constructs a new writer instance.\n * @classdesc Wire format writer using `Uint8Array` if available, otherwise `Array`.\n * @constructor\n */\nfunction Writer() {\n\n    /**\n     * Current length.\n     * @type {number}\n     */\n    this.len = 0;\n\n    /**\n     * Operations head.\n     * @type {Object}\n     */\n    this.head = new Op(noop, 0, 0);\n\n    /**\n     * Operations tail\n     * @type {Object}\n     */\n    this.tail = this.head;\n\n    /**\n     * Linked forked states.\n     * @type {Object|null}\n     */\n    this.states = null;\n\n    // When a value is written, the writer calculates its byte length and puts it into a linked\n    // list of operations to perform when finish() is called. This both allows us to allocate\n    // buffers of the exact required size and reduces the amount of work we have to do compared\n    // to first calculating over objects and then encoding over objects. In our case, the encoding\n    // part is just a linked list walk calling operations with already prepared values.\n}\n\n/**\n * Creates a new writer.\n * @function\n * @returns {BufferWriter|Writer} A {@link BufferWriter} when Buffers are supported, otherwise a {@link Writer}\n */\nWriter.create = util.Buffer\n    ? function create_buffer_setup() {\n        return (Writer.create = function create_buffer() {\n            return new BufferWriter();\n        })();\n    }\n    /* istanbul ignore next */\n    : function create_array() {\n        return new Writer();\n    };\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Uint8Array} Buffer\n */\nWriter.alloc = function alloc(size) {\n    return new util.Array(size);\n};\n\n// Use Uint8Array buffer pool in the browser, just like node does with buffers\n/* istanbul ignore else */\nif (util.Array !== Array)\n    Writer.alloc = util.pool(Writer.alloc, util.Array.prototype.subarray);\n\n/**\n * Pushes a new operation to the queue.\n * @param {function(Uint8Array, number, *)} fn Function to call\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @returns {Writer} `this`\n * @private\n */\nWriter.prototype._push = function push(fn, len, val) {\n    this.tail = this.tail.next = new Op(fn, len, val);\n    this.len += len;\n    return this;\n};\n\nfunction writeByte(val, buf, pos) {\n    buf[pos] = val & 255;\n}\n\nfunction writeVarint32(val, buf, pos) {\n    while (val > 127) {\n        buf[pos++] = val & 127 | 128;\n        val >>>= 7;\n    }\n    buf[pos] = val;\n}\n\n/**\n * Constructs a new varint writer operation instance.\n * @classdesc Scheduled varint writer operation.\n * @extends Op\n * @constructor\n * @param {number} len Value byte length\n * @param {number} val Value to write\n * @ignore\n */\nfunction VarintOp(len, val) {\n    this.len = len;\n    this.next = undefined;\n    this.val = val;\n}\n\nVarintOp.prototype = Object.create(Op.prototype);\nVarintOp.prototype.fn = writeVarint32;\n\n/**\n * Writes an unsigned 32 bit value as a varint.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.uint32 = function write_uint32(value) {\n    // here, the call to this.push has been inlined and a varint specific Op subclass is used.\n    // uint32 is by far the most frequently used operation and benefits significantly from this.\n    this.len += (this.tail = this.tail.next = new VarintOp(\n        (value = value >>> 0)\n                < 128       ? 1\n        : value < 16384     ? 2\n        : value < 2097152   ? 3\n        : value < 268435456 ? 4\n        :                     5,\n    value)).len;\n    return this;\n};\n\n/**\n * Writes a signed 32 bit value as a varint.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.int32 = function write_int32(value) {\n    return value < 0\n        ? this._push(writeVarint64, 10, LongBits.fromNumber(value)) // 10 bytes per spec\n        : this.uint32(value);\n};\n\n/**\n * Writes a 32 bit value as a varint, zig-zag encoded.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sint32 = function write_sint32(value) {\n    return this.uint32((value << 1 ^ value >> 31) >>> 0);\n};\n\nfunction writeVarint64(val, buf, pos) {\n    while (val.hi) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = (val.lo >>> 7 | val.hi << 25) >>> 0;\n        val.hi >>>= 7;\n    }\n    while (val.lo > 127) {\n        buf[pos++] = val.lo & 127 | 128;\n        val.lo = val.lo >>> 7;\n    }\n    buf[pos++] = val.lo;\n}\n\n/**\n * Writes an unsigned 64 bit value as a varint.\n * @param {number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.uint64 = function write_uint64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a signed 64 bit value as a varint.\n * @function\n * @param {number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.int64 = Writer.prototype.uint64;\n\n/**\n * Writes a signed 64 bit value as a varint, zig-zag encoded.\n * @param {number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sint64 = function write_sint64(value) {\n    var bits = LongBits.from(value).zzEncode();\n    return this._push(writeVarint64, bits.length(), bits);\n};\n\n/**\n * Writes a boolish value as a varint.\n * @param {boolean} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bool = function write_bool(value) {\n    return this._push(writeByte, 1, value ? 1 : 0);\n};\n\nfunction writeFixed32(val, buf, pos) {\n    buf[pos    ] =  val         & 255;\n    buf[pos + 1] =  val >>> 8   & 255;\n    buf[pos + 2] =  val >>> 16  & 255;\n    buf[pos + 3] =  val >>> 24;\n}\n\n/**\n * Writes an unsigned 32 bit value as fixed 32 bits.\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.fixed32 = function write_fixed32(value) {\n    return this._push(writeFixed32, 4, value >>> 0);\n};\n\n/**\n * Writes a signed 32 bit value as fixed 32 bits.\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.sfixed32 = Writer.prototype.fixed32;\n\n/**\n * Writes an unsigned 64 bit value as fixed 64 bits.\n * @param {number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.fixed64 = function write_fixed64(value) {\n    var bits = LongBits.from(value);\n    return this._push(writeFixed32, 4, bits.lo)._push(writeFixed32, 4, bits.hi);\n};\n\n/**\n * Writes a signed 64 bit value as fixed 64 bits.\n * @function\n * @param {number|string} value Value to write\n * @returns {Writer} `this`\n * @throws {TypeError} If `value` is a string and no long library is present.\n */\nWriter.prototype.sfixed64 = Writer.prototype.fixed64;\n\n/**\n * Writes a float (32 bit).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.float = function write_float(value) {\n    return this._push(util.float.writeFloatLE, 4, value);\n};\n\n/**\n * Writes a double (64 bit float).\n * @function\n * @param {number} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.double = function write_double(value) {\n    return this._push(util.float.writeDoubleLE, 8, value);\n};\n\nvar writeBytes = util.Array.prototype.set\n    ? function writeBytes_set(val, buf, pos) {\n        buf.set(val, pos); // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytes_for(val, buf, pos) {\n        for (var i = 0; i < val.length; ++i)\n            buf[pos + i] = val[i];\n    };\n\n/**\n * Writes a sequence of bytes.\n * @param {Uint8Array|string} value Buffer or base64 encoded string to write\n * @returns {Writer} `this`\n */\nWriter.prototype.bytes = function write_bytes(value) {\n    var len = value.length >>> 0;\n    if (!len)\n        return this._push(writeByte, 1, 0);\n    if (util.isString(value)) {\n        var buf = Writer.alloc(len = base64.length(value));\n        base64.decode(value, buf, 0);\n        value = buf;\n    }\n    return this.uint32(len)._push(writeBytes, len, value);\n};\n\n/**\n * Writes a string.\n * @param {string} value Value to write\n * @returns {Writer} `this`\n */\nWriter.prototype.string = function write_string(value) {\n    var len = utf8.length(value);\n    return len\n        ? this.uint32(len)._push(utf8.write, len, value)\n        : this._push(writeByte, 1, 0);\n};\n\n/**\n * Forks this writer's state by pushing it to a stack.\n * Calling {@link Writer#reset|reset} or {@link Writer#ldelim|ldelim} resets the writer to the previous state.\n * @returns {Writer} `this`\n */\nWriter.prototype.fork = function fork() {\n    this.states = new State(this);\n    this.head = this.tail = new Op(noop, 0, 0);\n    this.len = 0;\n    return this;\n};\n\n/**\n * Resets this instance to the last state.\n * @returns {Writer} `this`\n */\nWriter.prototype.reset = function reset() {\n    if (this.states) {\n        this.head   = this.states.head;\n        this.tail   = this.states.tail;\n        this.len    = this.states.len;\n        this.states = this.states.next;\n    } else {\n        this.head = this.tail = new Op(noop, 0, 0);\n        this.len  = 0;\n    }\n    return this;\n};\n\n/**\n * Resets to the last state and appends the fork state's current write length as a varint followed by its operations.\n * @returns {Writer} `this`\n */\nWriter.prototype.ldelim = function ldelim() {\n    var head = this.head,\n        tail = this.tail,\n        len  = this.len;\n    this.reset().uint32(len);\n    if (len) {\n        this.tail.next = head.next; // skip noop\n        this.tail = tail;\n        this.len += len;\n    }\n    return this;\n};\n\n/**\n * Finishes the write operation.\n * @returns {Uint8Array} Finished buffer\n */\nWriter.prototype.finish = function finish() {\n    var head = this.head.next, // skip noop\n        buf  = this.constructor.alloc(this.len),\n        pos  = 0;\n    while (head) {\n        head.fn(head.val, buf, pos);\n        pos += head.len;\n        head = head.next;\n    }\n    // this.head = this.tail = null;\n    return buf;\n};\n\nWriter._configure = function(BufferWriter_) {\n    BufferWriter = BufferWriter_;\n};\n", "\"use strict\";\nmodule.exports = <PERSON><PERSON>erWriter;\n\n// extends Writer\nvar Writer = require(42);\n(BufferWriter.prototype = Object.create(Writer.prototype)).constructor = BufferWriter;\n\nvar util = require(39);\n\nvar Buffer = util.Buffer;\n\n/**\n * Constructs a new buffer writer instance.\n * @classdesc Wire format writer using node buffers.\n * @extends Writer\n * @constructor\n */\nfunction BufferWriter() {\n    Writer.call(this);\n}\n\n/**\n * Allocates a buffer of the specified size.\n * @param {number} size Buffer size\n * @returns {Buffer} Buffer\n */\nBufferWriter.alloc = function alloc_buffer(size) {\n    return (BufferWriter.alloc = util._Buffer_allocUnsafe)(size);\n};\n\nvar writeBytesBuffer = Buffer && Buffer.prototype instanceof Uint8Array && Buffer.prototype.set.name === \"set\"\n    ? function writeBytesBuffer_set(val, buf, pos) {\n        buf.set(val, pos); // faster than copy (requires node >= 4 where Buffers extend Uint8Array and set is properly inherited)\n                           // also works for plain array values\n    }\n    /* istanbul ignore next */\n    : function writeBytesBuffer_copy(val, buf, pos) {\n        if (val.copy) // Buffer values\n            val.copy(buf, pos, 0, val.length);\n        else for (var i = 0; i < val.length;) // plain array values\n            buf[pos++] = val[i++];\n    };\n\n/**\n * @override\n */\nBufferWriter.prototype.bytes = function write_bytes_buffer(value) {\n    if (util.isString(value))\n        value = util._Buffer_from(value, \"base64\");\n    var len = value.length >>> 0;\n    this.uint32(len);\n    if (len)\n        this._push(writeBytesBuffer, len, value);\n    return this;\n};\n\nfunction writeStringBuffer(val, buf, pos) {\n    if (val.length < 40) // plain js is faster for short strings (probably due to redundant assertions)\n        util.utf8.write(val, buf, pos);\n    else\n        buf.utf8Write(val, pos);\n}\n\n/**\n * @override\n */\nBufferWriter.prototype.string = function write_string_buffer(value) {\n    var len = Buffer.byteLength(value);\n    this.uint32(len);\n    if (len)\n        this._push(writeStringBuffer, len, value);\n    return this;\n};\n\n\n/**\n * Finishes the write operation.\n * @name BufferWriter#finish\n * @function\n * @returns {Buffer} Finished buffer\n */\n"], "sourceRoot": "."}