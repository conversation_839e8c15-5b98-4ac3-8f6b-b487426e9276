// backend/prisma/schema.prisma - SQLite Compatible
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      String   @default("STUDENT") // STUDENT, INSTRUCTOR, ADMIN
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  profile           UserProfile?
  enrolledCourses   CourseProgress[]
  instructedCourses Course[]
  achievements      UserAchievement[]
  quizAttempts      QuizAttempt[]

  @@map("users")
}

model UserProfile {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  avatar String?
  bio    String?
  level  String  @default("مبتدئ") // مبتدئ، متوسط، متقدم، خبير
  xp     Int     @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("user_profiles")
}

model Course {
  id          String   @id @default(cuid())
  title       String
  description String
  level       String   @default("L1") // L1, L2, L3
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  instructorId String
  instructor   User   @relation(fields: [instructorId], references: [id])

  modules  Module[]
  students CourseProgress[]

  @@map("courses")
}

model Module {
  id          String   @id @default(cuid())
  title       String
  description String
  order       Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  courseId String
  course   Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  lessons Lesson[]

  @@map("modules")
}

model Lesson {
  id       String   @id @default(cuid())
  title    String
  content  String   // HTML content or markdown
  type     String   @default("TEXT") // VIDEO, TEXT, INTERACTIVE, SIMULATION
  duration Int      @default(15) // in minutes
  order    Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  moduleId String
  module   Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  quizzes Quiz[]

  @@map("lessons")
}

model Quiz {
  id        String   @id @default(cuid())
  title     String
  timeLimit Int?     // in minutes
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  lessonId String
  lesson   Lesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  questions Question[]
  attempts  QuizAttempt[]

  @@map("quizzes")
}

model Question {
  id            String   @id @default(cuid())
  text          String
  type          String   @default("MULTIPLE_CHOICE") // MULTIPLE_CHOICE, TRUE_FALSE, CALCULATION, ESSAY
  options       String?  // JSON string for multiple choice options
  correctAnswer String
  explanation   String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  quizId String
  quiz   Quiz   @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@map("questions")
}

model CourseProgress {
  id               String   @id @default(cuid())
  userId           String
  courseId         String
  completedLessons String?  @default("[]") // JSON string of completed lesson IDs
  currentModule    String?
  score            Float?
  lastAccessed     DateTime @default(now())
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@map("course_progress")
}

model Achievement {
  id          String   @id @default(cuid())
  title       String
  description String
  icon        String
  xpReward    Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  users UserAchievement[]

  @@map("achievements")
}

model UserAchievement {
  id           String   @id @default(cuid())
  userId       String
  achievementId String
  unlockedAt   DateTime @default(now())

  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  achievement Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
  @@map("user_achievements")
}

model QuizAttempt {
  id        String   @id @default(cuid())
  userId    String
  quizId    String
  answers   String   @default("[]") // JSON string of answers
  score     Float
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@map("quiz_attempts")
}
