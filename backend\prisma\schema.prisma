// backend/prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String
  role      UserRole @default(STUDENT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  profile           UserProfile?
  enrolledCourses   CourseProgress[]
  instructedCourses Course[]
  achievements      UserAchievement[]
  quizAttempts      QuizAttempt[]

  @@map("users")
}

model UserProfile {
  id     String @id @default(cuid())
  userId String @unique
  avatar String?
  bio    String?
  level  String @default("مبتدئ")
  xp     Int    @default(0)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_profiles")
}

model Course {
  id          String      @id @default(cuid())
  title       String
  description String
  level       CourseLevel
  instructorId String
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  instructor User             @relation(fields: [instructorId], references: [id])
  modules    Module[]
  students   CourseProgress[]

  @@map("courses")
}

model Module {
  id          String @id @default(cuid())
  title       String
  description String
  order       Int
  courseId    String

  course  Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
  lessons Lesson[]

  @@map("modules")
}

model Lesson {
  id       String     @id @default(cuid())
  title    String
  content  String
  type     LessonType
  duration Int // في الدقائق
  order    Int
  moduleId String

  module  Module @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  quizzes Quiz[]

  @@map("lessons")
}

model Quiz {
  id        String @id @default(cuid())
  title     String
  lessonId  String
  timeLimit Int? // في الدقائق

  lesson    Lesson        @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  questions Question[]
  attempts  QuizAttempt[]

  @@map("quizzes")
}

model Question {
  id            String       @id @default(cuid())
  text          String
  type          QuestionType
  options       String[] // للأسئلة متعددة الخيارات
  correctAnswer String
  explanation   String?
  quizId        String

  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@map("questions")
}

model CourseProgress {
  id               String   @id @default(cuid())
  userId           String
  courseId         String
  completedLessons String[] // IDs of completed lessons
  currentModule    String?
  score            Float?
  lastAccessed     DateTime @default(now())

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@map("course_progress")
}

model Achievement {
  id          String @id @default(cuid())
  title       String
  description String
  icon        String
  xpReward    Int

  users UserAchievement[]

  @@map("achievements")
}

model UserAchievement {
  id            String   @id @default(cuid())
  userId        String
  achievementId String
  unlockedAt    DateTime @default(now())

  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  achievement Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
  @@map("user_achievements")
}

model QuizAttempt {
  id        String   @id @default(cuid())
  userId    String
  quizId    String
  answers   String[] // JSON array of answers
  score     Float
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@map("quiz_attempts")
}

enum UserRole {
  STUDENT
  INSTRUCTOR
  ADMIN
}

enum CourseLevel {
  L1
  L2
  L3
}

enum LessonType {
  VIDEO
  TEXT
  INTERACTIVE
  SIMULATION
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  CALCULATION
  ESSAY
}
