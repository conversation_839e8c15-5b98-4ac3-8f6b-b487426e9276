"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMaxRecursiveSelectionsRule = exports.DEFAULT_MAX_RECURSIVE_SELECTIONS = exports.NoIntrospection = void 0;
var NoIntrospection_js_1 = require("./NoIntrospection.js");
Object.defineProperty(exports, "NoIntrospection", { enumerable: true, get: function () { return NoIntrospection_js_1.NoIntrospection; } });
var RecursiveSelectionsLimit_js_1 = require("./RecursiveSelectionsLimit.js");
Object.defineProperty(exports, "DEFAULT_MAX_RECURSIVE_SELECTIONS", { enumerable: true, get: function () { return RecursiveSelectionsLimit_js_1.DEFAULT_MAX_RECURSIVE_SELECTIONS; } });
Object.defineProperty(exports, "createMaxRecursiveSelectionsRule", { enumerable: true, get: function () { return RecursiveSelectionsLimit_js_1.createMaxRecursiveSelectionsRule; } });
//# sourceMappingURL=index.js.map