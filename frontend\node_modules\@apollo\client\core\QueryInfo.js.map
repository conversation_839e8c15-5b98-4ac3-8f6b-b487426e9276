{"version": 3, "file": "QueryInfo.js", "sourceRoot": "", "sources": ["../../src/core/QueryInfo.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAGtC,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACnD,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAI7D,OAAO,EACL,eAAe,EACf,qBAAqB,EACrB,aAAa,GACd,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAgBnD,IAAM,uBAAuB,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAGhE,CAAC;AAEJ,SAAS,0BAA0B,CACjC,KAAuB,EACvB,UAAwC;IAExC,IAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;IACnC,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;QACnC,kEAAkE;QAClE,KAAK,CAAC,UAAU,CAAC,GAAG;YAClB,uBAAuB,CAAC,GAAG,CACzB,KAAK;YACL,8DAA8D;YAC9D,+DAA+D;YAC/D,6DAA6D;YAC7D,sDAAsD;YACtD,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CACjD,CAAC;YACF,kEAAkE;YAClE,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;AACH,CAAC;AAED,8DAA8D;AAC9D,qEAAqE;AACrE,0EAA0E;AAC1E,0EAA0E;AAC1E,sEAAsE;AACtE,iEAAiE;AACjE,iEAAiE;AACjE,yEAAyE;AACzE,uEAAuE;AACvE,wEAAwE;AACxE,yEAAyE;AACzE,8DAA8D;AAC9D;IAYE,mBACE,YAA+B,EACf,OAAwC;QAAxC,wBAAA,EAAA,UAAU,YAAY,CAAC,eAAe,EAAE;QAAxC,YAAO,GAAP,OAAO,CAAiC;QAb1D,aAAQ,GAAwB,IAAI,CAAC;QACrC,kBAAa,GAAG,CAAC,CAAC;QAKlB,YAAO,GAAG,KAAK,CAAC;QA8IA,oBAAe,GAAqC,IAAI,CAAC;QArIvE,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;QAEhD,mEAAmE;QACnE,gEAAgE;QAChE,sEAAsE;QACtE,mEAAmE;QACnE,oDAAoD;QACpD,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YACxC,uBAAuB,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACtC,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC3C,0BAA0B,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAC5C,0BAA0B,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEM,wBAAI,GAAX,UAAY,KASX;QACC,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,IAAI,aAAa,CAAC,OAAO,CAAC;QACjE,IACE,IAAI,CAAC,SAAS;YACd,IAAI,CAAC,aAAa,KAAK,aAAa,CAAC,OAAO;YAC5C,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,EACvC,CAAC;YACD,aAAa,GAAG,aAAa,CAAC,YAAY,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5C,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;YACvB,sEAAsE;YACtE,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE;YAClB,QAAQ,EAAE,KAAK,CAAC,QAAQ;YACxB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,YAAY,EAAE,IAAI;YAClB,aAAa,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE;YACvC,aAAa,eAAA;SACd,CAAC,CAAC;QAEH,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;QAC3C,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,6BAAS,GAAT;QACE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;IACzB,CAAC;IAED,2BAAO,GAAP;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEjC,IAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;QAChC,IAAI,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;YAChD,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;QAC7B,CAAC;QAED,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACtC,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAOO,kCAAc,GAAtB,UACE,IAAkC,EAClC,OAA2B;QAE3B,IAAI,CAAC,QAAQ;YACX,IAAI,CAAC,CAAC;gBACJ;oBACE,IAAI,MAAA;oBACJ,OAAO,EAAE,OAAO,IAAI,IAAI,CAAC,cAAc,EAAE;iBAC1C;gBACH,CAAC,CAAC,KAAK,CAAC,CAAC;IACb,CAAC;IAEO,kCAAc,GAAtB,UAAuB,SAA0B;;QAA1B,0BAAA,EAAA,YAAY,IAAI,CAAC,SAAS;QAC/C,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,QAAS;YACrB,SAAS,WAAA;YACT,iBAAiB,EAAE,IAAI;YACvB,UAAU,EAAE,IAAI;YAChB,eAAe,EAAE,MAAA,IAAI,CAAC,eAAe,0CAAE,OAAO,CAAC,eAAe;SAC/D,CAAC;IACJ,CAAC;IAED,2BAAO,GAAP,UAAQ,IAAkC;;QACxC,IAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QAEpD,mEAAmE;QACnE,4EAA4E;QAC5E,2EAA2E;QAC3E,kEAAkE;QAClE,0EAA0E;QAC1E,SAAS;QACT,EAAE;QACF,2EAA2E;QAC3E,6BAA6B;QAC7B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAI,MAAA,IAAI,CAAC,eAAe,0CAAE,YAAY,EAAE,CAAA,EAAE,CAAC;YACnE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAE1B,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3D,MAAA,IAAI,CAAC,eAAe,0CAAG,gBAAgB,GAAG,CAAC;QAC7C,CAAC;IACH,CAAC;IAGD,sCAAkB,GAAlB,UAAmB,EAAoC;QACrD,IAAI,EAAE,KAAK,IAAI,CAAC,eAAe;YAAE,OAAO;QACvC,IAAY,CAAC,eAAe,GAAG,EAAE,CAAC;QACnC,IAAI,EAAE,EAAE,CAAC;YACP,EAAE,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;QACzB,CAAC;IACH,CAAC;IAEM,wBAAI,GAAX;;QACE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YAEpB,oCAAoC;YACpC,MAAA,IAAI,CAAC,eAAe,0CAAG,oBAAoB,GAAG,CAAC;YAC/C,IAAI,CAAC,MAAM,EAAE,CAAC;YAEd,IAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;YAChC,IAAI,EAAE;gBAAE,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3B,CAAC;IACH,CAAC;IAEO,0BAAM,GAAd;;QACE,MAAA,IAAI,CAAC,WAAW,oDAAI,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;IAC5B,CAAC;IAIO,+BAAW,GAAnB,UAAoB,SAA0B;QAA9C,iBAmBC;QAnBmB,0BAAA,EAAA,YAAY,IAAI,CAAC,SAAS;QAC5C,IAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;QAChC,IAAI,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;YAChD,OAAO;QACT,CAAC;QAED,IAAM,YAAY,yBAIb,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,KACjC,OAAO,EAAE,IAAI,EACb,QAAQ,EAAE,UAAC,IAAI,IAAK,OAAA,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAlB,CAAkB,GACvC,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5D,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAQM,kCAAc,GAArB;QACE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;IAC1B,CAAC;IAEO,+BAAW,GAAnB,UACE,MAAwB,EACxB,SAAyC;QAEjC,IAAA,SAAS,GAAK,IAAI,UAAT,CAAU;QAC3B,OAAO,CAAC,CACN,SAAS;YACT,mEAAmE;YACnE,iEAAiE;YACjE,0CAA0C;YAC1C,SAAS,CAAC,OAAO,KAAK,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;YAC7D,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC;YACrC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAC1C,CAAC;IACJ,CAAC;IAEM,8BAAU,GAAjB,UACE,MAAsB,EACtB,QAAsB,EACtB,OAGC,EACD,kBAAsC;QAPxC,iBAkIC;;QAzHC,IAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,IAAM,aAAa,GACjB,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAE/D,iFAAiF;QACjF,+EAA+E;QAC/E,MAAA,IAAI,CAAC,eAAe,0CAAG,oBAAoB,GAAG,CAAC;QAE/C,IAAI,aAAa,IAAI,MAAM,IAAI,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YACnE,IAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACvE,MAAM,CAAC,IAAI,GAAG,UAAU,CAAC;YAEzB,wEAAwE;YACxE,qEAAqE;YACrE,wEAAwE;YACxE,wEAAwE;YACxE,yDAAyD;QAC3D,CAAC;aAAM,IAAI,SAAS,IAAI,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACjD,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,IAAI,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;YACvC,IAAI,CAAC,cAAc,CACjB,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EACvC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CACvC,CAAC;QACJ,CAAC;aAAM,IAAI,kBAAkB,sCAA8B,EAAE,CAAC;YAC5D,IAAI,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;gBACnD,kEAAkE;gBAClE,kEAAkE;gBAClE,iEAAiE;gBACjE,6DAA6D;gBAC7D,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,UAAC,KAAK;oBAClC,IAAI,KAAI,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;wBAChD,KAAK,CAAC,UAAU,CAAC;4BACf,KAAK,EAAE,QAAQ;4BACf,IAAI,EAAE,MAAM,CAAC,IAAmB;4BAChC,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,SAAS,EAAE,kBAAkB,yCAAiC;yBAC/D,CAAC,CAAC;wBAEH,KAAI,CAAC,SAAS,GAAG;4BACf,MAAM,QAAA;4BACN,SAAS,EAAE,OAAO,CAAC,SAAS;4BAC5B,OAAO,EAAE,uBAAuB,CAAC,GAAG,CAAC,KAAI,CAAC,KAAK,CAAC;yBACjD,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,4DAA4D;wBAC5D,2DAA2D;wBAC3D,2DAA2D;wBAC3D,yDAAyD;wBACzD,2DAA2D;wBAC3D,2DAA2D;wBAC3D,8DAA8D;wBAC9D,6DAA6D;wBAC7D,2DAA2D;wBAC3D,6DAA6D;wBAC7D,2DAA2D;wBAC3D,6DAA6D;wBAC7D,2DAA2D;wBAC3D,2DAA2D;wBAC3D,wDAAwD;wBACxD,6DAA6D;wBAC7D,wDAAwD;wBACxD,4DAA4D;wBAC5D,0DAA0D;wBAC1D,2DAA2D;wBAC3D,6DAA6D;wBAC7D,4DAA4D;wBAC5D,6DAA6D;wBAC7D,4DAA4D;wBAC5D,6DAA6D;wBAC7D,8DAA8D;wBAC9D,wDAAwD;wBACxD,6DAA6D;wBAC7D,yDAAyD;wBACzD,4DAA4D;wBAC5D,uDAAuD;wBACvD,oDAAoD;wBACpD,IAAI,KAAI,CAAC,QAAQ,IAAI,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;4BACjD,wDAAwD;4BACxD,2BAA2B;4BAC3B,MAAM,CAAC,IAAI,GAAG,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;4BACxC,OAAO;wBACT,CAAC;wBACD,4DAA4D;wBAC5D,qDAAqD;oBACvD,CAAC;oBAED,IAAM,WAAW,GAAG,KAAI,CAAC,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oBAC3D,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAI,WAAW,CAAC,CAAC;oBAExC,2DAA2D;oBAC3D,gEAAgE;oBAChE,gEAAgE;oBAChE,sEAAsE;oBACtE,iEAAiE;oBACjE,UAAU;oBACV,IAAI,CAAC,KAAI,CAAC,OAAO,IAAI,KAAK,CAAC,KAAI,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;wBAC9D,4DAA4D;wBAC5D,yCAAyC;wBACzC,KAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;oBACtC,CAAC;oBAED,4DAA4D;oBAC5D,+DAA+D;oBAC/D,6DAA6D;oBAC7D,+DAA+D;oBAC/D,mDAAmD;oBACnD,KAAI,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;oBACvC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAClB,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;oBAC5B,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEM,6BAAS,GAAhB;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC;IAEM,6BAAS,GAAhB,UAAiB,KAAkB;;QACjC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;QAExB,MAAA,IAAI,CAAC,eAAe,0CAAG,oBAAoB,GAAG,CAAC;QAE/C,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;QAC3C,CAAC;QAED,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;QACzC,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IACH,gBAAC;AAAD,CAAC,AA1XD,IA0XC;;AAED,MAAM,UAAU,iBAAiB,CAC/B,MAAsB,EACtB,WAAiC;IAAjC,4BAAA,EAAA,oBAAiC;IAEjC,IAAM,YAAY,GAAG,WAAW,KAAK,QAAQ,IAAI,WAAW,KAAK,KAAK,CAAC;IACvE,IAAI,eAAe,GAAG,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;IACrD,IAAI,CAAC,eAAe,IAAI,YAAY,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QACpD,eAAe,GAAG,IAAI,CAAC;IACzB,CAAC;IACD,OAAO,eAAe,CAAC;AACzB,CAAC", "sourcesContent": ["import type { DocumentNode, GraphQLFormattedError } from \"graphql\";\nimport { equal } from \"@wry/equality\";\n\nimport type { Cache, ApolloCache } from \"../cache/index.js\";\nimport { DeepMerger } from \"../utilities/index.js\";\nimport { mergeIncrementalData } from \"../utilities/index.js\";\nimport type { WatchQueryOptions, ErrorPolicy } from \"./watchQueryOptions.js\";\nimport type { ObservableQuery } from \"./ObservableQuery.js\";\nimport type { FetchResult } from \"../link/core/index.js\";\nimport {\n  isNonEmptyArray,\n  graphQLResultHasError,\n  canUseWeakMap,\n} from \"../utilities/index.js\";\nimport { NetworkStatus } from \"./networkStatus.js\";\nimport type { ApolloError } from \"../errors/index.js\";\nimport type { QueryManager } from \"./QueryManager.js\";\nimport type { Unmasked } from \"../masking/index.js\";\n\nexport type QueryStoreValue = Pick<\n  QueryInfo,\n  \"variables\" | \"networkStatus\" | \"networkError\" | \"graphQLErrors\"\n>;\n\nexport const enum CacheWriteBehavior {\n  FORBID,\n  OVERWRITE,\n  MERGE,\n}\n\nconst destructiveMethodCounts = new (canUseWeakMap ? WeakMap : Map)<\n  ApolloCache<any>,\n  number\n>();\n\nfunction wrapDestructiveCacheMethod(\n  cache: ApolloCache<any>,\n  methodName: \"evict\" | \"modify\" | \"reset\"\n) {\n  const original = cache[methodName];\n  if (typeof original === \"function\") {\n    // @ts-expect-error this is just too generic to be typed correctly\n    cache[methodName] = function () {\n      destructiveMethodCounts.set(\n        cache,\n        // The %1e15 allows the count to wrap around to 0 safely every\n        // quadrillion evictions, so there's no risk of overflow. To be\n        // clear, this is more of a pedantic principle than something\n        // that matters in any conceivable practical scenario.\n        (destructiveMethodCounts.get(cache)! + 1) % 1e15\n      );\n      // @ts-expect-error this is just too generic to be typed correctly\n      return original.apply(this, arguments);\n    };\n  }\n}\n\n// A QueryInfo object represents a single query managed by the\n// QueryManager, which tracks all QueryInfo objects by queryId in its\n// this.queries Map. QueryInfo objects store the latest results and errors\n// for the given query, and are responsible for reporting those results to\n// the corresponding ObservableQuery, via the QueryInfo.notify method.\n// Results are reported asynchronously whenever setDiff marks the\n// QueryInfo object as dirty, though a call to the QueryManager's\n// broadcastQueries method may trigger the notification before it happens\n// automatically. This class used to be a simple interface type without\n// any field privacy or meaningful methods, which is why it still has so\n// many public fields. The effort to lock down and simplify the QueryInfo\n// interface is ongoing, and further improvements are welcome.\nexport class QueryInfo {\n  document: DocumentNode | null = null;\n  lastRequestId = 1;\n  variables?: Record<string, any>;\n  networkStatus?: NetworkStatus;\n  networkError?: Error | null;\n  graphQLErrors?: ReadonlyArray<GraphQLFormattedError>;\n  stopped = false;\n\n  private cancelWatch?: () => void;\n  private cache: ApolloCache<any>;\n\n  constructor(\n    queryManager: QueryManager<any>,\n    public readonly queryId = queryManager.generateQueryId()\n  ) {\n    const cache = (this.cache = queryManager.cache);\n\n    // Track how often cache.evict is called, since we want eviction to\n    // override the feud-stopping logic in the markResult method, by\n    // causing shouldWrite to return true. Wrapping the cache.evict method\n    // is a bit of a hack, but it saves us from having to make eviction\n    // counting an official part of the ApolloCache API.\n    if (!destructiveMethodCounts.has(cache)) {\n      destructiveMethodCounts.set(cache, 0);\n      wrapDestructiveCacheMethod(cache, \"evict\");\n      wrapDestructiveCacheMethod(cache, \"modify\");\n      wrapDestructiveCacheMethod(cache, \"reset\");\n    }\n  }\n\n  public init(query: {\n    document: DocumentNode;\n    variables: Record<string, any> | undefined;\n    // The initial networkStatus for this fetch, most often\n    // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n    // or setVariables.\n    networkStatus?: NetworkStatus;\n    observableQuery?: ObservableQuery<any, any>;\n    lastRequestId?: number;\n  }): this {\n    let networkStatus = query.networkStatus || NetworkStatus.loading;\n    if (\n      this.variables &&\n      this.networkStatus !== NetworkStatus.loading &&\n      !equal(this.variables, query.variables)\n    ) {\n      networkStatus = NetworkStatus.setVariables;\n    }\n\n    if (!equal(query.variables, this.variables)) {\n      this.lastDiff = void 0;\n      // Ensure we don't continue to receive cache updates for old variables\n      this.cancel();\n    }\n\n    Object.assign(this, {\n      document: query.document,\n      variables: query.variables,\n      networkError: null,\n      graphQLErrors: this.graphQLErrors || [],\n      networkStatus,\n    });\n\n    if (query.observableQuery) {\n      this.setObservableQuery(query.observableQuery);\n    }\n\n    if (query.lastRequestId) {\n      this.lastRequestId = query.lastRequestId;\n    }\n\n    return this;\n  }\n\n  resetDiff() {\n    this.lastDiff = void 0;\n  }\n\n  getDiff(): Cache.DiffResult<any> {\n    const options = this.getDiffOptions();\n\n    if (this.lastDiff && equal(options, this.lastDiff.options)) {\n      return this.lastDiff.diff;\n    }\n\n    this.updateWatch(this.variables);\n\n    const oq = this.observableQuery;\n    if (oq && oq.options.fetchPolicy === \"no-cache\") {\n      return { complete: false };\n    }\n\n    const diff = this.cache.diff(options);\n    this.updateLastDiff(diff, options);\n    return diff;\n  }\n\n  private lastDiff?: {\n    diff: Cache.DiffResult<any>;\n    options: Cache.DiffOptions;\n  };\n\n  private updateLastDiff(\n    diff: Cache.DiffResult<any> | null,\n    options?: Cache.DiffOptions\n  ) {\n    this.lastDiff =\n      diff ?\n        {\n          diff,\n          options: options || this.getDiffOptions(),\n        }\n      : void 0;\n  }\n\n  private getDiffOptions(variables = this.variables): Cache.DiffOptions {\n    return {\n      query: this.document!,\n      variables,\n      returnPartialData: true,\n      optimistic: true,\n      canonizeResults: this.observableQuery?.options.canonizeResults,\n    };\n  }\n\n  setDiff(diff: Cache.DiffResult<any> | null) {\n    const oldDiff = this.lastDiff && this.lastDiff.diff;\n\n    // If we are trying to deliver an incomplete cache result, we avoid\n    // reporting it if the query has errored, otherwise we let the broadcast try\n    // and repair the partial result by refetching the query. This check avoids\n    // a situation where a query that errors and another succeeds with\n    // overlapping data does not report the partial data result to the errored\n    // query.\n    //\n    // See https://github.com/apollographql/apollo-client/issues/11400 for more\n    // information on this issue.\n    if (diff && !diff.complete && this.observableQuery?.getLastError()) {\n      return;\n    }\n\n    this.updateLastDiff(diff);\n\n    if (!equal(oldDiff && oldDiff.result, diff && diff.result)) {\n      this.observableQuery?.[\"scheduleNotify\"]();\n    }\n  }\n\n  public readonly observableQuery: ObservableQuery<any, any> | null = null;\n  setObservableQuery(oq: ObservableQuery<any, any> | null) {\n    if (oq === this.observableQuery) return;\n    (this as any).observableQuery = oq;\n    if (oq) {\n      oq[\"queryInfo\"] = this;\n    }\n  }\n\n  public stop() {\n    if (!this.stopped) {\n      this.stopped = true;\n\n      // Cancel the pending notify timeout\n      this.observableQuery?.[\"resetNotifications\"]();\n      this.cancel();\n\n      const oq = this.observableQuery;\n      if (oq) oq.stopPolling();\n    }\n  }\n\n  private cancel() {\n    this.cancelWatch?.();\n    this.cancelWatch = void 0;\n  }\n\n  private lastWatch?: Cache.WatchOptions;\n\n  private updateWatch(variables = this.variables) {\n    const oq = this.observableQuery;\n    if (oq && oq.options.fetchPolicy === \"no-cache\") {\n      return;\n    }\n\n    const watchOptions: Cache.WatchOptions = {\n      // Although this.getDiffOptions returns Cache.DiffOptions instead of\n      // Cache.WatchOptions, all the overlapping options should be the same, so\n      // we can reuse getDiffOptions here, for consistency.\n      ...this.getDiffOptions(variables),\n      watcher: this,\n      callback: (diff) => this.setDiff(diff),\n    };\n\n    if (!this.lastWatch || !equal(watchOptions, this.lastWatch)) {\n      this.cancel();\n      this.cancelWatch = this.cache.watch((this.lastWatch = watchOptions));\n    }\n  }\n\n  private lastWrite?: {\n    result: FetchResult<any>;\n    variables: WatchQueryOptions[\"variables\"];\n    dmCount: number | undefined;\n  };\n\n  public resetLastWrite() {\n    this.lastWrite = void 0;\n  }\n\n  private shouldWrite(\n    result: FetchResult<any>,\n    variables: WatchQueryOptions[\"variables\"]\n  ) {\n    const { lastWrite } = this;\n    return !(\n      lastWrite &&\n      // If cache.evict has been called since the last time we wrote this\n      // data into the cache, there's a chance writing this result into\n      // the cache will repair what was evicted.\n      lastWrite.dmCount === destructiveMethodCounts.get(this.cache) &&\n      equal(variables, lastWrite.variables) &&\n      equal(result.data, lastWrite.result.data)\n    );\n  }\n\n  public markResult<T>(\n    result: FetchResult<T>,\n    document: DocumentNode,\n    options: Pick<\n      WatchQueryOptions,\n      \"variables\" | \"fetchPolicy\" | \"errorPolicy\"\n    >,\n    cacheWriteBehavior: CacheWriteBehavior\n  ) {\n    const merger = new DeepMerger();\n    const graphQLErrors =\n      isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n\n    // Cancel the pending notify timeout (if it exists) to prevent extraneous network\n    // requests. To allow future notify timeouts, diff and dirty are reset as well.\n    this.observableQuery?.[\"resetNotifications\"]();\n\n    if (\"incremental\" in result && isNonEmptyArray(result.incremental)) {\n      const mergedData = mergeIncrementalData(this.getDiff().result, result);\n      result.data = mergedData;\n\n      // Detect the first chunk of a deferred query and merge it with existing\n      // cache data. This ensures a `cache-first` fetch policy that returns\n      // partial cache data or a `cache-and-network` fetch policy that already\n      // has full data in the cache does not complain when trying to merge the\n      // initial deferred server data with existing cache data.\n    } else if (\"hasNext\" in result && result.hasNext) {\n      const diff = this.getDiff();\n      result.data = merger.merge(diff.result, result.data);\n    }\n\n    this.graphQLErrors = graphQLErrors;\n\n    if (options.fetchPolicy === \"no-cache\") {\n      this.updateLastDiff(\n        { result: result.data, complete: true },\n        this.getDiffOptions(options.variables)\n      );\n    } else if (cacheWriteBehavior !== CacheWriteBehavior.FORBID) {\n      if (shouldWriteResult(result, options.errorPolicy)) {\n        // Using a transaction here so we have a chance to read the result\n        // back from the cache before the watch callback fires as a result\n        // of writeQuery, so we can store the new diff quietly and ignore\n        // it when we receive it redundantly from the watch callback.\n        this.cache.performTransaction((cache) => {\n          if (this.shouldWrite(result, options.variables)) {\n            cache.writeQuery({\n              query: document,\n              data: result.data as Unmasked<T>,\n              variables: options.variables,\n              overwrite: cacheWriteBehavior === CacheWriteBehavior.OVERWRITE,\n            });\n\n            this.lastWrite = {\n              result,\n              variables: options.variables,\n              dmCount: destructiveMethodCounts.get(this.cache),\n            };\n          } else {\n            // If result is the same as the last result we received from\n            // the network (and the variables match too), avoid writing\n            // result into the cache again. The wisdom of skipping this\n            // cache write is far from obvious, since any cache write\n            // could be the one that puts the cache back into a desired\n            // state, fixing corruption or missing data. However, if we\n            // always write every network result into the cache, we enable\n            // feuds between queries competing to update the same data in\n            // incompatible ways, which can lead to an endless cycle of\n            // cache broadcasts and useless network requests. As with any\n            // feud, eventually one side must step back from the brink,\n            // letting the other side(s) have the last word(s). There may\n            // be other points where we could break this cycle, such as\n            // silencing the broadcast for cache.writeQuery (not a good\n            // idea, since it just delays the feud a bit) or somehow\n            // avoiding the network request that just happened (also bad,\n            // because the server could return useful new data). All\n            // options considered, skipping this cache write seems to be\n            // the least damaging place to break the cycle, because it\n            // reflects the intuition that we recently wrote this exact\n            // result into the cache, so the cache *should* already/still\n            // contain this data. If some other query has clobbered that\n            // data in the meantime, that's too bad, but there will be no\n            // winners if every query blindly reverts to its own version\n            // of the data. This approach also gives the network a chance\n            // to return new data, which will be written into the cache as\n            // usual, notifying only those queries that are directly\n            // affected by the cache updates, as usual. In the future, an\n            // even more sophisticated cache could perhaps prevent or\n            // mitigate the clobbering somehow, but that would make this\n            // particular cache write even less important, and thus\n            // skipping it would be even safer than it is today.\n            if (this.lastDiff && this.lastDiff.diff.complete) {\n              // Reuse data from the last good (complete) diff that we\n              // received, when possible.\n              result.data = this.lastDiff.diff.result;\n              return;\n            }\n            // If the previous this.diff was incomplete, fall through to\n            // re-reading the latest data with cache.diff, below.\n          }\n\n          const diffOptions = this.getDiffOptions(options.variables);\n          const diff = cache.diff<T>(diffOptions);\n\n          // In case the QueryManager stops this QueryInfo before its\n          // results are delivered, it's important to avoid restarting the\n          // cache watch when markResult is called. We also avoid updating\n          // the watch if we are writing a result that doesn't match the current\n          // variables to avoid race conditions from broadcasting the wrong\n          // result.\n          if (!this.stopped && equal(this.variables, options.variables)) {\n            // Any time we're about to update this.diff, we need to make\n            // sure we've started watching the cache.\n            this.updateWatch(options.variables);\n          }\n\n          // If we're allowed to write to the cache, and we can read a\n          // complete result from the cache, update result.data to be the\n          // result from the cache, rather than the raw network result.\n          // Set without setDiff to avoid triggering a notify call, since\n          // we have other ways of notifying for this result.\n          this.updateLastDiff(diff, diffOptions);\n          if (diff.complete) {\n            result.data = diff.result;\n          }\n        });\n      } else {\n        this.lastWrite = void 0;\n      }\n    }\n  }\n\n  public markReady() {\n    this.networkError = null;\n    return (this.networkStatus = NetworkStatus.ready);\n  }\n\n  public markError(error: ApolloError) {\n    this.networkStatus = NetworkStatus.error;\n    this.lastWrite = void 0;\n\n    this.observableQuery?.[\"resetNotifications\"]();\n\n    if (error.graphQLErrors) {\n      this.graphQLErrors = error.graphQLErrors;\n    }\n\n    if (error.networkError) {\n      this.networkError = error.networkError;\n    }\n\n    return error;\n  }\n}\n\nexport function shouldWriteResult<T>(\n  result: FetchResult<T>,\n  errorPolicy: ErrorPolicy = \"none\"\n) {\n  const ignoreErrors = errorPolicy === \"ignore\" || errorPolicy === \"all\";\n  let writeWithErrors = !graphQLResultHasError(result);\n  if (!writeWithErrors && ignoreErrors && result.data) {\n    writeWithErrors = true;\n  }\n  return writeWithErrors;\n}\n"]}