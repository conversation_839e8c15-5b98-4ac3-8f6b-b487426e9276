{"version": 3, "file": "RecursiveSelectionsLimit.js", "sourceRoot": "", "sources": ["../../../src/validationRules/RecursiveSelectionsLimit.ts"], "names": [], "mappings": ";;;AAAA,qCAKiB;AACjB,iDAAqE;AAExD,QAAA,gCAAgC,GAAG,QAAU,CAAC;AAO3D,MAAM,mCAAmC;IAUvC,YACmB,mBAA2B,EAC3B,OAA0B;QAD1B,wBAAmB,GAAnB,mBAAmB,CAAQ;QAC3B,YAAO,GAAP,OAAO,CAAmB;QAX5B,iBAAY,GAC3B,IAAI,GAAG,EAAE,CAAC;QACK,kBAAa,GAC5B,IAAI,GAAG,EAAE,CAAC;QAGK,oCAA+B,GAC9C,IAAI,GAAG,EAAE,CAAC;IAKT,CAAC;IAEI,0BAA0B;QAChC,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE,CAAC;YACvC,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACxD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,KAAK,GAAG;oBACN,cAAc,EAAE,CAAC;oBACjB,eAAe,EAAE,IAAI,GAAG,EAAE;iBAC3B,CAAC;gBACF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACrD,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE,CAAC;YACxC,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC1D,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,KAAK,GAAG;oBACN,cAAc,EAAE,CAAC;oBACjB,eAAe,EAAE,IAAI,GAAG,EAAE;iBAC3B,CAAC;gBACF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YACvD,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,gBAAgB,CAAC,kBAA2B;QAC1C,MAAM,cAAc,GAAG,IAAI,CAAC,0BAA0B,EAAE,CAAC;QACzD,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QACD,cAAc,CAAC,cAAc,EAAE,CAAC;QAChC,IAAI,kBAAkB,KAAK,SAAS,EAAE,CAAC;YACrC,IAAI,WAAW,GACb,CAAC,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACpE,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED,aAAa,CAAC,QAAgB;QAC5B,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;IAClC,CAAC;IAED,aAAa;QACX,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;IACnC,CAAC;IAED,cAAc,CAAC,SAAwB;QACrC,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;IAED,uCAAuC,CAAC,QAAgB;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvE,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;YAMzB,OAAO,CAAC,CAAC;QACX,CAAC;QACD,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;YAC9B,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAKzD,MAAM,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAI,cAAc,EAAE,CAAC;YACnB,KAAK,GAAG,cAAc,CAAC,cAAc,CAAC;YACtC,KAAK,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;gBACrE,KAAK;oBACH,WAAW,GAAG,IAAI,CAAC,uCAAuC,CAAC,QAAQ,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;QACD,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC1D,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,WAAW,CAAC,SAAwB;QAC1C,MAAM,aAAa,GAAG,SAAS;YAC7B,CAAC,CAAC,cAAc,SAAS,GAAG;YAC5B,CAAC,CAAC,qBAAqB,CAAC;QAC1B,IAAI,CAAC,OAAO,CAAC,WAAW,CACtB,IAAI,sBAAY,CACd,GAAG,aAAa,4CAA4C,EAC5D;YACE,KAAK,EAAE,EAAE;YACT,UAAU,EAAE;gBACV,mBAAmB,EACjB,0CAA+B,CAAC,iCAAiC;aACpE;SACF,CACF,CACF,CAAC;IACJ,CAAC;IAED,kBAAkB;QAChB,KAAK,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7D,IAAI,KAAK,GAAG,cAAc,CAAC,cAAc,CAAC;YAC1C,KAAK,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;gBACrE,KAAK;oBACH,WAAW,GAAG,IAAI,CAAC,uCAAuC,CAAC,QAAQ,CAAC,CAAC;YACzE,CAAC;YACD,IAAI,KAAK,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACrC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAUD,SAAgB,gCAAgC,CAC9C,KAAa;IAEb,OAAO,CAAC,OAA0B,EAAc,EAAE;QAChD,MAAM,gBAAgB,GAAG,IAAI,mCAAmC,CAC9D,KAAK,EACL,OAAO,CACR,CAAC;QACF,OAAO;YACL,KAAK;gBACH,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YACtC,CAAC;YACD,cAAc;gBACZ,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;YACtC,CAAC;YACD,cAAc,CAAC,IAAI;gBACjB,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,CAAC;YACD,kBAAkB,EAAE;gBAClB,KAAK,CAAC,IAAI;oBACR,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAClD,CAAC;gBACD,KAAK;oBACH,gBAAgB,CAAC,aAAa,EAAE,CAAC;gBACnC,CAAC;aACF;YACD,mBAAmB,EAAE;gBACnB,KAAK,CAAC,IAAI;oBACR,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,IAAI,IAAI,CAAC,CAAC;gBAC5D,CAAC;gBACD,KAAK;oBACH,gBAAgB,CAAC,cAAc,EAAE,CAAC;gBACpC,CAAC;aACF;YACD,QAAQ,EAAE;gBACR,KAAK;oBACH,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;gBACxC,CAAC;aACF;SACF,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAzCD,4EAyCC"}