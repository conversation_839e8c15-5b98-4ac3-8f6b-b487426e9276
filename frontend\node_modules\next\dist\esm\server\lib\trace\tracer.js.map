{"version": 3, "sources": ["../../../../src/server/lib/trace/tracer.ts"], "names": ["NextVanillaSpanAllowlist", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "trace", "SpanStatusCode", "SpanKind", "isPromise", "p", "then", "closeSpanWithError", "span", "error", "bubble", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "NextTracerImpl", "getTracerInstance", "getTracer", "getContext", "getActiveScopeSpan", "getSpan", "active", "args", "type", "fnOrOptions", "fnOrEmpty", "fn", "options", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanName", "spanContext", "getSpanContext", "parentSpan", "isRootSpan", "ROOT_CONTEXT", "spanId", "attributes", "with", "setValue", "startActiveSpan", "onCleanup", "delete", "set", "Object", "entries", "length", "result", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "undefined", "getRootSpanAttributes", "getValue", "get"], "mappings": "AACA,SAASA,wBAAwB,QAAQ,cAAa;AAUtD,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCH,MAAMI,QAAQ;AAChB,OAAO;IACL,IAAI;QACFJ,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MAAMI,QAAQ;IAChB;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAE,GAAGT;AAErD,MAAMU,YAAY,CAAIC;IACpB,OAAOA,MAAM,QAAQ,OAAOA,MAAM,YAAY,OAAOA,EAAEC,IAAI,KAAK;AAClE;AAIA,MAAMC,qBAAqB,CAACC,MAAYC;IACtC,IAAI,CAACA,yBAAD,AAACA,MAAoCC,MAAM,MAAK,MAAM;QACxDF,KAAKG,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIF,OAAO;YACTD,KAAKI,eAAe,CAACH;QACvB;QACAD,KAAKK,SAAS,CAAC;YAAEC,MAAMZ,eAAea,KAAK;YAAEC,OAAO,EAAEP,yBAAAA,MAAOO,OAAO;QAAC;IACvE;IACAR,KAAKS,GAAG;AACV;AAkGA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB1B,IAAI2B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAExB,MAAME;IACJ;;;;GAIC,GACD,AAAQC,oBAA4B;QAClC,OAAOxB,MAAMyB,SAAS,CAAC,WAAW;IACpC;IAEOC,aAAyB;QAC9B,OAAO3B;IACT;IAEO4B,qBAAuC;QAC5C,OAAO3B,MAAM4B,OAAO,CAAC7B,2BAAAA,QAAS8B,MAAM;IACtC;IAsBO7B,MAAS,GAAG8B,IAAgB,EAAE;QACnC,MAAM,CAACC,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJI,EAAE,EACFC,OAAO,EACR,GAIC,OAAOH,gBAAgB,aACnB;YACEE,IAAIF;YACJG,SAAS,CAAC;QACZ,IACA;YACED,IAAID;YACJE,SAAS;gBAAE,GAAGH,WAAW;YAAC;QAC5B;QAEN,IACE,AAAC,CAACxC,yBAAyB4C,QAAQ,CAACL,SAClCrC,QAAQC,GAAG,CAAC0C,iBAAiB,KAAK,OACpCF,QAAQG,QAAQ,EAChB;YACA,OAAOJ;QACT;QAEA,MAAMK,WAAWJ,QAAQI,QAAQ,IAAIR;QAErC,mHAAmH;QACnH,IAAIS,cAAc,IAAI,CAACC,cAAc,CACnCN,CAAAA,2BAAAA,QAASO,UAAU,KAAI,IAAI,CAACf,kBAAkB;QAEhD,IAAIgB,aAAa;QAEjB,IAAI,CAACH,aAAa;YAChBA,cAAc/C,IAAImD,YAAY;YAC9BD,aAAa;QACf;QAEA,MAAME,SAASvB;QAEfa,QAAQW,UAAU,GAAG;YACnB,kBAAkBP;YAClB,kBAAkBR;YAClB,GAAGI,QAAQW,UAAU;QACvB;QAEA,OAAOrD,IAAIM,OAAO,CAACgD,IAAI,CAACP,YAAYQ,QAAQ,CAAC7B,eAAe0B,SAAS,IACnE,IAAI,CAACrB,iBAAiB,GAAGyB,eAAe,CACtCV,UACAJ,SACA,CAAC5B;gBACC,MAAM2C,YAAY;oBAChBjC,wBAAwBkC,MAAM,CAACN;gBACjC;gBACA,IAAIF,YAAY;oBACd1B,wBAAwBmC,GAAG,CACzBP,QACA,IAAI3B,IACFmC,OAAOC,OAAO,CAACnB,QAAQW,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAIZ,GAAGqB,MAAM,GAAG,GAAG;wBACjB,OAAOrB,GAAG3B,MAAM,CAACT,MAAgBQ,mBAAmBC,MAAMT;oBAC5D;oBAEA,MAAM0D,SAAStB,GAAG3B;oBAElB,IAAIJ,UAAUqD,SAAS;wBACrBA,OACGnD,IAAI,CACH,IAAME,KAAKS,GAAG,IACd,CAAClB,MAAQQ,mBAAmBC,MAAMT,MAEnC2D,OAAO,CAACP;oBACb,OAAO;wBACL3C,KAAKS,GAAG;wBACRkC;oBACF;oBAEA,OAAOM;gBACT,EAAE,OAAO1D,KAAU;oBACjBQ,mBAAmBC,MAAMT;oBACzBoD;oBACA,MAAMpD;gBACR;YACF;IAGN;IAaO4D,KAAK,GAAG5B,IAAgB,EAAE;QAC/B,MAAM6B,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMzB,SAASD,GAAG,GACvBJ,KAAKyB,MAAM,KAAK,IAAIzB,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,CAACtC,yBAAyB4C,QAAQ,CAACwB,SACnClE,QAAQC,GAAG,CAAC0C,iBAAiB,KAAK,KAClC;YACA,OAAOH;QACT;QAEA,OAAO;YACL,IAAI2B,aAAa1B;YACjB,IAAI,OAAO0B,eAAe,cAAc,OAAO3B,OAAO,YAAY;gBAChE2B,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUR,MAAM,GAAG;YACrC,MAAMU,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOjC,UAAU,GAAGyC,IAAI,CAACpE,QAAQ8B,MAAM,IAAIoC;gBAChE,OAAON,OAAO3D,KAAK,CAAC4D,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAUlE,GAAQ;wBACvCuE,wBAAAA,KAAOvE;wBACP,OAAOoE,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAO7B,GAAG4B,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAO3D,KAAK,CAAC4D,MAAMC,YAAY,IAAM3B,GAAG4B,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGxC,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMI,QAAQ,GAA4CL;QAEjE,MAAMU,cAAc,IAAI,CAACC,cAAc,CACrCN,CAAAA,2BAAAA,QAASO,UAAU,KAAI,IAAI,CAACf,kBAAkB;QAEhD,OAAO,IAAI,CAACH,iBAAiB,GAAG8C,SAAS,CAACvC,MAAMI,SAASK;IAC3D;IAEQC,eAAeC,UAAiB,EAAE;QACxC,MAAMF,cAAcE,aAChB1C,MAAMuE,OAAO,CAACxE,QAAQ8B,MAAM,IAAIa,cAChC8B;QAEJ,OAAOhC;IACT;IAEOiC,wBAAwB;QAC7B,MAAM5B,SAAS9C,QAAQ8B,MAAM,GAAG6C,QAAQ,CAACvD;QACzC,OAAOF,wBAAwB0D,GAAG,CAAC9B;IACrC;AACF;AAEA,MAAMpB,YAAY,AAAC,CAAA;IACjB,MAAMkC,SAAS,IAAIpC;IAEnB,OAAO,IAAMoC;AACf,CAAA;AAEA,SAASlC,SAAS,EAAExB,cAAc,EAAEC,QAAQ,GAAE"}