{"nested": {"google": {"nested": {"protobuf": {"nested": {"Type": {"fields": {"name": {"type": "string", "id": 1}, "fields": {"rule": "repeated", "type": "Field", "id": 2}, "oneofs": {"rule": "repeated", "type": "string", "id": 3}, "options": {"rule": "repeated", "type": "Option", "id": 4}, "sourceContext": {"type": "SourceContext", "id": 5}, "syntax": {"type": "Syntax", "id": 6}}}, "Field": {"fields": {"kind": {"type": "Kind", "id": 1}, "cardinality": {"type": "Cardinality", "id": 2}, "number": {"type": "int32", "id": 3}, "name": {"type": "string", "id": 4}, "typeUrl": {"type": "string", "id": 6}, "oneofIndex": {"type": "int32", "id": 7}, "packed": {"type": "bool", "id": 8}, "options": {"rule": "repeated", "type": "Option", "id": 9}, "jsonName": {"type": "string", "id": 10}, "defaultValue": {"type": "string", "id": 11}}, "nested": {"Kind": {"values": {"TYPE_UNKNOWN": 0, "TYPE_DOUBLE": 1, "TYPE_FLOAT": 2, "TYPE_INT64": 3, "TYPE_UINT64": 4, "TYPE_INT32": 5, "TYPE_FIXED64": 6, "TYPE_FIXED32": 7, "TYPE_BOOL": 8, "TYPE_STRING": 9, "TYPE_GROUP": 10, "TYPE_MESSAGE": 11, "TYPE_BYTES": 12, "TYPE_UINT32": 13, "TYPE_ENUM": 14, "TYPE_SFIXED32": 15, "TYPE_SFIXED64": 16, "TYPE_SINT32": 17, "TYPE_SINT64": 18}}, "Cardinality": {"values": {"CARDINALITY_UNKNOWN": 0, "CARDINALITY_OPTIONAL": 1, "CARDINALITY_REQUIRED": 2, "CARDINALITY_REPEATED": 3}}}}, "Enum": {"fields": {"name": {"type": "string", "id": 1}, "enumvalue": {"rule": "repeated", "type": "EnumValue", "id": 2}, "options": {"rule": "repeated", "type": "Option", "id": 3}, "sourceContext": {"type": "SourceContext", "id": 4}, "syntax": {"type": "Syntax", "id": 5}}}, "EnumValue": {"fields": {"name": {"type": "string", "id": 1}, "number": {"type": "int32", "id": 2}, "options": {"rule": "repeated", "type": "Option", "id": 3}}}, "Option": {"fields": {"name": {"type": "string", "id": 1}, "value": {"type": "Any", "id": 2}}}, "Syntax": {"values": {"SYNTAX_PROTO2": 0, "SYNTAX_PROTO3": 1}}, "Any": {"fields": {"type_url": {"type": "string", "id": 1}, "value": {"type": "bytes", "id": 2}}}, "SourceContext": {"fields": {"fileName": {"type": "string", "id": 1}}}}}}}}}