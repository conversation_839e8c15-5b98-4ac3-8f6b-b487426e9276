{"version": 3, "file": "cache.js", "sourceRoot": "", "sources": ["../../../src/cache/core/cache.ts"], "names": [], "mappings": ";AAKA,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAQhC,OAAO,EACL,UAAU,EACV,UAAU,EAEV,qBAAqB,EACrB,wBAAwB,EACxB,cAAc,GACf,MAAM,0BAA0B,CAAC;AAGlC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,6BAA6B,EAAE,MAAM,+CAA+C,CAAC;AAM9F,OAAO,EAAE,YAAY,EAAE,MAAM,4BAA4B,CAAC;AAC1D,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAC7D,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAqEtD;IAAA;QACkB,2BAAsB,GAAY,KAAK,CAAC;QA2OxD,oEAAoE;QACpE,qDAAqD;QAC7C,mBAAc,GAAG,IAAI,CAAC,wBAAwB,EAAE;YACtD,GAAG,EACD,UAAU,CAAC,8BAA8B,CAAC;4EACO;YACnD,KAAK,EAAE,SAAS;SACjB,CAAC,CAAC;IAiFL,CAAC;IAvQC,4EAA4E;IAC5E,6EAA6E;IAC7E,2EAA2E;IACpE,oCAAc,GAArB,UAAsB,YAAoB;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,oBAAoB;IAEpB,0EAA0E;IAC1E,4EAA4E;IAC5E,4EAA4E;IAC5E,4EAA4E;IAC5E,4EAA4E;IACrE,2BAAK,GAAZ,UAAgB,OAAoC;QAApD,iBAWC;QAVC,IAAM,YAAY,GAChB,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU;YAC3D,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI;gBACrC,CAAC,CAAC,KAAK,CAAC,CAAC;QACX,IAAI,YAAe,CAAC;QACpB,IAAI,CAAC,kBAAkB,CACrB,cAAM,OAAA,CAAC,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,KAAI,CAAC,CAAC,EAArC,CAAqC,EAC3C,YAAY,CACb,CAAC;QACF,OAAO,YAAa,CAAC;IACvB,CAAC;IAcM,iDAA2B,GAAlC,UACE,WAAqC,EACrC,YAAoB;QAEpB,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;IACrD,CAAC;IAED,eAAe;IAEf,4EAA4E;IAC5E,kDAAkD;IAC3C,uCAAiB,GAAxB,UAAyB,QAAsB;QAC7C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,4EAA4E;IAC5E,yEAAyE;IAClE,sCAAgB,GAAvB,UAAwB,QAAsB;QAC5C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,8BAAQ,GAAf,UAAgB,MAA+B;QAC7C,OAAO;IACT,CAAC;IAEM,wBAAE,GAAT;QACE,OAAO,EAAE,CAAC;IACZ,CAAC;IAEM,4BAAM,GAAb,UACE,OAAoC;QAEpC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,gBAAgB;IACT,+BAAS,GAAhB,UACE,OAAsD,EACtD,UAAiC;QAAjC,2BAAA,EAAA,cAAc,CAAC,OAAO,CAAC,UAAU;QAEjC,OAAO,IAAI,CAAC,IAAI,uBACX,OAAO,KACV,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,YAAY,EAClC,UAAU,YAAA,IACV,CAAC;IACL,CAAC;IAED,wEAAwE;IACjE,mCAAa,GAApB,UACE,OAA2C;QAD7C,iBAsFC;QAlFG,IAAA,QAAQ,GAKN,OAAO,SALD,EACR,YAAY,GAIV,OAAO,aAJG,EACZ,IAAI,GAGF,OAAO,KAHL,EACJ,KAEE,OAAO,WAFQ,EAAjB,UAAU,mBAAG,IAAI,KAAA,EACd,YAAY,UACb,OAAO,EANL,kDAML,CADgB,CACL;QACZ,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAC1D,qEAAqE;QACrE,sEAAsE;QACtE,mEAAmE;QACnE,sEAAsE;QACtE,sEAAsE;QACtE,WAAW;QACX,IAAM,EAAE,GACN,OAAO,IAAI,KAAK,WAAW,IAAI,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC;YACvD,IAAI;YACN,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxB,IAAM,WAAW,GAAG,CAAC,CAAE,OAAe,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC;QAEzE,IAAI,OAAO,EAAE,CAAC;YACZ,IAAM,kBAAkB,GACtB,YAAY,IAAI,qBAAqB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YAE7D,IAAI,CAAC,EAAE,EAAE,CAAC;gBACR,SAAS,CAAC,IAAI,CACZ,2OAA2O,EAC3O,kBAAkB,CACnB,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAM,WAAW,yBACZ,YAAY,KACf,iBAAiB,EAAE,IAAI,EACvB,EAAE,IAAA,EACF,KAAK,OAAA,EACL,UAAU,YAAA,GACX,CAAC;QAEF,IAAI,UAAmD,CAAC;QAExD,OAAO,IAAI,UAAU,CAAC,UAAC,QAAQ;YAC7B,OAAO,KAAI,CAAC,KAAK,uBACZ,WAAW,KACd,SAAS,EAAE,IAAI,EACf,QAAQ,EAAE,UAAC,IAAI;oBACb,IAAM,IAAI,GACR,WAAW,CAAC,CAAC;wBACX,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAI,EAAE,YAAY,CAAC;wBACzD,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;oBAEhB;oBACE,4CAA4C;oBAC5C,UAAU;wBACV,YAAY,CACV,KAAK,EACL,EAAE,IAAI,EAAE,UAAU,CAAC,MAAM,EAAE,EAC3B,EAAE,IAAI,MAAA,EAAE;wBACR,2DAA2D;wBAC3D,6BAA6B;wBAC7B,OAAO,CAAC,SAA+B,CACxC,EACD,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,IAAM,MAAM,GAAG;wBACb,IAAI,MAAA;wBACJ,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;qBACI,CAAC;oBAEhC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;wBACjB,MAAM,CAAC,OAAO,GAAG,cAAc,CAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,OAAO,EAAb,CAAa,CAAC,CAC3C,CAAC;oBACJ,CAAC;oBAED,UAAU,yBAAQ,IAAI,KAAE,MAAM,EAAE,IAAI,GAAE,CAAC;oBACvC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxB,CAAC,IACD,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAWM,kCAAY,GAAnB,UACE,OAA4D,EAC5D,UAAiC;QAAjC,2BAAA,EAAA,cAAc,CAAC,OAAO,CAAC,UAAU;QAEjC,OAAO,IAAI,CAAC,IAAI,uBACX,OAAO,KACV,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,EAClE,MAAM,EAAE,OAAO,CAAC,EAAE,EAClB,UAAU,YAAA,IACV,CAAC;IACL,CAAC;IAEM,gCAAU,GAAjB,UAAiD,EAIJ;QAH3C,IAAA,EAAE,QAAA,EACF,IAAI,UAAA,EACD,OAAO,cAHqC,cAIhD,CADW;QAEV,OAAO,IAAI,CAAC,KAAK,CACf,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;YACrB,MAAM,EAAE,EAAE,IAAI,YAAY;YAC1B,MAAM,EAAE,IAAI;SACb,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,mCAAa,GAApB,UAAoD,EAMJ;QAL9C,IAAA,EAAE,QAAA,EACF,IAAI,UAAA,EACJ,QAAQ,cAAA,EACR,YAAY,kBAAA,EACT,OAAO,cALwC,0CAMnD,CADW;QAEV,OAAO,IAAI,CAAC,KAAK,CACf,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;YACrB,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,YAAY,CAAC;YAClD,MAAM,EAAE,EAAE;YACV,MAAM,EAAE,IAAI;SACb,CAAC,CACH,CAAC;IACJ,CAAC;IAEM,iCAAW,GAAlB,UACE,OAAoD,EACpD,MAAuE;QAEvE,OAAO,IAAI,CAAC,KAAK,CAAC;YAChB,MAAM,YAAC,KAAK;gBACV,IAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAoB,OAAO,CAAC,CAAC;gBAC1D,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC3B,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI;oBAAE,OAAO,KAAK,CAAC;gBACnD,KAAK,CAAC,UAAU,uBAAyB,OAAO,KAAE,IAAI,MAAA,IAAG,CAAC;gBAC1D,OAAO,IAAI,CAAC;YACd,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAEM,oCAAc,GAArB,UACE,OAAuD,EACvD,MAAuE;QAEvE,OAAO,IAAI,CAAC,KAAK,CAAC;YAChB,MAAM,YAAC,KAAK;gBACV,IAAM,KAAK,GAAG,KAAK,CAAC,YAAY,CAAoB,OAAO,CAAC,CAAC;gBAC7D,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC3B,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI;oBAAE,OAAO,KAAK,CAAC;gBACnD,KAAK,CAAC,aAAa,uBAAyB,OAAO,KAAE,IAAI,MAAA,IAAG,CAAC;gBAC7D,OAAO,IAAI,CAAC;YACd,CAAC;SACF,CAAC,CAAC;IACL,CAAC;IAUH,kBAAC;AAAD,CAAC,AApUD,IAoUC;;AAED,IAAI,OAAO,EAAE,CAAC;IACZ,WAAW,CAAC,SAAS,CAAC,kBAAkB,GAAG,6BAA6B,CAAC;AAC3E,CAAC", "sourcesContent": ["import type {\n  DocumentNode,\n  FragmentDefinitionNode,\n  InlineFragmentNode,\n} from \"graphql\";\nimport { wrap } from \"optimism\";\n\nimport type {\n  StoreObject,\n  Reference,\n  DeepPartial,\n  NoInfer,\n} from \"../../utilities/index.js\";\nimport {\n  Observable,\n  cacheSizes,\n  defaultCacheSizes,\n  getFragmentDefinition,\n  getFragmentQueryDocument,\n  mergeDeepArray,\n} from \"../../utilities/index.js\";\nimport type { DataProxy } from \"./types/DataProxy.js\";\nimport type { Cache } from \"./types/Cache.js\";\nimport { WeakCache } from \"@wry/caches\";\nimport { getApolloCacheMemoryInternals } from \"../../utilities/caching/getMemoryInternals.js\";\nimport type {\n  OperationVariables,\n  TypedDocumentNode,\n} from \"../../core/types.js\";\nimport type { MissingTree } from \"./types/common.js\";\nimport { equalByQuery } from \"../../core/equalByQuery.js\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { maskFragment } from \"../../masking/index.js\";\nimport type {\n  FragmentType,\n  MaybeMasked,\n  Unmasked,\n} from \"../../masking/index.js\";\n\nexport type Transaction<T> = (c: ApolloCache<T>) => void;\n\n/**\n * Watched fragment options.\n */\nexport interface WatchFragmentOptions<TData, TVars> {\n  /**\n   * A GraphQL fragment document parsed into an AST with the `gql`\n   * template literal.\n   *\n   * @docGroup 1. Required options\n   */\n  fragment: DocumentNode | TypedDocumentNode<TData, TVars>;\n  /**\n   * An object containing a `__typename` and primary key fields\n   * (such as `id`) identifying the entity object from which the fragment will\n   * be retrieved, or a `{ __ref: \"...\" }` reference, or a `string` ID\n   * (uncommon).\n   *\n   * @docGroup 1. Required options\n   */\n  from: StoreObject | Reference | FragmentType<NoInfer<TData>> | string;\n  /**\n   * Any variables that the GraphQL fragment may depend on.\n   *\n   * @docGroup 2. Cache options\n   */\n  variables?: TVars;\n  /**\n   * The name of the fragment defined in the fragment document.\n   *\n   * Required if the fragment document includes more than one fragment,\n   * optional otherwise.\n   *\n   * @docGroup 2. Cache options\n   */\n  fragmentName?: string;\n  /**\n   * If `true`, `watchFragment` returns optimistic results.\n   *\n   * The default value is `true`.\n   *\n   * @docGroup 2. Cache options\n   */\n  optimistic?: boolean;\n}\n\n/**\n * Watched fragment results.\n */\nexport type WatchFragmentResult<TData> =\n  | {\n      data: MaybeMasked<TData>;\n      complete: true;\n      missing?: never;\n    }\n  | {\n      data: DeepPartial<MaybeMasked<TData>>;\n      complete: false;\n      missing: MissingTree;\n    };\n\nexport abstract class ApolloCache<TSerialized> implements DataProxy {\n  public readonly assumeImmutableResults: boolean = false;\n\n  // required to implement\n  // core API\n  public abstract read<TData = any, TVariables = any>(\n    query: Cache.ReadOptions<TVariables, TData>\n  ): Unmasked<TData> | null;\n  public abstract write<TData = any, TVariables = any>(\n    write: Cache.WriteOptions<TData, TVariables>\n  ): Reference | undefined;\n  public abstract diff<T>(query: Cache.DiffOptions): Cache.DiffResult<T>;\n  public abstract watch<TData = any, TVariables = any>(\n    watch: Cache.WatchOptions<TData, TVariables>\n  ): () => void;\n\n  // Empty the cache and restart all current watches (unless\n  // options.discardWatches is true).\n  public abstract reset(options?: Cache.ResetOptions): Promise<void>;\n\n  // Remove whole objects from the cache by passing just options.id, or\n  // specific fields by passing options.field and/or options.args. If no\n  // options.args are provided, all fields matching options.field (even\n  // those with arguments) will be removed. Returns true iff any data was\n  // removed from the cache.\n  public abstract evict(options: Cache.EvictOptions): boolean;\n\n  // initializer / offline / ssr API\n  /**\n   * Replaces existing state in the cache (if any) with the values expressed by\n   * `serializedState`.\n   *\n   * Called when hydrating a cache (server side rendering, or offline storage),\n   * and also (potentially) during hot reloads.\n   */\n  public abstract restore(\n    serializedState: TSerialized\n  ): ApolloCache<TSerialized>;\n\n  /**\n   * Exposes the cache's complete state, in a serializable format for later restoration.\n   */\n  public abstract extract(optimistic?: boolean): TSerialized;\n\n  // Optimistic API\n\n  public abstract removeOptimistic(id: string): void;\n\n  // Data masking API\n\n  // Used by data masking to determine if an inline fragment with a type\n  // condition matches a given typename.\n  //\n  // If not implemented by a cache subclass, data masking will effectively be\n  // disabled since we will not be able to accurately determine if a given type\n  // condition for a union or interface matches a particular type.\n  public fragmentMatches?(\n    fragment: InlineFragmentNode,\n    typename: string\n  ): boolean;\n\n  // Function used to lookup a fragment when a fragment definition is not part\n  // of the GraphQL document. This is useful for caches, such as InMemoryCache,\n  // that register fragments ahead of time so they can be referenced by name.\n  public lookupFragment(fragmentName: string): FragmentDefinitionNode | null {\n    return null;\n  }\n\n  // Transactional API\n\n  // The batch method is intended to replace/subsume both performTransaction\n  // and recordOptimisticTransaction, but performTransaction came first, so we\n  // provide a default batch implementation that's just another way of calling\n  // performTransaction. Subclasses of ApolloCache (such as InMemoryCache) can\n  // override the batch method to do more interesting things with its options.\n  public batch<U>(options: Cache.BatchOptions<this, U>): U {\n    const optimisticId =\n      typeof options.optimistic === \"string\" ? options.optimistic\n      : options.optimistic === false ? null\n      : void 0;\n    let updateResult: U;\n    this.performTransaction(\n      () => (updateResult = options.update(this)),\n      optimisticId\n    );\n    return updateResult!;\n  }\n\n  public abstract performTransaction(\n    transaction: Transaction<TSerialized>,\n    // Although subclasses may implement recordOptimisticTransaction\n    // however they choose, the default implementation simply calls\n    // performTransaction with a string as the second argument, allowing\n    // performTransaction to handle both optimistic and non-optimistic\n    // (broadcast-batching) transactions. Passing null for optimisticId is\n    // also allowed, and indicates that performTransaction should apply\n    // the transaction non-optimistically (ignoring optimistic data).\n    optimisticId?: string | null\n  ): void;\n\n  public recordOptimisticTransaction(\n    transaction: Transaction<TSerialized>,\n    optimisticId: string\n  ) {\n    this.performTransaction(transaction, optimisticId);\n  }\n\n  // Optional API\n\n  // Called once per input document, allowing the cache to make static changes\n  // to the query, such as adding __typename fields.\n  public transformDocument(document: DocumentNode): DocumentNode {\n    return document;\n  }\n\n  // Called before each ApolloLink request, allowing the cache to make dynamic\n  // changes to the query, such as filling in missing fragment definitions.\n  public transformForLink(document: DocumentNode): DocumentNode {\n    return document;\n  }\n\n  public identify(object: StoreObject | Reference): string | undefined {\n    return;\n  }\n\n  public gc(): string[] {\n    return [];\n  }\n\n  public modify<Entity extends Record<string, any> = Record<string, any>>(\n    options: Cache.ModifyOptions<Entity>\n  ): boolean {\n    return false;\n  }\n\n  // DataProxy API\n  public readQuery<QueryType, TVariables = any>(\n    options: Cache.ReadQueryOptions<QueryType, TVariables>,\n    optimistic = !!options.optimistic\n  ): Unmasked<QueryType> | null {\n    return this.read({\n      ...options,\n      rootId: options.id || \"ROOT_QUERY\",\n      optimistic,\n    });\n  }\n\n  /** {@inheritDoc @apollo/client!ApolloClient#watchFragment:member(1)} */\n  public watchFragment<TData = any, TVars = OperationVariables>(\n    options: WatchFragmentOptions<TData, TVars>\n  ): Observable<WatchFragmentResult<TData>> {\n    const {\n      fragment,\n      fragmentName,\n      from,\n      optimistic = true,\n      ...otherOptions\n    } = options;\n    const query = this.getFragmentDoc(fragment, fragmentName);\n    // While our TypeScript types do not allow for `undefined` as a valid\n    // `from`, its possible `useFragment` gives us an `undefined` since it\n    // calls` cache.identify` and provides that value to `from`. We are\n    // adding this fix here however to ensure those using plain JavaScript\n    // and using `cache.identify` themselves will avoid seeing the obscure\n    // warning.\n    const id =\n      typeof from === \"undefined\" || typeof from === \"string\" ?\n        from\n      : this.identify(from);\n    const dataMasking = !!(options as any)[Symbol.for(\"apollo.dataMasking\")];\n\n    if (__DEV__) {\n      const actualFragmentName =\n        fragmentName || getFragmentDefinition(fragment).name.value;\n\n      if (!id) {\n        invariant.warn(\n          \"Could not identify object passed to `from` for '%s' fragment, either because the object is non-normalized or the key fields are missing. If you are masking this object, please ensure the key fields are requested by the parent object.\",\n          actualFragmentName\n        );\n      }\n    }\n\n    const diffOptions: Cache.DiffOptions<TData, TVars> = {\n      ...otherOptions,\n      returnPartialData: true,\n      id,\n      query,\n      optimistic,\n    };\n\n    let latestDiff: DataProxy.DiffResult<TData> | undefined;\n\n    return new Observable((observer) => {\n      return this.watch<TData, TVars>({\n        ...diffOptions,\n        immediate: true,\n        callback: (diff) => {\n          const data =\n            dataMasking ?\n              maskFragment(diff.result, fragment, this, fragmentName)\n            : diff.result;\n\n          if (\n            // Always ensure we deliver the first result\n            latestDiff &&\n            equalByQuery(\n              query,\n              { data: latestDiff.result },\n              { data },\n              // TODO: Fix the type on WatchFragmentOptions so that TVars\n              // extends OperationVariables\n              options.variables as OperationVariables\n            )\n          ) {\n            return;\n          }\n\n          const result = {\n            data,\n            complete: !!diff.complete,\n          } as WatchFragmentResult<TData>;\n\n          if (diff.missing) {\n            result.missing = mergeDeepArray(\n              diff.missing.map((error) => error.missing)\n            );\n          }\n\n          latestDiff = { ...diff, result: data };\n          observer.next(result);\n        },\n      });\n    });\n  }\n\n  // Make sure we compute the same (===) fragment query document every\n  // time we receive the same fragment in readFragment.\n  private getFragmentDoc = wrap(getFragmentQueryDocument, {\n    max:\n      cacheSizes[\"cache.fragmentQueryDocuments\"] ||\n      defaultCacheSizes[\"cache.fragmentQueryDocuments\"],\n    cache: WeakCache,\n  });\n\n  public readFragment<FragmentType, TVariables = any>(\n    options: Cache.ReadFragmentOptions<FragmentType, TVariables>,\n    optimistic = !!options.optimistic\n  ): Unmasked<FragmentType> | null {\n    return this.read({\n      ...options,\n      query: this.getFragmentDoc(options.fragment, options.fragmentName),\n      rootId: options.id,\n      optimistic,\n    });\n  }\n\n  public writeQuery<TData = any, TVariables = any>({\n    id,\n    data,\n    ...options\n  }: Cache.WriteQueryOptions<TData, TVariables>): Reference | undefined {\n    return this.write(\n      Object.assign(options, {\n        dataId: id || \"ROOT_QUERY\",\n        result: data,\n      })\n    );\n  }\n\n  public writeFragment<TData = any, TVariables = any>({\n    id,\n    data,\n    fragment,\n    fragmentName,\n    ...options\n  }: Cache.WriteFragmentOptions<TData, TVariables>): Reference | undefined {\n    return this.write(\n      Object.assign(options, {\n        query: this.getFragmentDoc(fragment, fragmentName),\n        dataId: id,\n        result: data,\n      })\n    );\n  }\n\n  public updateQuery<TData = any, TVariables = any>(\n    options: Cache.UpdateQueryOptions<TData, TVariables>,\n    update: (data: Unmasked<TData> | null) => Unmasked<TData> | null | void\n  ): Unmasked<TData> | null {\n    return this.batch({\n      update(cache) {\n        const value = cache.readQuery<TData, TVariables>(options);\n        const data = update(value);\n        if (data === void 0 || data === null) return value;\n        cache.writeQuery<TData, TVariables>({ ...options, data });\n        return data;\n      },\n    });\n  }\n\n  public updateFragment<TData = any, TVariables = any>(\n    options: Cache.UpdateFragmentOptions<TData, TVariables>,\n    update: (data: Unmasked<TData> | null) => Unmasked<TData> | null | void\n  ): Unmasked<TData> | null {\n    return this.batch({\n      update(cache) {\n        const value = cache.readFragment<TData, TVariables>(options);\n        const data = update(value);\n        if (data === void 0 || data === null) return value;\n        cache.writeFragment<TData, TVariables>({ ...options, data });\n        return data;\n      },\n    });\n  }\n\n  /**\n   * @experimental\n   * @internal\n   * This is not a stable API - it is used in development builds to expose\n   * information to the DevTools.\n   * Use at your own risk!\n   */\n  public getMemoryInternals?: typeof getApolloCacheMemoryInternals;\n}\n\nif (__DEV__) {\n  ApolloCache.prototype.getMemoryInternals = getApolloCacheMemoryInternals;\n}\n"]}