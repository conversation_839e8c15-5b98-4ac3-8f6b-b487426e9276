"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.lessonResolvers = void 0;
exports.lessonResolvers = {
    Query: {},
    Mutation: {
        submitQuiz: async (_, args, { prisma, user }) => {
            if (!user)
                throw new Error('Not authenticated');
            const { quizId, answers } = args;
            // Get quiz with questions
            const quiz = await prisma.quiz.findUnique({
                where: { id: quizId },
                include: {
                    questions: true
                }
            });
            if (!quiz)
                throw new Error('Quiz not found');
            // Calculate score
            let correctAnswers = 0;
            const feedback = quiz.questions.map((question, index) => {
                const userAnswer = answers[index];
                const isCorrect = userAnswer === question.correctAnswer;
                if (isCorrect)
                    correctAnswers++;
                return {
                    questionId: question.id,
                    correct: isCorrect,
                    explanation: question.explanation
                };
            });
            const score = (correctAnswers / quiz.questions.length) * 100;
            // Save attempt
            await prisma.quizAttempt.create({
                data: {
                    userId: user.userId,
                    quizId,
                    answers: JSON.stringify(answers),
                    score
                }
            });
            return {
                score,
                totalQuestions: quiz.questions.length,
                correctAnswers,
                feedback
            };
        },
    },
    Lesson: {
        quizzes: async (parent, _, { prisma }) => {
            return prisma.quiz.findMany({
                where: { lessonId: parent.id },
                include: {
                    questions: true
                }
            });
        },
    }
};
//# sourceMappingURL=lesson.js.map