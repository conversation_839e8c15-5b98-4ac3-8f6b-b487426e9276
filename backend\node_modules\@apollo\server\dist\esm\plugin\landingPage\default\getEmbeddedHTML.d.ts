import type { ApolloServerPluginEmbeddedLandingPageLocalDefaultOptions, ApolloServerPluginEmbeddedLandingPageProductionDefaultOptions } from './types';
export declare const getEmbeddedExplorerHTML: (explorerCdnVersion: string, config: ApolloServerPluginEmbeddedLandingPageProductionDefaultOptions, apolloServerVersion: string, nonce: string) => string;
export declare const getEmbeddedSandboxHTML: (sandboxCdnVersion: string, config: ApolloServerPluginEmbeddedLandingPageLocalDefaultOptions, apolloServerVersion: string, nonce: string) => string;
//# sourceMappingURL=getEmbeddedHTML.d.ts.map