// frontend/src/components/sections/HeroSection.tsx
import { Arrow<PERSON>ef<PERSON>, Play, Star } from 'lucide-react'
import Link from 'next/link'

export function HeroSection() {
  return (
    <section className="relative py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-right">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
              <span className="text-blue-600">منارة</span> المحاسبة
              <br />
              <span className="text-indigo-600">والمالية</span>
            </h1>
            
            <p className="mt-6 text-xl text-gray-600 leading-relaxed">
              منصة تعليمية ذكية متكاملة للتمويل والمحاسبة في الجزائر
              <br />
              تعلم بطريقة تفاعلية مع محاكيات مالية واقعية
            </p>

            <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link
                href="/register"
                className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
              >
                ابدأ التعلم الآن
                <ArrowLeft className="mr-2 w-5 h-5" />
              </Link>
              
              <button className="inline-flex items-center px-8 py-3 border-2 border-gray-300 text-gray-700 font-semibold rounded-lg hover:border-blue-600 hover:text-blue-600 transition-colors">
                <Play className="ml-2 w-5 h-5" />
                شاهد العرض التوضيحي
              </button>
            </div>

            {/* Stats */}
            <div className="mt-12 flex justify-center lg:justify-start space-x-8 space-x-reverse">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">2000+</div>
                <div className="text-sm text-gray-600">طالب مسجل</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600">500+</div>
                <div className="text-sm text-gray-600">درس تفاعلي</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center">
                  <div className="text-2xl font-bold text-yellow-500">4.9</div>
                  <Star className="w-5 h-5 text-yellow-500 fill-current mr-1" />
                </div>
                <div className="text-sm text-gray-600">تقييم المستخدمين</div>
              </div>
            </div>
          </div>

          {/* Visual */}
          <div className="relative">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl p-8 text-white">
              <div className="bg-white/10 rounded-lg p-6 backdrop-blur-sm">
                <h3 className="text-xl font-semibold mb-4">لوحة تحكم الطالب</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span>التقدم في المحاسبة العامة</span>
                    <span className="bg-green-500 px-2 py-1 rounded text-sm">85%</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div className="bg-green-400 h-2 rounded-full" style={{width: '85%'}}></div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span>التحليل المالي</span>
                    <span className="bg-yellow-500 px-2 py-1 rounded text-sm">65%</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div className="bg-yellow-400 h-2 rounded-full" style={{width: '65%'}}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
