import type { BaseContext, ApolloServerPlugin } from '../../index.js';
export declare function ApolloServerPluginCacheControlDisabled(): ApolloServerPlugin<BaseContext>;
export declare function ApolloServerPluginInlineTraceDisabled(): ApolloServerPlugin<BaseContext>;
export declare function ApolloServerPluginLandingPageDisabled(): ApolloServerPlugin<BaseContext>;
export declare function ApolloServerPluginSchemaReportingDisabled(): ApolloServerPlugin<BaseContext>;
export declare function ApolloServerPluginUsageReportingDisabled(): ApolloServerPlugin<BaseContext>;
//# sourceMappingURL=index.d.ts.map