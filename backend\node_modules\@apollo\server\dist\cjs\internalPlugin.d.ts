import type { BaseContext, ApolloServerPlugin } from './externalTypes/index.js';
export interface InternalApolloServerPlugin<TContext extends BaseContext> extends ApolloServerPlugin<TContext> {
    __internal_plugin_id__: InternalPluginId;
    __is_disabled_plugin__: boolean;
}
export declare function internalPlugin<TContext extends BaseContext>(p: InternalApolloServerPlugin<TContext>): ApolloServerPlugin<TContext>;
export type InternalPluginId = 'CacheControl' | 'LandingPageDisabled' | 'SchemaReporting' | 'InlineTrace' | 'UsageReporting' | 'DisableSuggestions';
export declare function pluginIsInternal<TContext extends BaseContext>(plugin: ApolloServerPlugin<TContext>): plugin is InternalApolloServerPlugin<TContext>;
//# sourceMappingURL=internalPlugin.d.ts.map