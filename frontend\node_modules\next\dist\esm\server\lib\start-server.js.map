{"version": 3, "sources": ["../../../src/server/lib/start-server.ts"], "names": ["performance", "getEntriesByName", "length", "mark", "fs", "v8", "path", "http", "https", "Watchpack", "Log", "setupDebug", "RESTART_EXIT_CODE", "checkNodeDebugType", "getDebugPort", "formatHostname", "initialize", "CONFIG_FILES", "getStartServerInfo", "logStartInfo", "validateTurboNextConfig", "isPostpone", "debug", "getRequestHandlers", "dir", "port", "isDev", "server", "hostname", "minimalMode", "isNodeDebugging", "keepAliveTimeout", "experimentalTestProxy", "experimentalHttpsServer", "dev", "startServer", "serverOptions", "allowRetry", "isExperimentalTestProxy", "selfSignedCertificate", "process", "title", "handlersReady", "handlersError", "handlersPromise", "Promise", "resolve", "reject", "requestHandler", "req", "res", "Error", "upgradeHandler", "socket", "head", "requestListener", "undefined", "err", "statusCode", "end", "error", "url", "console", "getHeapStatistics", "used_heap_size", "heap_size_limit", "warn", "exit", "createServer", "key", "readFileSync", "cert", "on", "destroy", "portRetryCount", "code", "listen", "nodeDebugType", "addr", "address", "actualHostname", "formattedHostname", "networkUrl", "appUrl", "debugPort", "info", "env", "PORT", "envInfo", "expFeatureInfo", "startServerInfo", "maxExperimentalFeatures", "cleanup", "close", "exception", "initResult", "Boolean", "startServerProcessDuration", "measure", "duration", "formatDurationText", "Math", "round", "event", "TURBOPACK", "watchConfigFiles", "dirToWatch", "onChange", "wp", "watch", "files", "map", "file", "join", "filename", "__NEXT_DISABLE_MEMORY_WATCHER", "basename", "NEXT_PRIVATE_WORKER", "send", "addListener", "msg", "nextWorkerOptions", "nextServerReady", "nextWorkerReady"], "mappings": "AAAA,IAAIA,YAAYC,gBAAgB,CAAC,cAAcC,MAAM,KAAK,GAAG;IAC3DF,YAAYG,IAAI,CAAC;AACnB;AACA,OAAO,UAAS;AAChB,OAAO,kBAAiB;AAMxB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,OAAOC,UAAU,OAAM;AACvB,OAAOC,WAAW,QAAO;AACzB,OAAOC,eAAe,YAAW;AACjC,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,gBAAgB,2BAA0B;AACjD,SAASC,iBAAiB,EAAEC,kBAAkB,EAAEC,YAAY,QAAQ,UAAS;AAC7E,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,UAAU,QAAQ,kBAAiB;AAC5C,SAASC,YAAY,QAAQ,6BAA4B;AACzD,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,iBAAgB;AACjE,SAASC,uBAAuB,QAAQ,8BAA6B;AACrE,SAASC,UAAU,QAAQ,6BAA4B;AAEvD,MAAMC,QAAQX,WAAW;AAgBzB,OAAO,eAAeY,mBAAmB,EACvCC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,eAAe,EACfC,gBAAgB,EAChBC,qBAAqB,EACrBC,uBAAuB,EAYxB;IACC,OAAOjB,WAAW;QAChBQ;QACAC;QACAG;QACAM,KAAKR;QACLG;QACAF;QACAG,iBAAiBA,mBAAmB;QACpCC;QACAC;QACAC;IACF;AACF;AAEA,OAAO,eAAeE,YACpBC,aAAiC;IAEjC,MAAM,EACJZ,GAAG,EACHE,KAAK,EACLE,QAAQ,EACRC,WAAW,EACXQ,UAAU,EACVN,gBAAgB,EAChBO,uBAAuB,EACvBC,qBAAqB,EACtB,GAAGH;IACJ,IAAI,EAAEX,IAAI,EAAE,GAAGW;IAEfI,QAAQC,KAAK,GAAG;IAChB,IAAIC,gBAAgB,KAAO;IAC3B,IAAIC,gBAAgB,KAAO;IAE3B,IAAIC,kBAA6C,IAAIC,QACnD,CAACC,SAASC;QACRL,gBAAgBI;QAChBH,gBAAgBI;IAClB;IAEF,IAAIC,iBAAuC,OACzCC,KACAC;QAEA,IAAIN,iBAAiB;YACnB,MAAMA;YACN,OAAOI,eAAeC,KAAKC;QAC7B;QACA,MAAM,IAAIC,MAAM;IAClB;IACA,IAAIC,iBAAuC,OACzCH,KACAI,QACAC;QAEA,IAAIV,iBAAiB;YACnB,MAAMA;YACN,OAAOQ,eAAeH,KAAKI,QAAQC;QACrC;QACA,MAAM,IAAIH,MAAM;IAClB;IAEA,4CAA4C;IAC5C,IAAIZ,yBAAyB,CAACb,OAAO;QACnC,MAAM,IAAIyB,MACR;IAEJ;IAEA,eAAeI,gBAAgBN,GAAoB,EAAEC,GAAmB;QACtE,IAAI;YACF,IAAIN,iBAAiB;gBACnB,MAAMA;gBACNA,kBAAkBY;YACpB;YACA,MAAMR,eAAeC,KAAKC;QAC5B,EAAE,OAAOO,KAAK;YACZP,IAAIQ,UAAU,GAAG;YACjBR,IAAIS,GAAG,CAAC;YACRjD,IAAIkD,KAAK,CAAC,CAAC,6BAA6B,EAAEX,IAAIY,GAAG,CAAC,CAAC;YACnDC,QAAQF,KAAK,CAACH;QAChB,SAAU;YACR,IAAI/B,OAAO;gBACT,IACErB,GAAG0D,iBAAiB,GAAGC,cAAc,GACrC,MAAM3D,GAAG0D,iBAAiB,GAAGE,eAAe,EAC5C;oBACAvD,IAAIwD,IAAI,CACN,CAAC,8DAA8D,CAAC;oBAElE1B,QAAQ2B,IAAI,CAACvD;gBACf;YACF;QACF;IACF;IAEA,MAAMe,SAASY,wBACX/B,MAAM4D,YAAY,CAChB;QACEC,KAAKjE,GAAGkE,YAAY,CAAC/B,sBAAsB8B,GAAG;QAC9CE,MAAMnE,GAAGkE,YAAY,CAAC/B,sBAAsBgC,IAAI;IAClD,GACAhB,mBAEFhD,KAAK6D,YAAY,CAACb;IAEtB,IAAIxB,kBAAkB;QACpBJ,OAAOI,gBAAgB,GAAGA;IAC5B;IACAJ,OAAO6C,EAAE,CAAC,WAAW,OAAOvB,KAAKI,QAAQC;QACvC,IAAI;YACF,MAAMF,eAAeH,KAAKI,QAAQC;QACpC,EAAE,OAAOG,KAAK;YACZJ,OAAOoB,OAAO;YACd/D,IAAIkD,KAAK,CAAC,CAAC,6BAA6B,EAAEX,IAAIY,GAAG,CAAC,CAAC;YACnDC,QAAQF,KAAK,CAACH;QAChB;IACF;IAEA,IAAIiB,iBAAiB;IAErB/C,OAAO6C,EAAE,CAAC,SAAS,CAACf;QAClB,IACEpB,cACAZ,QACAC,SACA+B,IAAIkB,IAAI,KAAK,gBACbD,iBAAiB,IACjB;YACAhE,IAAIwD,IAAI,CAAC,CAAC,KAAK,EAAEzC,KAAK,mBAAmB,EAAEA,OAAO,EAAE,SAAS,CAAC;YAC9DA,QAAQ;YACRiD,kBAAkB;YAClB/C,OAAOiD,MAAM,CAACnD,MAAMG;QACtB,OAAO;YACLlB,IAAIkD,KAAK,CAAC,CAAC,sBAAsB,CAAC;YAClCE,QAAQF,KAAK,CAACH;YACdjB,QAAQ2B,IAAI,CAAC;QACf;IACF;IAEA,MAAMU,gBAAgBhE;IAEtB,MAAM,IAAIgC,QAAc,CAACC;QACvBnB,OAAO6C,EAAE,CAAC,aAAa;YACrB,MAAMM,OAAOnD,OAAOoD,OAAO;YAC3B,MAAMC,iBAAiBjE,eACrB,OAAO+D,SAAS,WACZA,CAAAA,wBAAAA,KAAMC,OAAO,KAAInD,YAAY,cAC7BkD;YAEN,MAAMG,oBACJ,CAACrD,YAAYoD,mBAAmB,YAC5B,cACAA,mBAAmB,SACnB,UACAjE,eAAea;YAErBH,OAAO,OAAOqD,SAAS,WAAWA,CAAAA,wBAAAA,KAAMrD,IAAI,KAAIA,OAAOA;YAEvD,MAAMyD,aAAatD,WAAW,CAAC,OAAO,EAAEoD,eAAe,CAAC,EAAEvD,KAAK,CAAC,GAAG;YACnE,MAAM0D,SAAS,CAAC,EACd5C,wBAAwB,UAAU,OACnC,GAAG,EAAE0C,kBAAkB,CAAC,EAAExD,KAAK,CAAC;YAEjC,IAAIoD,eAAe;gBACjB,MAAMO,YAAYtE;gBAClBJ,IAAI2E,IAAI,CACN,CAAC,MAAM,EAAER,cAAc,4EAA4E,EAAEO,UAAU,CAAC,CAAC;YAErH;YAEA,yCAAyC;YACzC5C,QAAQ8C,GAAG,CAACC,IAAI,GAAG9D,OAAO;YAE1B,0DAA0D;YAC1D,IAAI+D;YACJ,IAAIC;YACJ,IAAI/D,OAAO;gBACT,MAAMgE,kBAAkB,MAAMxE,mBAAmBM;gBACjDgE,UAAUE,gBAAgBF,OAAO;gBACjCC,iBAAiBC,gBAAgBD,cAAc;YACjD;YACAtE,aAAa;gBACX+D;gBACAC;gBACAK;gBACAC;gBACAE,yBAAyB;YAC3B;YAEA,IAAI;gBACF,MAAMC,UAAU,CAACjB;oBACfrD,MAAM;oBACNK,OAAOkE,KAAK;oBACZrD,QAAQ2B,IAAI,CAACQ,QAAQ;gBACvB;gBACA,MAAMmB,YAAY,CAACrC;oBACjB,IAAIpC,WAAWoC,MAAM;wBACnB,0EAA0E;wBAC1E,qDAAqD;wBACrD;oBACF;oBAEA,uDAAuD;oBACvDK,QAAQF,KAAK,CAACH;gBAChB;gBACAjB,QAAQgC,EAAE,CAAC,QAAQ,CAACG,OAASiB,QAAQjB;gBACrC,+CAA+C;gBAC/CnC,QAAQgC,EAAE,CAAC,UAAU,IAAMoB,QAAQ;gBACnCpD,QAAQgC,EAAE,CAAC,WAAW,IAAMoB,QAAQ;gBACpCpD,QAAQgC,EAAE,CAAC,oBAAoB;gBAC7B,sEAAsE;gBACtE,uEAAuE;gBACvE,6DAA6D;gBAC/D;gBACAhC,QAAQgC,EAAE,CAAC,qBAAqBsB;gBAChCtD,QAAQgC,EAAE,CAAC,sBAAsBsB;gBAEjC,MAAMC,aAAa,MAAMxE,mBAAmB;oBAC1CC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiBkE,QAAQnB;oBACzB9C;oBACAC,uBAAuB,CAAC,CAACM;oBACzBL,yBAAyB,CAAC,CAACM;gBAC7B;gBACAS,iBAAiB+C,UAAU,CAAC,EAAE;gBAC9B3C,iBAAiB2C,UAAU,CAAC,EAAE;gBAE9B,MAAME,6BACJjG,YAAYG,IAAI,CAAC,qBACjBH,YAAYkG,OAAO,CACjB,uBACA,cACA,kBACAC,QAAQ;gBAEZzD;gBACA,MAAM0D,qBACJH,6BAA6B,OACzB,CAAC,EAAEI,KAAKC,KAAK,CAACL,6BAA6B,OAAO,GAAG,CAAC,CAAC,GACvD,CAAC,EAAEI,KAAKC,KAAK,CAACL,4BAA4B,EAAE,CAAC;gBAEnDvF,IAAI6F,KAAK,CAAC,CAAC,SAAS,EAAEH,mBAAmB,CAAC;gBAE1C,IAAI5D,QAAQ8C,GAAG,CAACkB,SAAS,EAAE;oBACzB,MAAMpF,wBAAwB;wBAC5B,GAAGgB,aAAa;wBAChBV,OAAO;oBACT;gBACF;YACF,EAAE,OAAO+B,KAAK;gBACZ,gCAAgC;gBAChCd;gBACAmB,QAAQF,KAAK,CAACH;gBACdjB,QAAQ2B,IAAI,CAAC;YACf;YAEArB;QACF;QACAnB,OAAOiD,MAAM,CAACnD,MAAMG;IACtB;IAEA,IAAIF,OAAO;QACT,SAAS+E,iBACPC,UAAkB,EAClBC,QAAoC;YAEpC,MAAMC,KAAK,IAAInG;YACfmG,GAAGC,KAAK,CAAC;gBACPC,OAAO7F,aAAa8F,GAAG,CAAC,CAACC,OAAS1G,KAAK2G,IAAI,CAACP,YAAYM;YAC1D;YACAJ,GAAGpC,EAAE,CAAC,UAAUmC;QAClB;QACAF,iBAAiBjF,KAAK,OAAO0F;YAC3B,IAAI1E,QAAQ8C,GAAG,CAAC6B,6BAA6B,EAAE;gBAC7CzG,IAAI2E,IAAI,CACN,CAAC,qFAAqF,CAAC;gBAEzF;YACF;YAEA3E,IAAIwD,IAAI,CACN,CAAC,kBAAkB,EAAE5D,KAAK8G,QAAQ,CAChCF,UACA,+CAA+C,CAAC;YAEpD1E,QAAQ2B,IAAI,CAACvD;QACf;IACF;AACF;AAEA,IAAI4B,QAAQ8C,GAAG,CAAC+B,mBAAmB,IAAI7E,QAAQ8E,IAAI,EAAE;IACnD9E,QAAQ+E,WAAW,CAAC,WAAW,OAAOC;QACpC,IAAIA,OAAO,OAAOA,OAAOA,IAAIC,iBAAiB,IAAIjF,QAAQ8E,IAAI,EAAE;YAC9D,MAAMnF,YAAYqF,IAAIC,iBAAiB;YACvCjF,QAAQ8E,IAAI,CAAC;gBAAEI,iBAAiB;YAAK;QACvC;IACF;IACAlF,QAAQ8E,IAAI,CAAC;QAAEK,iBAAiB;IAAK;AACvC"}