{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/core/index.ts"], "names": [], "mappings": "AAAA,UAAU;AAGV,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAE/D,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAkBvD,OAAO,EAAE,aAAa,EAAE,uBAAuB,EAAE,MAAM,oBAAoB,CAAC;AAG5E,OAAO,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AAoBhE,OAAO,EACL,KAAK,EACL,WAAW,EACX,aAAa,EACb,iBAAiB,EACjB,uBAAuB,EACvB,OAAO,GACR,MAAM,mBAAmB,CAAC;AAI3B,UAAU;AAEV,cAAc,uBAAuB,CAAC;AACtC,cAAc,uBAAuB,CAAC;AAEtC,OAAO,EACL,SAAS,EACT,SAAS,EACT,WAAW,EACX,gBAAgB,GACjB,MAAM,wBAAwB,CAAC;AAqBhC,OAAO,EACL,iBAAiB,EACjB,UAAU,EACV,WAAW,EACX,aAAa,GACd,MAAM,uBAAuB,CAAC;AAE/B,gBAAgB;AAEhB,yEAAyE;AACzE,qEAAqE;AACrE,uEAAuE;AACvE,6DAA6D;AAC7D,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAC5C,OAAO,EAAE,YAAY,IAAI,eAAe,EAAE,CAAC;AAC3C,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;AAEzC,0DAA0D;AAC1D,qEAAqE;AACrE,sEAAsE;AACtE,yEAAyE;AACzE,yCAAyC;AACzC,wEAAwE;AACxE,6EAA6E;AAC7E,8EAA8E;AAC9E,sEAAsE;AACtE,qEAAqE;AACrE,4EAA4E;AAC5E,6BAA6B;AAC7B,OAAO,EACL,GAAG,EACH,WAAW,EACX,uBAAuB,EACvB,mCAAmC,EACnC,oCAAoC,GACrC,MAAM,aAAa,CAAC", "sourcesContent": ["/* Core */\n\nexport type { ApolloClientOptions, DefaultOptions } from \"./ApolloClient.js\";\nexport { ApolloClient, mergeOptions } from \"./ApolloClient.js\";\nexport type { FetchMoreOptions } from \"./ObservableQuery.js\";\nexport { ObservableQuery } from \"./ObservableQuery.js\";\nexport type {\n  QueryOptions,\n  WatchQueryOptions,\n  MutationOptions,\n  SubscriptionOptions,\n  FetchPolicy,\n  WatchQueryFetchPolicy,\n  MutationFetchPolicy,\n  RefetchWritePolicy,\n  ErrorPolicy,\n  FetchMoreQueryOptions,\n  SubscribeToMoreOptions,\n  SubscribeToMoreFunction,\n  UpdateQueryMapFn,\n  UpdateQueryOptions,\n  SubscribeToMoreUpdateQueryFn,\n} from \"./watchQueryOptions.js\";\nexport { NetworkStatus, isNetworkRequestSettled } from \"./networkStatus.js\";\nexport type * from \"./types.js\";\nexport type { Resolver, FragmentMatcher } from \"./LocalState.js\";\nexport { isA<PERSON>lo<PERSON>rror, ApolloError } from \"../errors/index.js\";\n/* Cache */\n\nexport type {\n  // All the exports (types) from ../cache, minus cacheSlot,\n  // which we want to keep semi-private.\n  Transaction,\n  DataProxy,\n  InMemoryCacheConfig,\n  ReactiveVar,\n  TypePolicies,\n  TypePolicy,\n  FieldPolicy,\n  FieldReadFunction,\n  FieldMergeFunction,\n  FieldFunctionOptions,\n  PossibleTypesMap,\n  WatchFragmentOptions,\n  WatchFragmentResult,\n} from \"../cache/index.js\";\nexport {\n  Cache,\n  ApolloCache,\n  InMemoryCache,\n  MissingFieldError,\n  defaultDataIdFromObject,\n  makeVar,\n} from \"../cache/index.js\";\n\nexport type * from \"../cache/inmemory/types.js\";\n\n/* Link */\n\nexport * from \"../link/core/index.js\";\nexport * from \"../link/http/index.js\";\nexport type { ServerError } from \"../link/utils/index.js\";\nexport {\n  fromError,\n  toPromise,\n  fromPromise,\n  throwServerError,\n} from \"../link/utils/index.js\";\n\n/* Masking */\nexport type {\n  DataMasking,\n  FragmentType,\n  Masked,\n  MaskedDocumentNode,\n  MaybeMasked,\n  Unmasked,\n} from \"../masking/index.js\";\n\n/* Utilities */\n\nexport type {\n  DocumentTransformCacheKey,\n  Observer,\n  ObservableSubscription,\n  Reference,\n  StoreObject,\n} from \"../utilities/index.js\";\nexport {\n  DocumentTransform,\n  Observable,\n  isReference,\n  makeReference,\n} from \"../utilities/index.js\";\n\n/* Supporting */\n\n// The verbosity of invariant.{log,warn,error} can be controlled globally\n// (for anyone using the same ts-invariant package) by passing \"log\",\n// \"warn\", \"error\", or \"silent\" to setVerbosity (\"log\" is the default).\n// Note that all invariant.* logging is hidden in production.\nimport { setVerbosity } from \"ts-invariant\";\nexport { setVerbosity as setLogVerbosity };\nsetVerbosity(__DEV__ ? \"log\" : \"silent\");\n\n// Note that importing `gql` by itself, then destructuring\n// additional properties separately before exporting, is intentional.\n// Due to the way the `graphql-tag` library is setup, certain bundlers\n// can't find the properties added to the exported `gql` function without\n// additional guidance (e.g. Rollup - see\n// https://rollupjs.org/guide/en/#error-name-is-not-exported-by-module).\n// Instead of having people that are using bundlers with `@apollo/client` add\n// extra bundler config to help `graphql-tag` exports be found (which would be\n// awkward since they aren't importing `graphql-tag` themselves), this\n// workaround of pulling the extra properties off the `gql` function,\n// then re-exporting them separately, helps keeps bundlers happy without any\n// additional config changes.\nexport {\n  gql,\n  resetCaches,\n  disableFragmentWarnings,\n  enableExperimentalFragmentVariables,\n  disableExperimentalFragmentVariables,\n} from \"graphql-tag\";\n"]}