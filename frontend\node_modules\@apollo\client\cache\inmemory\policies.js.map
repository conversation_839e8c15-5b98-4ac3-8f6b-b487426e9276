{"version": 3, "file": "policies.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/policies.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AAehF,OAAO,EACL,qBAAqB,EACrB,wBAAwB,EACxB,WAAW,EACX,eAAe,EACf,eAAe,EACf,mBAAmB,GACpB,MAAM,0BAA0B,CAAC;AAOlC,OAAO,EACL,MAAM,EACN,sBAAsB,EACtB,uBAAuB,EACvB,yBAAyB,EACzB,qBAAqB,EACrB,uBAAuB,EACvB,OAAO,GACR,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAY9C,OAAO,EACL,sBAAsB,EACtB,wBAAwB,GACzB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAE,mBAAmB,EAAE,MAAM,wBAAwB,CAAC;AAkH7D,SAAS,sBAAsB,CAAC,IAAoB;IAClD,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;QAChC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC;YACnE,CAAC,CAAC,IAAI,CACP,CAAC;AACJ,CAAC;AA6FD,IAAM,eAAe,GAAsB,cAAM,OAAA,KAAK,CAAC,EAAN,CAAM,CAAC;AACxD,IAAM,eAAe,GAAoB,UAAC,KAAK,EAAE,OAAO,IAAK,OAAA,OAAO,CAAC,SAAS,EAAjB,CAAiB,CAAC;AAE/E,oEAAoE;AACpE,iCAAiC;AACjC,IAAM,WAAW,GAA4B,UAC3C,QAAQ,EACR,QAAQ,EACR,EAAgB;QAAd,YAAY,kBAAA;IACX,OAAA,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;AAAhC,CAAgC,CAAC;AACtC,IAAM,YAAY,GAA4B,UAAC,CAAC,EAAE,QAAQ,IAAK,OAAA,QAAQ,EAAR,CAAQ,CAAC;AAMxE;IAwCE,kBACU,MAKP;QALO,WAAM,GAAN,MAAM,CAKb;QA7CK,iBAAY,GAYhB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEhB,cAAS,GAEb,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAExB,oEAAoE;QACpE,wEAAwE;QACxE,uEAAuE;QACvE,8DAA8D;QACtD,iBAAY,GAAG,IAAI,GAAG,EAAuB,CAAC;QAEtD,qEAAqE;QACrE,sEAAsE;QACtE,qEAAqE;QACrE,kEAAkE;QAC1D,kBAAa,GAAG,IAAI,GAAG,EAAkB,CAAC;QAIlC,sBAAiB,GAC/B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACN,sBAAiB,GAC/B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEN,uBAAkB,GAAG,KAAK,CAAC;QAUzC,IAAI,CAAC,MAAM,cACT,gBAAgB,EAAE,uBAAuB,IACtC,MAAM,CACV,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QAE/B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAC9B,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QACjC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QAErC,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACzB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAEM,2BAAQ,GAAf,UACE,MAAmB,EACnB,cAA0C;;QAE1C,IAAM,QAAQ,GAAG,IAAI,CAAC;QAEtB,IAAM,QAAQ,GACZ,CAAC,cAAc;YACb,CAAC,cAAc,CAAC,QAAQ,KAAI,MAAA,cAAc,CAAC,WAAW,0CAAE,UAAU,CAAA,CAAC,CAAC;YACtE,MAAM,CAAC,UAAU,CAAC;QAEpB,uEAAuE;QACvE,uEAAuE;QACvE,uEAAuE;QACvE,qEAAqE;QACrE,6DAA6D;QAC7D,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC;YACnD,OAAO,CAAC,YAAY,CAAC,CAAC;QACxB,CAAC;QAED,mEAAmE;QACnE,IAAM,WAAW,GACf,CAAC,cAAc,IAAI,cAAc,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC;QAE3D,IAAM,OAAO,yBACR,cAAc,KACjB,QAAQ,UAAA,EACR,WAAW,aAAA,EACX,SAAS,EACP,CAAC,cAAc,IAAI,cAAc,CAAC,SAAS,CAAC;gBAC5C;oBACE,IAAM,OAAO,GAAG,yBAAyB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;oBAClE,OAAO,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE;wBACjC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;wBAC7B,SAAS,EAAE,OAAO,CAAC,SAAS;qBAC7B,CAAC,CAAC;gBACL,CAAC,GACJ,CAAC;QAEF,IAAI,EAAmB,CAAC;QAExB,IAAM,MAAM,GAAG,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,KAAK,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAErE,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE;YAClC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAM,aAAa,GAAG,KAAK,uBAAM,MAAM,GAAK,WAAW,GAAI,OAAO,CAAC,CAAC;gBACpE,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;oBAC3B,KAAK,GAAG,wBAAwB,CAAC,aAAa,CAAC,CAAC;gBAClD,CAAC;qBAAM,CAAC;oBACN,EAAE,GAAG,aAAa,CAAC;oBACnB,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAC9B,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5D,CAAC;IAEM,kCAAe,GAAtB,UAAuB,YAA0B;QAAjD,iBA6BC;QA5BC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,UAAC,QAAQ;YACzC,IAAM,KACJ,YAAY,CAAC,QAAQ,CAAC,EADhB,SAAS,eAAA,EAAE,YAAY,kBAAA,EAAE,gBAAgB,sBAAA,EAAK,QAAQ,cAAxD,iDAA0D,CACxC,CAAC;YAEzB,oEAAoE;YACpE,+DAA+D;YAC/D,+DAA+D;YAC/D,oEAAoE;YACpE,oEAAoE;YACpE,iEAAiE;YACjE,mEAAmE;YACnE,oEAAoE;YACpE,mEAAmE;YACnE,oEAAoE;YACpE,mEAAmE;YACnE,mEAAmE;YACnE,mEAAmE;YACnE,4BAA4B;YAC5B,IAAI,SAAS;gBAAE,KAAI,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACvD,IAAI,YAAY;gBAAE,KAAI,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC7D,IAAI,gBAAgB;gBAAE,KAAI,CAAC,eAAe,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YAErE,IAAI,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC1C,KAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACN,KAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACxC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mCAAgB,GAAxB,UAAyB,QAAgB,EAAE,QAAoB;QAA/D,iBAyEC;QAxEC,IAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAA,SAAS,GAAa,QAAQ,UAArB,EAAE,MAAM,GAAK,QAAQ,OAAb,CAAc;QAEvC,SAAS,QAAQ,CACf,QAAkD,EAClD,KAAoC;YAEpC,QAAQ,CAAC,KAAK;gBACZ,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK;oBACjC,4DAA4D;oBAC5D,yDAAyD;oBAC3D,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,WAAW;wBAC5B,4DAA4D;wBAC5D,8CAA8C;wBAChD,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,YAAY;4BAChC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrB,CAAC;QAED,iEAAiE;QACjE,+CAA+C;QAC/C,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEnC,QAAQ,CAAC,KAAK;YACZ,yDAAyD;YACzD,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,eAAe;gBACnC,4DAA4D;gBAC5D,6CAA6C;gBAC/C,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAC,SAAS,CAAC;oBACxD,4DAA4D;oBAC9D,CAAC,CAAC,OAAO,SAAS,KAAK,UAAU,CAAC,CAAC,CAAC,SAAS;wBAC3C,sDAAsD;wBACxD,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;QAEnB,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAC,SAAS;gBACpC,IAAM,QAAQ,GAAG,KAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAE,CAAC;gBACjE,IAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;gBAEnC,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;oBACnC,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC;gBAC3B,CAAC;qBAAM,CAAC;oBACE,IAAA,OAAO,GAAkB,QAAQ,QAA1B,EAAE,IAAI,GAAY,QAAQ,KAApB,EAAE,KAAK,GAAK,QAAQ,MAAb,CAAc;oBAE1C,QAAQ,CAAC,KAAK;wBACZ,0DAA0D;wBAC1D,oBAAoB;wBACpB,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC,eAAe;4BACjC,qDAAqD;4BACrD,8CAA8C;4BAChD,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,OAAO,CAAC;gCAClD,4DAA4D;gCAC9D,CAAC,CAAC,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO;oCACvC,sDAAsD;oCACxD,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;oBAEnB,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;wBAC/B,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;oBACvB,CAAC;oBAED,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;gBAC5B,CAAC;gBAED,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;oBACpC,sDAAsD;oBACtD,0DAA0D;oBAC1D,6DAA6D;oBAC7D,4DAA4D;oBAC5D,yCAAyC;oBACzC,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,eAAe,CAAC;gBACrD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,kCAAe,GAAvB,UACE,KAA4C,EAC5C,QAAwB;QAAxB,yBAAA,EAAA,gBAAwB;QAExB,IAAM,MAAM,GAAG,OAAO,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAC7C,IAAM,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,QAAQ,KAAK,GAAG,EAAE,CAAC;YACrB,SAAS,CACP,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,EACrB,iDAAiD,EACjD,KAAK,CACN,CAAC;YACF,oEAAoE;YACpE,qBAAqB;YACrB,IAAI,GAAG;gBAAE,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAC5C,8DAA8D;YAC9D,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;YAC1C,8DAA8D;YAC9D,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;QAC5C,CAAC;IACH,CAAC;IAEM,mCAAgB,GAAvB,UAAwB,aAA+B;QAAvD,iBAiBC;QAhBE,IAAI,CAAC,kBAA8B,GAAG,IAAI,CAAC;QAC5C,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAC,SAAS;YAC3C,kEAAkE;YAClE,mEAAmE;YACnE,wDAAwD;YACxD,KAAI,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;YAEtC,aAAa,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAC,OAAO;gBACvC,KAAI,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACpD,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;gBACnD,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;oBACnC,8DAA8D;oBAC9D,KAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gCAAa,GAArB,UAAsB,QAAgB;QAAtC,iBAwEC;QAvEC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC;YAC9C,IAAM,QAAM,GAAqC,CAAC,IAAI,CAAC,YAAY,CACjE,QAAQ,CACT,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB,QAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAEpC,iEAAiE;YACjE,kEAAkE;YAClE,+DAA+D;YAC/D,EAAE;YACF,oEAAoE;YACpE,+DAA+D;YAC/D,gEAAgE;YAChE,gEAAgE;YAChE,8DAA8D;YAC9D,gEAAgE;YAChE,oCAAoC;YACpC,EAAE;YACF,kEAAkE;YAClE,oEAAoE;YACpE,oEAAoE;YACpE,+CAA+C;YAC/C,EAAE;YACF,yEAAyE;YACzE,yEAAyE;YACzE,qEAAqE;YACrE,4DAA4D;YAC5D,IAAI,YAAU,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,CAAC,YAAU,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;gBAC3C,uEAAuE;gBACvE,sEAAsE;gBACtE,iEAAiE;gBACjE,YAAU,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAE,CAAC;gBACnD,qEAAqE;gBACrE,mEAAmE;gBACnE,mEAAmE;gBACnE,wEAAwE;gBACxE,0BAA0B;gBAC1B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,KAAK;oBACvC,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC1B,oEAAoE;wBACpE,0DAA0D;wBAC1D,gDAAgD;wBAChD,IAAM,eAAe,GAAG,KAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACrD,IAAI,eAAe,EAAE,CAAC;4BACpB,eAAe,CAAC,OAAO,CAAC,UAAC,SAAS;gCAChC,OAAA,YAAW,CAAC,GAAG,CAAC,SAAS,CAAC;4BAA1B,CAA0B,CAC3B,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,YAAU,IAAI,YAAU,CAAC,IAAI,EAAE,CAAC;gBAClC,YAAU,CAAC,OAAO,CAAC,UAAC,SAAS;oBAC3B,IAAM,KAAsB,KAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAjD,MAAM,YAAA,EAAK,IAAI,cAAjB,UAAmB,CAAgC,CAAC;oBAC1D,MAAM,CAAC,MAAM,CAAC,QAAM,EAAE,IAAI,CAAC,CAAC;oBAC5B,MAAM,CAAC,MAAM,CAAC,QAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACvC,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACvC,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YAC1B,uEAAuE;YACvE,2CAA2C;YAC3C,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM;gBAC7B,KAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC1C,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;IACrC,CAAC;IAEO,iCAAc,GAAtB,UACE,QAA4B,EAC5B,SAAiB,EACjB,eAAwB;QAQxB,IAAI,QAAQ,EAAE,CAAC;YACb,IAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;YAC1D,OAAO,CACL,aAAa,CAAC,SAAS,CAAC;gBACxB,CAAC,eAAe,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CACtE,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,kCAAe,GAAvB,UACE,OAAe,EACf,eAAwB;QAExB,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,YAAY,IAAI,eAAe,EAAE,CAAC;YACrC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC,CAAC,CAAC;QACrE,CAAC;QACD,OAAO,YAAY,CAAC;IACtB,CAAC;IAEM,kCAAe,GAAtB,UACE,QAAqD,EACrD,QAA4B,EAC5B,MAA4B,EAC5B,SAA+B;QAJjC,iBAgGC;QA1FC,IAAI,CAAC,QAAQ,CAAC,aAAa;YAAE,OAAO,IAAI,CAAC;QAEzC,qEAAqE;QACrE,iEAAiE;QACjE,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QAE5B,IAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;QACpD,oEAAoE;QACpE,IAAI,QAAQ,KAAK,SAAS;YAAE,OAAO,IAAI,CAAC;QAExC,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAChE,IAAM,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAE,CAAC;YACnE,IAAM,WAAS,GAAG,CAAC,oBAAoB,CAAC,CAAC;YACzC,IAAM,cAAY,GAAG,UAAC,OAAe;gBACnC,IAAM,YAAY,GAAG,KAAI,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC1D,IACE,YAAY;oBACZ,YAAY,CAAC,IAAI;oBACjB,WAAS,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,EACnC,CAAC;oBACD,WAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC,CAAC;YAEF,+DAA+D;YAC/D,iEAAiE;YACjE,iEAAiE;YACjE,kEAAkE;YAClE,+DAA+D;YAC/D,0DAA0D;YAC1D,IAAI,wBAAwB,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACrE,IAAI,qBAAqB,GAAG,KAAK,CAAC;YAElC,uEAAuE;YACvE,sEAAsE;YACtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAS,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBAC1C,IAAM,YAAY,GAAG,WAAS,CAAC,CAAC,CAAC,CAAC;gBAElC,IAAI,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;oBAChC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;wBACzC,IAAI,qBAAqB,EAAE,CAAC;4BAC1B,SAAS,CAAC,IAAI,CACZ,sCAAsC,EACtC,QAAQ,EACR,SAAS,CACV,CAAC;wBACJ,CAAC;wBACD,oDAAoD;wBACpD,0DAA0D;wBAC1D,8DAA8D;wBAC9D,kBAAkB;wBAClB,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBACtC,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED,YAAY,CAAC,OAAO,CAAC,cAAY,CAAC,CAAC;gBAEnC,IACE,wBAAwB;oBACxB,0DAA0D;oBAC1D,8DAA8D;oBAC9D,CAAC,KAAK,WAAS,CAAC,MAAM,GAAG,CAAC;oBAC1B,2DAA2D;oBAC3D,8DAA8D;oBAC9D,yDAAyD;oBACzD,wDAAwD;oBACxD,yBAAyB,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAO,EAAE,SAAS,CAAC,EACpE,CAAC;oBACD,6DAA6D;oBAC7D,+DAA+D;oBAC/D,mCAAmC;oBACnC,wBAAwB,GAAG,KAAK,CAAC;oBACjC,qBAAqB,GAAG,IAAI,CAAC;oBAE7B,gEAAgE;oBAChE,4DAA4D;oBAC5D,4DAA4D;oBAC5D,sBAAsB;oBACtB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,WAAW;wBAC7C,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;wBACrC,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;4BACnC,cAAY,CAAC,WAAW,CAAC,CAAC;wBAC5B,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,6BAAU,GAAjB,UAAkB,QAA4B,EAAE,SAAiB;QAC/D,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAC/D,OAAO,CAAC,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAEM,oCAAiB,GAAxB,UAAyB,SAAyB;QACxC,IAAA,QAAQ,GAAgB,SAAS,SAAzB,EAAE,SAAS,GAAK,SAAS,UAAd,CAAe;QAC1C,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAC/D,IAAI,cAAkE,CAAC;QAEvE,IAAI,KAAK,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;QACnC,IAAI,KAAK,IAAI,QAAQ,EAAE,CAAC;YACtB,IAAM,OAAO,GAAmC;gBAC9C,QAAQ,UAAA;gBACR,SAAS,WAAA;gBACT,KAAK,EAAE,SAAS,CAAC,KAAK,IAAI,IAAI;gBAC9B,SAAS,EAAE,SAAS,CAAC,SAAS;aAC/B,CAAC;YACF,IAAM,IAAI,GAAG,sBAAsB,CAAC,SAAS,CAAC,CAAC;YAC/C,OAAO,KAAK,EAAE,CAAC;gBACb,IAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAC/C,IAAI,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;oBAC/B,KAAK,GAAG,sBAAsB,CAAC,iBAAiB,CAAC,CAAC;gBACpD,CAAC;qBAAM,CAAC;oBACN,0DAA0D;oBAC1D,qBAAqB;oBACrB,cAAc,GAAG,iBAAiB,IAAI,SAAS,CAAC;oBAChD,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,cAAc,KAAK,KAAK,CAAC,EAAE,CAAC;YAC9B,cAAc;gBACZ,SAAS,CAAC,KAAK,CAAC,CAAC;oBACf,qBAAqB,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,SAAS,CAAC;oBAC7D,CAAC,CAAC,eAAe,CAAC,SAAS,EAAE,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAC;QACpE,CAAC;QAED,8DAA8D;QAC9D,oCAAoC;QACpC,IAAI,cAAc,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,sEAAsE;QACtE,kEAAkE;QAClE,wDAAwD;QACxD,OAAO,SAAS,KAAK,sBAAsB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc;YAC1E,CAAC,CAAC,SAAS,GAAG,GAAG,GAAG,cAAc,CAAC;IACvC,CAAC;IAEM,4BAAS,GAAhB,UACE,OAAyB,EACzB,OAA+B;QAE/B,IAAM,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;QACvC,IAAI,CAAC,iBAAiB;YAAE,OAAO;QAE/B,IAAM,WAAW,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,SAAS,CAAC;QACvD,IAAI,CAAC,WAAW;YAAE,OAAO;QAEzB,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,EAAE,CAAC;YAChC,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAC1C,iBAAiB,EACjB,YAAY,CACb,CAAC;YACF,IAAI,QAAQ;gBAAE,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC5C,CAAC;QAED,IAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,IAAM,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;QACzD,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAC1C,iBAAiB,EACjB,cAAc,CACf,CAAC;QACF,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QACvE,IAAM,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC;QAEnC,IAAI,IAAI,EAAE,CAAC;YACT,IAAM,WAAW,GAAG,wBAAwB,CAC1C,IAAI,EACJ,iBAAiB,EACjB,OAAO,EACP,OAAO,EACP,OAAO,CAAC,KAAK,CAAC,UAAU,CACtB,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBAC9B,iBAAiB,CAAC,KAAK;gBACzB,CAAC,CAAC,iBAAiB,EACnB,cAAc,CACf,CACF,CAAC;YAEF,sEAAsE;YACtE,OAAO,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE;gBAC3C,QAAQ;gBACR,WAAW;aACZ,CAAoB,CAAC;QACxB,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,kCAAe,GAAtB,UACE,QAA4B,EAC5B,SAAiB;QAEjB,IAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAC/D,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC;IAC/B,CAAC;IAEM,mCAAgB,GAAvB,UACE,cAAkC,EAClC,SAAiB,EACjB,aAAiC;QAEjC,IAAI,MAAM,GAGM,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QACtE,IAAI,KAAK,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;QACnC,IAAI,CAAC,KAAK,IAAI,aAAa,EAAE,CAAC;YAC5B,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YAC3C,KAAK,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;QACjC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,mCAAgB,GAAvB,UACE,QAAoB,EACpB,QAAoB,EACpB,EAAqC,EACrC,OAAqB,EACrB,OAAqB;YAFnB,KAAK,WAAA,EAAE,QAAQ,cAAA,EAAE,KAAK,WAAA;QAIxB,IAAI,KAAK,KAAK,WAAW,EAAE,CAAC;YAC1B,qDAAqD;YACrD,8DAA8D;YAC9D,kDAAkD;YAClD,OAAO,wBAAwB,CAAC,OAAO,CAAC,KAAK,CAAC,CAC5C,QAAuB,EACvB,QAAuB,CACxB,CAAC;QACJ,CAAC;QAED,IAAI,KAAK,KAAK,YAAY,EAAE,CAAC;YAC3B,mEAAmE;YACnE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,6DAA6D;QAC7D,oEAAoE;QACpE,oEAAoE;QACpE,mEAAmE;QACnE,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,QAAQ,GAAG,KAAK,CAAC,CAAC;QACpB,CAAC;QAED,OAAO,KAAK,CACV,QAAQ,EACR,QAAQ,EACR,wBAAwB,CACtB,IAAI;QACJ,8DAA8D;QAC9D,gEAAgE;QAChE,+DAA+D;QAC/D,gEAAgE;QAChE,8DAA8D;QAC9D,6DAA6D;QAC7D,8DAA8D;QAC9D,8DAA8D;QAC9D,2DAA2D;QAC3D,8DAA8D;QAC9D,yDAAyD;QACzD,KAAK,CAAC,EACN;YACE,QAAQ,UAAA;YACR,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;YAC3B,KAAK,OAAA;YACL,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,EACD,OAAO,EACP,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAC/B,CACF,CAAC;IACJ,CAAC;IACH,eAAC;AAAD,CAAC,AAxpBD,IAwpBC;;AAED,SAAS,wBAAwB,CAC/B,QAAkB,EAClB,iBAAsD,EACtD,SAAyB,EACzB,OAA+B,EAC/B,OAAoB;IAEpB,IAAM,cAAc,GAAG,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC7D,IAAM,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;IACzD,IAAM,SAAS,GAAG,SAAS,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;IACrD,IAAA,KAA2B,OAAO,CAAC,KAAK,EAAtC,WAAW,iBAAA,EAAE,OAAO,aAAkB,CAAC;IAE/C,OAAO;QACL,IAAI,EAAE,sBAAsB,CAAC,SAAS,CAAC;QACvC,KAAK,EAAE,SAAS,CAAC,KAAK,IAAI,IAAI;QAC9B,SAAS,WAAA;QACT,cAAc,gBAAA;QACd,SAAS,WAAA;QACT,WAAW,aAAA;QACX,WAAW,aAAA;QACX,OAAO,SAAA;QACP,KAAK,EAAE,QAAQ,CAAC,KAAK;QACrB,OAAO,SAAA;QACP,SAAS;YACP,OAAO,QAAQ,CAAC,SAAS,CACvB,yBAAyB,CAAC,SAAS,EAAE,iBAAiB,EAAE,SAAS,CAAC,EAClE,OAAO,CACR,CAAC;QACJ,CAAC;QACD,YAAY,EAAE,wBAAwB,CAAC,OAAO,CAAC,KAAK,CAAC;KACtD,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,yBAAyB,CACvC,aAAyB,EACzB,iBAAsD,EACtD,SAA+C;IAEvC,IAAG,kBAAkB,GAA4B,aAAa,GAAzC,EAAK,IAAI,GAAmB,aAAa,GAAhC,EAAU,IAAI,GAAK,aAAa,OAAlB,CAAmB;IAEvE,IAAI,OAAyB,CAAC;IAE9B,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE,CAAC;QAC3C,OAAO,GAAG;YACR,SAAS,EAAE,kBAAkB;YAC7B,gEAAgE;YAChE,kEAAkE;YAClE,iCAAiC;YACjC,IAAI,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;SAC1C,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,gBAAQ,kBAAkB,CAAE,CAAC;QACpC,oEAAoE;QACpE,gDAAgD;QAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,GAAG,iBAAiB,CAAC;QACnC,CAAC;IACH,CAAC;IAED,IAAI,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;QACvC,SAAS,CAAC,IAAI,CACZ,wDAAwD,EACxD,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAC/C,CAAC;IACJ,CAAC;IAED,IAAI,KAAK,CAAC,KAAK,OAAO,CAAC,SAAS,EAAE,CAAC;QACjC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;IAChC,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,wBAAwB,CAC/B,KAAsB;IAEtB,OAAO,SAAS,YAAY,CAAC,QAAQ,EAAE,QAAQ;QAC7C,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3C,MAAM,iBAAiB,CAAC,mCAAmC,CAAC,CAAC;QAC/D,CAAC;QAED,iEAAiE;QACjE,kEAAkE;QAClE,qEAAqE;QACrE,iCAAiC;QACjC,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC3D,IAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAC1D,IAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YAC1D,IAAM,WAAW,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;YAEtD,IAAI,WAAW,EAAE,CAAC;gBAChB,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/D,iEAAiE;gBACjE,uEAAuE;gBACvE,8BAA8B;gBAC9B,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBACtC,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,IAAI,uBAAuB,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC/D,iEAAiE;gBACjE,uEAAuE;gBACvE,gEAAgE;gBAChE,gCAAgC;gBAChC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACtC,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,IACE,uBAAuB,CAAC,QAAQ,CAAC;gBACjC,uBAAuB,CAAC,QAAQ,CAAC,EACjC,CAAC;gBACD,6BAAY,QAAQ,GAAK,QAAQ,EAAG;YACtC,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\n\nimport type {\n  InlineFragmentNode,\n  FragmentDefinitionNode,\n  SelectionSetNode,\n  FieldNode,\n} from \"graphql\";\n\nimport type {\n  FragmentMap,\n  StoreValue,\n  StoreObject,\n  Reference,\n} from \"../../utilities/index.js\";\nimport {\n  storeKeyNameFromField,\n  argumentsObjectFromField,\n  isReference,\n  getStoreKeyName,\n  isNonNullObject,\n  stringifyForDisplay,\n} from \"../../utilities/index.js\";\nimport type {\n  IdGetter,\n  MergeInfo,\n  NormalizedCache,\n  ReadMergeModifyContext,\n} from \"./types.js\";\nimport {\n  hasOwn,\n  fieldNameFromStoreName,\n  storeValueIsStoreObject,\n  selectionSetMatchesResult,\n  TypeOrFieldNameRegExp,\n  defaultDataIdFromObject,\n  isArray,\n} from \"./helpers.js\";\nimport { cacheSlot } from \"./reactiveVars.js\";\nimport type { InMemoryCache } from \"./inMemoryCache.js\";\nimport type {\n  SafeReadonly,\n  FieldSpecifier,\n  ToReferenceFunction,\n  ReadFieldFunction,\n  ReadFieldOptions,\n  CanReadFunction,\n} from \"../core/types/common.js\";\nimport type { WriteContext } from \"./writeToStore.js\";\n\nimport {\n  keyArgsFnFromSpecifier,\n  keyFieldsFnFromSpecifier,\n} from \"./key-extractor.js\";\nimport { disableWarningsSlot } from \"../../masking/index.js\";\n\nexport type TypePolicies = {\n  [__typename: string]: TypePolicy;\n};\n\n// TypeScript 3.7 will allow recursive type aliases, so this should work:\n// type KeySpecifier = (string | KeySpecifier)[]\nexport type KeySpecifier = ReadonlyArray<string | KeySpecifier>;\n\nexport type KeyFieldsContext = {\n  // The __typename of the incoming object, even if the __typename field was\n  // aliased to another name in the raw result object. May be undefined when\n  // dataIdFromObject is called for objects without __typename fields.\n  typename: string | undefined;\n\n  // The object to be identified, after processing to remove aliases and\n  // normalize identifiable child objects with references.\n  storeObject: StoreObject;\n\n  // Handy tool for reading additional fields from context.storeObject, either\n  // readField(\"fieldName\") to read storeObject[fieldName], or readField(\"name\",\n  // objectOrReference) to read from another object or Reference. If you read a\n  // field with a read function, that function will be invoked.\n  readField: ReadFieldFunction;\n\n  // If you are writing a custom keyFields function, and you plan to use the raw\n  // result object passed as the first argument, you may also need access to the\n  // selection set and available fragments for this object, just in case any\n  // fields have aliases. Since this logic is tricky to get right, and these\n  // context properties are not even always provided (for example, they are\n  // omitted when calling cache.identify(object), where object is assumed to be\n  // a StoreObject), we recommend you use context.storeObject (which has already\n  // been de-aliased) and context.readField (which can read from references as\n  // well as objects) instead of the raw result object in your keyFields\n  // functions, or just rely on the internal implementation of keyFields:[...]\n  // syntax to get these details right for you.\n  selectionSet?: SelectionSetNode;\n  fragmentMap?: FragmentMap;\n\n  // Internal. May be set by the KeyFieldsFunction to report fields that were\n  // involved in computing the ID. Never passed in by the caller.\n  keyObject?: Record<string, any>;\n};\n\nexport type KeyFieldsFunction = (\n  object: Readonly<StoreObject>,\n  context: KeyFieldsContext\n) => KeySpecifier | false | ReturnType<IdGetter>;\n\ntype KeyFieldsResult = Exclude<ReturnType<KeyFieldsFunction>, KeySpecifier>;\n\n// TODO Should TypePolicy be a generic type, with a TObject or TEntity\n// type parameter?\nexport type TypePolicy = {\n  // Allows defining the primary key fields for this type, either using an\n  // array of field names or a function that returns an arbitrary string.\n  keyFields?: KeySpecifier | KeyFieldsFunction | false;\n\n  // Allows defining a merge function (or merge:true/false shorthand) to\n  // be used for merging objects of this type wherever they appear, unless\n  // the parent field also defines a merge function/boolean (that is,\n  // parent field merge functions take precedence over type policy merge\n  // functions). In many cases, defining merge:true for a given type\n  // policy can save you from specifying merge:true for all the field\n  // policies where that type might be encountered.\n  merge?: FieldMergeFunction | boolean;\n\n  // In the rare event that your schema happens to use a different\n  // __typename for the root Query, Mutation, and/or Schema types, you can\n  // express your deviant preferences by enabling one of these options.\n  queryType?: true;\n  mutationType?: true;\n  subscriptionType?: true;\n\n  fields?: {\n    [fieldName: string]: FieldPolicy<any> | FieldReadFunction<any>;\n  };\n};\n\nexport type KeyArgsFunction = (\n  args: Record<string, any> | null,\n  context: {\n    typename: string;\n    fieldName: string;\n    field: FieldNode | null;\n    variables?: Record<string, any>;\n  }\n) => KeySpecifier | false | ReturnType<IdGetter>;\n\nexport type FieldPolicy<\n  // The internal representation used to store the field's data in the\n  // cache. Must be JSON-serializable if you plan to serialize the result\n  // of cache.extract() using JSON.\n  TExisting = any,\n  // The type of the incoming parameter passed to the merge function,\n  // typically matching the GraphQL response format, but with Reference\n  // objects substituted for any identifiable child objects. Often the\n  // same as TExisting, but not necessarily.\n  TIncoming = TExisting,\n  // The type that the read function actually returns, using TExisting\n  // data and options.args as input. Usually the same as TIncoming.\n  TReadResult = TIncoming,\n  // Allows FieldFunctionOptions definition to be overwritten by the\n  // developer\n  TOptions extends FieldFunctionOptions = FieldFunctionOptions,\n> = {\n  keyArgs?: KeySpecifier | KeyArgsFunction | false;\n  read?: FieldReadFunction<TExisting, TReadResult, TOptions>;\n  merge?: FieldMergeFunction<TExisting, TIncoming, TOptions> | boolean;\n};\n\nexport type StorageType = Record<string, any>;\n\nfunction argsFromFieldSpecifier(spec: FieldSpecifier) {\n  return (\n    spec.args !== void 0 ? spec.args\n    : spec.field ? argumentsObjectFromField(spec.field, spec.variables)\n    : null\n  );\n}\n\nexport interface FieldFunctionOptions<\n  TArgs = Record<string, any>,\n  TVars = Record<string, any>,\n> {\n  args: TArgs | null;\n\n  // The name of the field, equal to options.field.name.value when\n  // options.field is available. Useful if you reuse the same function for\n  // multiple fields, and you need to know which field you're currently\n  // processing. Always a string, even when options.field is null.\n  fieldName: string;\n\n  // The full field key used internally, including serialized key arguments.\n  storeFieldName: string;\n\n  // The FieldNode object used to read this field. Useful if you need to\n  // know about other attributes of the field, such as its directives. This\n  // option will be null when a string was passed to options.readField.\n  field: FieldNode | null;\n\n  variables?: TVars;\n\n  // Utilities for dealing with { __ref } objects.\n  isReference: typeof isReference;\n  toReference: ToReferenceFunction;\n\n  // A handy place to put field-specific data that you want to survive\n  // across multiple read function calls. Useful for field-level caching,\n  // if your read function does any expensive work.\n  storage: StorageType;\n\n  cache: InMemoryCache;\n\n  // Helper function for reading other fields within the current object.\n  // If a foreign object or reference is provided, the field will be read\n  // from that object instead of the current object, so this function can\n  // be used (together with isReference) to examine the cache outside the\n  // current object. If a FieldNode is passed instead of a string, and\n  // that FieldNode has arguments, the same options.variables will be used\n  // to compute the argument values. Note that this function will invoke\n  // custom read functions for other fields, if defined. Always returns\n  // immutable data (enforced with Object.freeze in development).\n  readField: ReadFieldFunction;\n\n  // Returns true for non-normalized StoreObjects and non-dangling\n  // References, indicating that readField(name, objOrRef) has a chance of\n  // working. Useful for filtering out dangling references from lists.\n  canRead: CanReadFunction;\n\n  // Instead of just merging objects with { ...existing, ...incoming }, this\n  // helper function can be used to merge objects in a way that respects any\n  // custom merge functions defined for their fields.\n  mergeObjects: MergeObjectsFunction;\n}\n\ntype MergeObjectsFunction = <T extends StoreObject | Reference>(\n  existing: T,\n  incoming: T\n) => T;\n\nexport type FieldReadFunction<\n  TExisting = any,\n  TReadResult = TExisting,\n  TOptions extends FieldFunctionOptions = FieldFunctionOptions,\n> = (\n  // When reading a field, one often needs to know about any existing\n  // value stored for that field. If the field is read before any value\n  // has been written to the cache, this existing parameter will be\n  // undefined, which makes it easy to use a default parameter expression\n  // to supply the initial value. This parameter is positional (rather\n  // than one of the named options) because that makes it possible for the\n  // developer to annotate it with a type, without also having to provide\n  // a whole new type for the options object.\n  existing: SafeReadonly<TExisting> | undefined,\n  options: TOptions\n) => TReadResult | undefined;\n\nexport type FieldMergeFunction<\n  TExisting = any,\n  TIncoming = TExisting,\n  // Passing the whole FieldFunctionOptions makes the current definition\n  // independent from its implementation\n  TOptions extends FieldFunctionOptions = FieldFunctionOptions,\n> = (\n  existing: SafeReadonly<TExisting> | undefined,\n  // The incoming parameter needs to be positional as well, for the same\n  // reasons discussed in FieldReadFunction above.\n  incoming: SafeReadonly<TIncoming>,\n  options: TOptions\n) => SafeReadonly<TExisting>;\n\nconst nullKeyFieldsFn: KeyFieldsFunction = () => void 0;\nconst simpleKeyArgsFn: KeyArgsFunction = (_args, context) => context.fieldName;\n\n// These merge functions can be selected by specifying merge:true or\n// merge:false in a field policy.\nconst mergeTrueFn: FieldMergeFunction<any> = (\n  existing,\n  incoming,\n  { mergeObjects }\n) => mergeObjects(existing, incoming);\nconst mergeFalseFn: FieldMergeFunction<any> = (_, incoming) => incoming;\n\nexport type PossibleTypesMap = {\n  [supertype: string]: string[];\n};\n\nexport class Policies {\n  private typePolicies: {\n    [__typename: string]: {\n      keyFn?: KeyFieldsFunction;\n      merge?: FieldMergeFunction<any>;\n      fields: {\n        [fieldName: string]: {\n          keyFn?: KeyArgsFunction;\n          read?: FieldReadFunction<any>;\n          merge?: FieldMergeFunction<any>;\n        };\n      };\n    };\n  } = Object.create(null);\n\n  private toBeAdded: {\n    [__typename: string]: TypePolicy[];\n  } = Object.create(null);\n\n  // Map from subtype names to sets of supertype names. Note that this\n  // representation inverts the structure of possibleTypes (whose keys are\n  // supertypes and whose values are arrays of subtypes) because it tends\n  // to be much more efficient to search upwards than downwards.\n  private supertypeMap = new Map<string, Set<string>>();\n\n  // Any fuzzy subtypes specified by possibleTypes will be converted to\n  // RegExp objects and recorded here. Every key of this map can also be\n  // found in supertypeMap. In many cases this Map will be empty, which\n  // means no fuzzy subtype checking will happen in fragmentMatches.\n  private fuzzySubtypes = new Map<string, RegExp>();\n\n  public readonly cache: InMemoryCache;\n\n  public readonly rootIdsByTypename: Record<string, string> =\n    Object.create(null);\n  public readonly rootTypenamesById: Record<string, string> =\n    Object.create(null);\n\n  public readonly usingPossibleTypes = false;\n\n  constructor(\n    private config: {\n      cache: InMemoryCache;\n      dataIdFromObject?: KeyFieldsFunction;\n      possibleTypes?: PossibleTypesMap;\n      typePolicies?: TypePolicies;\n    }\n  ) {\n    this.config = {\n      dataIdFromObject: defaultDataIdFromObject,\n      ...config,\n    };\n\n    this.cache = this.config.cache;\n\n    this.setRootTypename(\"Query\");\n    this.setRootTypename(\"Mutation\");\n    this.setRootTypename(\"Subscription\");\n\n    if (config.possibleTypes) {\n      this.addPossibleTypes(config.possibleTypes);\n    }\n\n    if (config.typePolicies) {\n      this.addTypePolicies(config.typePolicies);\n    }\n  }\n\n  public identify(\n    object: StoreObject,\n    partialContext?: Partial<KeyFieldsContext>\n  ): [string?, StoreObject?] {\n    const policies = this;\n\n    const typename =\n      (partialContext &&\n        (partialContext.typename || partialContext.storeObject?.__typename)) ||\n      object.__typename;\n\n    // It should be possible to write root Query fields with writeFragment,\n    // using { __typename: \"Query\", ... } as the data, but it does not make\n    // sense to allow the same identification behavior for the Mutation and\n    // Subscription types, since application code should never be writing\n    // directly to (or reading directly from) those root objects.\n    if (typename === this.rootTypenamesById.ROOT_QUERY) {\n      return [\"ROOT_QUERY\"];\n    }\n\n    // Default context.storeObject to object if not otherwise provided.\n    const storeObject =\n      (partialContext && partialContext.storeObject) || object;\n\n    const context: KeyFieldsContext = {\n      ...partialContext,\n      typename,\n      storeObject,\n      readField:\n        (partialContext && partialContext.readField) ||\n        function () {\n          const options = normalizeReadFieldOptions(arguments, storeObject);\n          return policies.readField(options, {\n            store: policies.cache[\"data\"],\n            variables: options.variables,\n          });\n        },\n    };\n\n    let id: KeyFieldsResult;\n\n    const policy = typename && this.getTypePolicy(typename);\n    let keyFn = (policy && policy.keyFn) || this.config.dataIdFromObject;\n\n    disableWarningsSlot.withValue(true, () => {\n      while (keyFn) {\n        const specifierOrId = keyFn({ ...object, ...storeObject }, context);\n        if (isArray(specifierOrId)) {\n          keyFn = keyFieldsFnFromSpecifier(specifierOrId);\n        } else {\n          id = specifierOrId;\n          break;\n        }\n      }\n    });\n\n    id = id ? String(id) : void 0;\n    return context.keyObject ? [id, context.keyObject] : [id];\n  }\n\n  public addTypePolicies(typePolicies: TypePolicies) {\n    Object.keys(typePolicies).forEach((typename) => {\n      const { queryType, mutationType, subscriptionType, ...incoming } =\n        typePolicies[typename];\n\n      // Though {query,mutation,subscription}Type configurations are rare,\n      // it's important to call setRootTypename as early as possible,\n      // since these configurations should apply consistently for the\n      // entire lifetime of the cache. Also, since only one __typename can\n      // qualify as one of these root types, these three properties cannot\n      // be inherited, unlike the rest of the incoming properties. That\n      // restriction is convenient, because the purpose of this.toBeAdded\n      // is to delay the processing of type/field policies until the first\n      // time they're used, allowing policies to be added in any order as\n      // long as all relevant policies (including policies for supertypes)\n      // have been added by the time a given policy is used for the first\n      // time. In other words, since inheritance doesn't matter for these\n      // properties, there's also no need to delay their processing using\n      // the this.toBeAdded queue.\n      if (queryType) this.setRootTypename(\"Query\", typename);\n      if (mutationType) this.setRootTypename(\"Mutation\", typename);\n      if (subscriptionType) this.setRootTypename(\"Subscription\", typename);\n\n      if (hasOwn.call(this.toBeAdded, typename)) {\n        this.toBeAdded[typename].push(incoming);\n      } else {\n        this.toBeAdded[typename] = [incoming];\n      }\n    });\n  }\n\n  private updateTypePolicy(typename: string, incoming: TypePolicy) {\n    const existing = this.getTypePolicy(typename);\n    const { keyFields, fields } = incoming;\n\n    function setMerge(\n      existing: { merge?: FieldMergeFunction | boolean },\n      merge?: FieldMergeFunction | boolean\n    ) {\n      existing.merge =\n        typeof merge === \"function\" ? merge\n          // Pass merge:true as a shorthand for a merge implementation\n          // that returns options.mergeObjects(existing, incoming).\n        : merge === true ? mergeTrueFn\n          // Pass merge:false to make incoming always replace existing\n          // without any warnings about data clobbering.\n        : merge === false ? mergeFalseFn\n        : existing.merge;\n    }\n\n    // Type policies can define merge functions, as an alternative to\n    // using field policies to merge child objects.\n    setMerge(existing, incoming.merge);\n\n    existing.keyFn =\n      // Pass false to disable normalization for this typename.\n      keyFields === false ? nullKeyFieldsFn\n        // Pass an array of strings to use those fields to compute a\n        // composite ID for objects of this typename.\n      : isArray(keyFields) ? keyFieldsFnFromSpecifier(keyFields)\n        // Pass a function to take full control over identification.\n      : typeof keyFields === \"function\" ? keyFields\n        // Leave existing.keyFn unchanged if above cases fail.\n      : existing.keyFn;\n\n    if (fields) {\n      Object.keys(fields).forEach((fieldName) => {\n        const existing = this.getFieldPolicy(typename, fieldName, true)!;\n        const incoming = fields[fieldName];\n\n        if (typeof incoming === \"function\") {\n          existing.read = incoming;\n        } else {\n          const { keyArgs, read, merge } = incoming;\n\n          existing.keyFn =\n            // Pass false to disable argument-based differentiation of\n            // field identities.\n            keyArgs === false ? simpleKeyArgsFn\n              // Pass an array of strings to use named arguments to\n              // compute a composite identity for the field.\n            : isArray(keyArgs) ? keyArgsFnFromSpecifier(keyArgs)\n              // Pass a function to take full control over field identity.\n            : typeof keyArgs === \"function\" ? keyArgs\n              // Leave existing.keyFn unchanged if above cases fail.\n            : existing.keyFn;\n\n          if (typeof read === \"function\") {\n            existing.read = read;\n          }\n\n          setMerge(existing, merge);\n        }\n\n        if (existing.read && existing.merge) {\n          // If we have both a read and a merge function, assume\n          // keyArgs:false, because read and merge together can take\n          // responsibility for interpreting arguments in and out. This\n          // default assumption can always be overridden by specifying\n          // keyArgs explicitly in the FieldPolicy.\n          existing.keyFn = existing.keyFn || simpleKeyArgsFn;\n        }\n      });\n    }\n  }\n\n  private setRootTypename(\n    which: \"Query\" | \"Mutation\" | \"Subscription\",\n    typename: string = which\n  ) {\n    const rootId = \"ROOT_\" + which.toUpperCase();\n    const old = this.rootTypenamesById[rootId];\n    if (typename !== old) {\n      invariant(\n        !old || old === which,\n        `Cannot change root %s __typename more than once`,\n        which\n      );\n      // First, delete any old __typename associated with this rootId from\n      // rootIdsByTypename.\n      if (old) delete this.rootIdsByTypename[old];\n      // Now make this the only __typename that maps to this rootId.\n      this.rootIdsByTypename[typename] = rootId;\n      // Finally, update the __typename associated with this rootId.\n      this.rootTypenamesById[rootId] = typename;\n    }\n  }\n\n  public addPossibleTypes(possibleTypes: PossibleTypesMap) {\n    (this.usingPossibleTypes as boolean) = true;\n    Object.keys(possibleTypes).forEach((supertype) => {\n      // Make sure all types have an entry in this.supertypeMap, even if\n      // their supertype set is empty, so we can return false immediately\n      // from policies.fragmentMatches for unknown supertypes.\n      this.getSupertypeSet(supertype, true);\n\n      possibleTypes[supertype].forEach((subtype) => {\n        this.getSupertypeSet(subtype, true)!.add(supertype);\n        const match = subtype.match(TypeOrFieldNameRegExp);\n        if (!match || match[0] !== subtype) {\n          // TODO Don't interpret just any invalid typename as a RegExp.\n          this.fuzzySubtypes.set(subtype, new RegExp(subtype));\n        }\n      });\n    });\n  }\n\n  private getTypePolicy(typename: string): Policies[\"typePolicies\"][string] {\n    if (!hasOwn.call(this.typePolicies, typename)) {\n      const policy: Policies[\"typePolicies\"][string] = (this.typePolicies[\n        typename\n      ] = Object.create(null));\n      policy.fields = Object.create(null);\n\n      // When the TypePolicy for typename is first accessed, instead of\n      // starting with an empty policy object, inherit any properties or\n      // fields from the type policies of the supertypes of typename.\n      //\n      // Any properties or fields defined explicitly within the TypePolicy\n      // for typename will take precedence, and if there are multiple\n      // supertypes, the properties of policies whose types were added\n      // later via addPossibleTypes will take precedence over those of\n      // earlier supertypes. TODO Perhaps we should warn about these\n      // conflicts in development, and recommend defining the property\n      // explicitly in the subtype policy?\n      //\n      // Field policy inheritance is atomic/shallow: you can't inherit a\n      // field policy and then override just its read function, since read\n      // and merge functions often need to cooperate, so changing only one\n      // of them would be a recipe for inconsistency.\n      //\n      // Once the TypePolicy for typename has been accessed, its properties can\n      // still be updated directly using addTypePolicies, but future changes to\n      // inherited supertype policies will not be reflected in this subtype\n      // policy, because this code runs at most once per typename.\n      let supertypes = this.supertypeMap.get(typename);\n      if (!supertypes && this.fuzzySubtypes.size) {\n        // To make the inheritance logic work for unknown typename strings that\n        // may have fuzzy supertypes, we give this typename an empty supertype\n        // set and then populate it with any fuzzy supertypes that match.\n        supertypes = this.getSupertypeSet(typename, true)!;\n        // This only works for typenames that are directly matched by a fuzzy\n        // supertype. What if there is an intermediate chain of supertypes?\n        // While possible, that situation can only be solved effectively by\n        // specifying the intermediate relationships via possibleTypes, manually\n        // and in a non-fuzzy way.\n        this.fuzzySubtypes.forEach((regExp, fuzzy) => {\n          if (regExp.test(typename)) {\n            // The fuzzy parameter is just the original string version of regExp\n            // (not a valid __typename string), but we can look up the\n            // associated supertype(s) in this.supertypeMap.\n            const fuzzySupertypes = this.supertypeMap.get(fuzzy);\n            if (fuzzySupertypes) {\n              fuzzySupertypes.forEach((supertype) =>\n                supertypes!.add(supertype)\n              );\n            }\n          }\n        });\n      }\n      if (supertypes && supertypes.size) {\n        supertypes.forEach((supertype) => {\n          const { fields, ...rest } = this.getTypePolicy(supertype);\n          Object.assign(policy, rest);\n          Object.assign(policy.fields, fields);\n        });\n      }\n    }\n\n    const inbox = this.toBeAdded[typename];\n    if (inbox && inbox.length) {\n      // Merge the pending policies into this.typePolicies, in the order they\n      // were originally passed to addTypePolicy.\n      inbox.splice(0).forEach((policy) => {\n        this.updateTypePolicy(typename, policy);\n      });\n    }\n\n    return this.typePolicies[typename];\n  }\n\n  private getFieldPolicy(\n    typename: string | undefined,\n    fieldName: string,\n    createIfMissing: boolean\n  ):\n    | {\n        keyFn?: KeyArgsFunction;\n        read?: FieldReadFunction<any>;\n        merge?: FieldMergeFunction<any>;\n      }\n    | undefined {\n    if (typename) {\n      const fieldPolicies = this.getTypePolicy(typename).fields;\n      return (\n        fieldPolicies[fieldName] ||\n        (createIfMissing && (fieldPolicies[fieldName] = Object.create(null)))\n      );\n    }\n  }\n\n  private getSupertypeSet(\n    subtype: string,\n    createIfMissing: boolean\n  ): Set<string> | undefined {\n    let supertypeSet = this.supertypeMap.get(subtype);\n    if (!supertypeSet && createIfMissing) {\n      this.supertypeMap.set(subtype, (supertypeSet = new Set<string>()));\n    }\n    return supertypeSet;\n  }\n\n  public fragmentMatches(\n    fragment: InlineFragmentNode | FragmentDefinitionNode,\n    typename: string | undefined,\n    result?: Record<string, any>,\n    variables?: Record<string, any>\n  ): boolean {\n    if (!fragment.typeCondition) return true;\n\n    // If the fragment has a type condition but the object we're matching\n    // against does not have a __typename, the fragment cannot match.\n    if (!typename) return false;\n\n    const supertype = fragment.typeCondition.name.value;\n    // Common case: fragment type condition and __typename are the same.\n    if (typename === supertype) return true;\n\n    if (this.usingPossibleTypes && this.supertypeMap.has(supertype)) {\n      const typenameSupertypeSet = this.getSupertypeSet(typename, true)!;\n      const workQueue = [typenameSupertypeSet];\n      const maybeEnqueue = (subtype: string) => {\n        const supertypeSet = this.getSupertypeSet(subtype, false);\n        if (\n          supertypeSet &&\n          supertypeSet.size &&\n          workQueue.indexOf(supertypeSet) < 0\n        ) {\n          workQueue.push(supertypeSet);\n        }\n      };\n\n      // We need to check fuzzy subtypes only if we encountered fuzzy\n      // subtype strings in addPossibleTypes, and only while writing to\n      // the cache, since that's when selectionSetMatchesResult gives a\n      // strong signal of fragment matching. The StoreReader class calls\n      // policies.fragmentMatches without passing a result object, so\n      // needToCheckFuzzySubtypes is always false while reading.\n      let needToCheckFuzzySubtypes = !!(result && this.fuzzySubtypes.size);\n      let checkingFuzzySubtypes = false;\n\n      // It's important to keep evaluating workQueue.length each time through\n      // the loop, because the queue can grow while we're iterating over it.\n      for (let i = 0; i < workQueue.length; ++i) {\n        const supertypeSet = workQueue[i];\n\n        if (supertypeSet.has(supertype)) {\n          if (!typenameSupertypeSet.has(supertype)) {\n            if (checkingFuzzySubtypes) {\n              invariant.warn(\n                `Inferring subtype %s of supertype %s`,\n                typename,\n                supertype\n              );\n            }\n            // Record positive results for faster future lookup.\n            // Unfortunately, we cannot safely cache negative results,\n            // because new possibleTypes data could always be added to the\n            // Policies class.\n            typenameSupertypeSet.add(supertype);\n          }\n          return true;\n        }\n\n        supertypeSet.forEach(maybeEnqueue);\n\n        if (\n          needToCheckFuzzySubtypes &&\n          // Start checking fuzzy subtypes only after exhausting all\n          // non-fuzzy subtypes (after the final iteration of the loop).\n          i === workQueue.length - 1 &&\n          // We could wait to compare fragment.selectionSet to result\n          // after we verify the supertype, but this check is often less\n          // expensive than that search, and we will have to do the\n          // comparison anyway whenever we find a potential match.\n          selectionSetMatchesResult(fragment.selectionSet, result!, variables)\n        ) {\n          // We don't always need to check fuzzy subtypes (if no result\n          // was provided, or !this.fuzzySubtypes.size), but, when we do,\n          // we only want to check them once.\n          needToCheckFuzzySubtypes = false;\n          checkingFuzzySubtypes = true;\n\n          // If we find any fuzzy subtypes that match typename, extend the\n          // workQueue to search through the supertypes of those fuzzy\n          // subtypes. Otherwise the for-loop will terminate and we'll\n          // return false below.\n          this.fuzzySubtypes.forEach((regExp, fuzzyString) => {\n            const match = typename.match(regExp);\n            if (match && match[0] === typename) {\n              maybeEnqueue(fuzzyString);\n            }\n          });\n        }\n      }\n    }\n\n    return false;\n  }\n\n  public hasKeyArgs(typename: string | undefined, fieldName: string) {\n    const policy = this.getFieldPolicy(typename, fieldName, false);\n    return !!(policy && policy.keyFn);\n  }\n\n  public getStoreFieldName(fieldSpec: FieldSpecifier): string {\n    const { typename, fieldName } = fieldSpec;\n    const policy = this.getFieldPolicy(typename, fieldName, false);\n    let storeFieldName: Exclude<ReturnType<KeyArgsFunction>, KeySpecifier>;\n\n    let keyFn = policy && policy.keyFn;\n    if (keyFn && typename) {\n      const context: Parameters<KeyArgsFunction>[1] = {\n        typename,\n        fieldName,\n        field: fieldSpec.field || null,\n        variables: fieldSpec.variables,\n      };\n      const args = argsFromFieldSpecifier(fieldSpec);\n      while (keyFn) {\n        const specifierOrString = keyFn(args, context);\n        if (isArray(specifierOrString)) {\n          keyFn = keyArgsFnFromSpecifier(specifierOrString);\n        } else {\n          // If the custom keyFn returns a falsy value, fall back to\n          // fieldName instead.\n          storeFieldName = specifierOrString || fieldName;\n          break;\n        }\n      }\n    }\n\n    if (storeFieldName === void 0) {\n      storeFieldName =\n        fieldSpec.field ?\n          storeKeyNameFromField(fieldSpec.field, fieldSpec.variables)\n        : getStoreKeyName(fieldName, argsFromFieldSpecifier(fieldSpec));\n    }\n\n    // Returning false from a keyArgs function is like configuring\n    // keyArgs: false, but more dynamic.\n    if (storeFieldName === false) {\n      return fieldName;\n    }\n\n    // Make sure custom field names start with the actual field.name.value\n    // of the field, so we can always figure out which properties of a\n    // StoreObject correspond to which original field names.\n    return fieldName === fieldNameFromStoreName(storeFieldName) ? storeFieldName\n      : fieldName + \":\" + storeFieldName;\n  }\n\n  public readField<V = StoreValue>(\n    options: ReadFieldOptions,\n    context: ReadMergeModifyContext\n  ): SafeReadonly<V> | undefined {\n    const objectOrReference = options.from;\n    if (!objectOrReference) return;\n\n    const nameOrField = options.field || options.fieldName;\n    if (!nameOrField) return;\n\n    if (options.typename === void 0) {\n      const typename = context.store.getFieldValue<string>(\n        objectOrReference,\n        \"__typename\"\n      );\n      if (typename) options.typename = typename;\n    }\n\n    const storeFieldName = this.getStoreFieldName(options);\n    const fieldName = fieldNameFromStoreName(storeFieldName);\n    const existing = context.store.getFieldValue<V>(\n      objectOrReference,\n      storeFieldName\n    );\n    const policy = this.getFieldPolicy(options.typename, fieldName, false);\n    const read = policy && policy.read;\n\n    if (read) {\n      const readOptions = makeFieldFunctionOptions(\n        this,\n        objectOrReference,\n        options,\n        context,\n        context.store.getStorage(\n          isReference(objectOrReference) ?\n            objectOrReference.__ref\n          : objectOrReference,\n          storeFieldName\n        )\n      );\n\n      // Call read(existing, readOptions) with cacheSlot holding this.cache.\n      return cacheSlot.withValue(this.cache, read, [\n        existing,\n        readOptions,\n      ]) as SafeReadonly<V>;\n    }\n\n    return existing;\n  }\n\n  public getReadFunction(\n    typename: string | undefined,\n    fieldName: string\n  ): FieldReadFunction | undefined {\n    const policy = this.getFieldPolicy(typename, fieldName, false);\n    return policy && policy.read;\n  }\n\n  public getMergeFunction(\n    parentTypename: string | undefined,\n    fieldName: string,\n    childTypename: string | undefined\n  ): FieldMergeFunction | undefined {\n    let policy:\n      | Policies[\"typePolicies\"][string]\n      | Policies[\"typePolicies\"][string][\"fields\"][string]\n      | undefined = this.getFieldPolicy(parentTypename, fieldName, false);\n    let merge = policy && policy.merge;\n    if (!merge && childTypename) {\n      policy = this.getTypePolicy(childTypename);\n      merge = policy && policy.merge;\n    }\n    return merge;\n  }\n\n  public runMergeFunction(\n    existing: StoreValue,\n    incoming: StoreValue,\n    { field, typename, merge }: MergeInfo,\n    context: WriteContext,\n    storage?: StorageType\n  ) {\n    if (merge === mergeTrueFn) {\n      // Instead of going to the trouble of creating a full\n      // FieldFunctionOptions object and calling mergeTrueFn, we can\n      // simply call mergeObjects, as mergeTrueFn would.\n      return makeMergeObjectsFunction(context.store)(\n        existing as StoreObject,\n        incoming as StoreObject\n      );\n    }\n\n    if (merge === mergeFalseFn) {\n      // Likewise for mergeFalseFn, whose implementation is even simpler.\n      return incoming;\n    }\n\n    // If cache.writeQuery or cache.writeFragment was called with\n    // options.overwrite set to true, we still call merge functions, but\n    // the existing data is always undefined, so the merge function will\n    // not attempt to combine the incoming data with the existing data.\n    if (context.overwrite) {\n      existing = void 0;\n    }\n\n    return merge(\n      existing,\n      incoming,\n      makeFieldFunctionOptions(\n        this,\n        // Unlike options.readField for read functions, we do not fall\n        // back to the current object if no foreignObjOrRef is provided,\n        // because it's not clear what the current object should be for\n        // merge functions: the (possibly undefined) existing object, or\n        // the incoming object? If you think your merge function needs\n        // to read sibling fields in order to produce a new value for\n        // the current field, you might want to rethink your strategy,\n        // because that's a recipe for making merge behavior sensitive\n        // to the order in which fields are written into the cache.\n        // However, readField(name, ref) is useful for merge functions\n        // that need to deduplicate child objects and references.\n        void 0,\n        {\n          typename,\n          fieldName: field.name.value,\n          field,\n          variables: context.variables,\n        },\n        context,\n        storage || Object.create(null)\n      )\n    );\n  }\n}\n\nfunction makeFieldFunctionOptions(\n  policies: Policies,\n  objectOrReference: StoreObject | Reference | undefined,\n  fieldSpec: FieldSpecifier,\n  context: ReadMergeModifyContext,\n  storage: StorageType\n): FieldFunctionOptions {\n  const storeFieldName = policies.getStoreFieldName(fieldSpec);\n  const fieldName = fieldNameFromStoreName(storeFieldName);\n  const variables = fieldSpec.variables || context.variables;\n  const { toReference, canRead } = context.store;\n\n  return {\n    args: argsFromFieldSpecifier(fieldSpec),\n    field: fieldSpec.field || null,\n    fieldName,\n    storeFieldName,\n    variables,\n    isReference,\n    toReference,\n    storage,\n    cache: policies.cache,\n    canRead,\n    readField<T>() {\n      return policies.readField<T>(\n        normalizeReadFieldOptions(arguments, objectOrReference, variables),\n        context\n      );\n    },\n    mergeObjects: makeMergeObjectsFunction(context.store),\n  };\n}\n\nexport function normalizeReadFieldOptions(\n  readFieldArgs: IArguments,\n  objectOrReference: StoreObject | Reference | undefined,\n  variables?: ReadMergeModifyContext[\"variables\"]\n): ReadFieldOptions {\n  const { 0: fieldNameOrOptions, 1: from, length: argc } = readFieldArgs;\n\n  let options: ReadFieldOptions;\n\n  if (typeof fieldNameOrOptions === \"string\") {\n    options = {\n      fieldName: fieldNameOrOptions,\n      // Default to objectOrReference only when no second argument was\n      // passed for the from parameter, not when undefined is explicitly\n      // passed as the second argument.\n      from: argc > 1 ? from : objectOrReference,\n    };\n  } else {\n    options = { ...fieldNameOrOptions };\n    // Default to objectOrReference only when fieldNameOrOptions.from is\n    // actually omitted, rather than just undefined.\n    if (!hasOwn.call(options, \"from\")) {\n      options.from = objectOrReference;\n    }\n  }\n\n  if (__DEV__ && options.from === void 0) {\n    invariant.warn(\n      `Undefined 'from' passed to readField with arguments %s`,\n      stringifyForDisplay(Array.from(readFieldArgs))\n    );\n  }\n\n  if (void 0 === options.variables) {\n    options.variables = variables;\n  }\n\n  return options;\n}\n\nfunction makeMergeObjectsFunction(\n  store: NormalizedCache\n): MergeObjectsFunction {\n  return function mergeObjects(existing, incoming) {\n    if (isArray(existing) || isArray(incoming)) {\n      throw newInvariantError(\"Cannot automatically merge arrays\");\n    }\n\n    // These dynamic checks are necessary because the parameters of a\n    // custom merge function can easily have the any type, so the type\n    // system cannot always enforce the StoreObject | Reference parameter\n    // types of options.mergeObjects.\n    if (isNonNullObject(existing) && isNonNullObject(incoming)) {\n      const eType = store.getFieldValue(existing, \"__typename\");\n      const iType = store.getFieldValue(incoming, \"__typename\");\n      const typesDiffer = eType && iType && eType !== iType;\n\n      if (typesDiffer) {\n        return incoming;\n      }\n\n      if (isReference(existing) && storeValueIsStoreObject(incoming)) {\n        // Update the normalized EntityStore for the entity identified by\n        // existing.__ref, preferring/overwriting any fields contributed by the\n        // newer incoming StoreObject.\n        store.merge(existing.__ref, incoming);\n        return existing;\n      }\n\n      if (storeValueIsStoreObject(existing) && isReference(incoming)) {\n        // Update the normalized EntityStore for the entity identified by\n        // incoming.__ref, taking fields from the older existing object only if\n        // those fields are not already present in the newer StoreObject\n        // identified by incoming.__ref.\n        store.merge(existing, incoming.__ref);\n        return incoming;\n      }\n\n      if (\n        storeValueIsStoreObject(existing) &&\n        storeValueIsStoreObject(incoming)\n      ) {\n        return { ...existing, ...incoming };\n      }\n    }\n\n    return incoming;\n  };\n}\n"]}