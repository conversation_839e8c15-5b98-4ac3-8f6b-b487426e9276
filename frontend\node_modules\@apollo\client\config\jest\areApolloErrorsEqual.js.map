{"version": 3, "file": "areApolloErrorsEqual.js", "sourceRoot": "", "sources": ["../../../src/config/jest/areApolloErrorsEqual.ts"], "names": [], "mappings": "AAEA,SAAS,aAAa,CAAC,CAAM;IAC3B,OAAO,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,IAAI,IAAI,aAAa,CAAC;AACvD,CAAC;AAED,MAAM,CAAC,IAAM,oBAAoB,GAAW,UAAU,CAAC,EAAE,CAAC,EAAE,aAAa;IACvE,IAAM,cAAc,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IACxC,IAAM,cAAc,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;IAExC,IAAI,cAAc,IAAI,cAAc,EAAE,CAAC;QACrC,OAAO,CACL,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO;YACvB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC,CAAC,aAAa,EAAE,aAAa,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,cAAc,EAAE,aAAa,CAAC;YAC9D,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,YAAY,EAAE,aAAa,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC,YAAY,EAAE,aAAa,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,aAAa,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,EAAE,aAAa,CAAC,CACrD,CAAC;IACJ,CAAC;SAAM,IAAI,cAAc,KAAK,cAAc,EAAE,CAAC;QAC7C,OAAO,SAAS,CAAC;IACnB,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC", "sourcesContent": ["import type { ApolloError } from \"../../errors/index.js\";\nimport type { Tester } from \"@jest/expect-utils\";\nfunction isApolloError(e: any): e is ApolloError {\n  return e instanceof Error && e.name == \"ApolloError\";\n}\n\nexport const areApolloErrorsEqual: Tester = function (a, b, customTesters) {\n  const isAApolloError = isApolloError(a);\n  const isBApolloError = isApolloError(b);\n\n  if (isAApolloError && isBApolloError) {\n    return (\n      a.message === b.message &&\n      this.equals(a.graphQLErrors, b.graphQLErrors, customTesters) &&\n      this.equals(a.protocolErrors, b.protocolErrors, customTesters) &&\n      this.equals(a.clientErrors, b.clientErrors, customTesters) &&\n      this.equals(a.networkError, b.networkError, customTesters) &&\n      this.equals(a.cause, b.cause, customTesters) &&\n      this.equals(a.extraInfo, b.extraInfo, customTesters)\n    );\n  } else if (isAApolloError === isBApolloError) {\n    return undefined;\n  } else {\n    return false;\n  }\n};\n"]}