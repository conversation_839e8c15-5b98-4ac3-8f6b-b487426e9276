{"version": 3, "file": "areGraphQlErrorsEqual.js", "sourceRoot": "", "sources": ["../../../src/config/jest/areGraphQlErrorsEqual.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAGvC,MAAM,CAAC,IAAM,qBAAqB,GAAW,UAAU,CAAC,EAAE,CAAC,EAAE,aAAa;IACxE,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,YAAY,YAAY,EAAE,CAAC;QAC3D,OAAO,IAAI,CAAC,MAAM,CAChB,CAAC,YAAY,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAC1C,CAAC,YAAY,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAC1C,aAAa,CACd,CAAC;IACJ,CAAC;AACH,CAAC,CAAC", "sourcesContent": ["import { GraphQLError } from \"graphql\";\nimport type { Tester } from \"@jest/expect-utils\";\n\nexport const areGraphQLErrorsEqual: Tester = function (a, b, customTesters) {\n  if (a instanceof GraphQLError || b instanceof GraphQLError) {\n    return this.equals(\n      a instanceof GraphQLError ? a.toJSON() : a,\n      b instanceof GraphQLError ? b.toJSON() : b,\n      customTesters\n    );\n  }\n};\n"]}