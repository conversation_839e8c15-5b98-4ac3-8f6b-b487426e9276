{"version": 3, "file": "getEmbeddedHTML.js", "sourceRoot": "", "sources": ["../../../../../src/plugin/landingPage/default/getEmbeddedHTML.ts"], "names": [], "mappings": "AAcA,SAAS,sBAAsB,CAAC,MAAc;IAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;SAC1B,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC;SACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC;SACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC;SACvB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AAC7B,CAAC;AAED,MAAM,CAAC,MAAM,uBAAuB,GAAG,CACrC,kBAA0B,EAC1B,MAAqE,EACrE,mBAA2B,EAC3B,KAAa,EACb,EAAE;IA2BF,MAAM,yCAAyC,GAAG;QAChD,cAAc,EAAE,EAAE;QAClB,oBAAoB,EAAE,KAAK;QAC3B,YAAY,EAAE,IAAI;QAClB,GAAG,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;KAC3D,CAAC;IACF,MAAM,sBAAsB,GAGF;QACxB,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,MAAM,EAAE,qBAAqB;QAC7B,YAAY,EAAE;YACZ,GAAG,CAAC,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,WAAW,IAAI,MAAM;gBACtE,CAAC,CAAC;oBACE,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,SAAS,EAAE,MAAM,CAAC,SAAS;iBAC5B;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,GAAG,CAAC,cAAc,IAAI,MAAM;gBAC1B,CAAC,CAAC;oBACE,YAAY,EAAE,MAAM,CAAC,YAAY;oBACjC,WAAW,EAAE,MAAM,CAAC,WAAW;iBAChC;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,cAAc,EAAE;gBACd,GAAG,yCAAyC,CAAC,cAAc;aAC5D;SACF;QACD,oBAAoB,EAClB,yCAAyC,CAAC,oBAAoB;QAChE,cAAc,EAAE,MAAM,CAAC,cAAc;QACrC,OAAO,EAAE,mBAAmB;QAC5B,YAAY,EAAE,yCAAyC,CAAC,YAAY;QACpE,kBAAkB,EAAE,KAAK;KAC1B,CAAC;IAEF,OAAO;;;;;eAKM,KAAK;;;;;;;;;;;;;;;iBAeH,KAAK,4DAA4D,kBAAkB,CAChG,kBAAkB,CACnB,sDAAsD,kBAAkB,CACvE,mBAAmB,CACpB;iBACc,KAAK;;iCAEW,sBAAsB,CACnD,sBAAsB,CACvB;;;;;;CAMF,CAAC;AACF,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAG,CACpC,iBAAyB,EACzB,MAAgE,EAChE,mBAA2B,EAC3B,KAAa,EACb,EAAE;IACF,MAAM,oCAAoC,GAAG;QAC3C,YAAY,EAAE,IAAI;QAClB,kBAAkB,EAAE,KAAK;QACzB,YAAY,EAAE,EAAE;QAChB,GAAG,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;KACnE,CAAC;IACF,MAAM,qBAAqB,GAAG;QAC5B,MAAM,EAAE,oBAAoB;QAC5B,YAAY,EAAE;YACZ,GAAG,CAAC,UAAU,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,IAAI,WAAW,IAAI,MAAM;gBACtE,CAAC,CAAC;oBACE,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,OAAO,EAAE,MAAM,CAAC,OAAO;iBACxB;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,GAAG,CAAC,cAAc,IAAI,MAAM;gBAC1B,CAAC,CAAC;oBACE,YAAY,EAAE,MAAM,CAAC,YAAY;oBACjC,WAAW,EAAE,MAAM,CAAC,WAAW;iBAChC;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,GAAG,oCAAoC,CAAC,YAAY;SACrD;QACD,gBAAgB,EAAE,KAAK;QACvB,kBAAkB,EAAE,oCAAoC,CAAC,kBAAkB;QAC3E,OAAO,EAAE,mBAAmB;QAC5B,YAAY,EAAE,oCAAoC,CAAC,YAAY;QAC/D,kBAAkB,EAAE,KAAK;KAC1B,CAAC;IACF,OAAO;;;;;eAKM,KAAK;;;;;;;;;;;;;;;iBAeH,KAAK,2DAA2D,kBAAkB,CAC/F,iBAAiB,CAClB,qDAAqD,kBAAkB,CACtE,mBAAmB,CACpB;iBACc,KAAK;;gCAEU,sBAAsB,CAAC,qBAAqB,CAAC;;;;;;;;CAQ5E,CAAC;AACF,CAAC,CAAC"}