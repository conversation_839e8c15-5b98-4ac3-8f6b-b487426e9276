{"version": 3, "file": "stats.js", "sourceRoot": "", "sources": ["../../../../src/plugin/usageReporting/stats.ts"], "names": [], "mappings": ";;;AACA,+EAY0C;AAE1C,iEAA2D;AAC3D,+DAAgF;AAiBhF,MAAa,aAAa;IAA1B;QACE,UAAK,GAAG,CAAC,CAAC;IACZ,CAAC;CAAA;AAFD,sCAEC;AACD,MAAa,SAAS;IAOpB,YAAqB,MAAoB;QAApB,WAAM,GAAN,MAAM,CAAc;QAFzC,wBAAmB,GAAG,KAAK,CAAC;QAGnB,mBAAc,GACrB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACtB,YAAO,GAAsC,IAAI,CAAC;QAClD,mBAAc,GAAG,CAAC,CAAC;QAUV,kBAAa,GAAG,IAAI,aAAa,EAAE,CAAC;IAdD,CAAC;IAgB7C,uBAAuB;QACrB,KAAK,MAAM,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;YAChE,cAAc,CAAC,uBAAuB,EAAE,CAAC;QAC3C,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,EACP,cAAc,EACd,KAAK,EACL,OAAO,EACP,sBAAsB,EAItB,aAAa,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAChC,iBAAiB,GAQlB;QACC,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC;YAC5C,cAAc;YACd,sBAAsB;SACvB,CAAC,CAAC;QACH,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,YAAY,GAAG,gCAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;YAElD,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,MAAM,GAAG,aAAa,EAAE,CAAC;gBACjE,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CACtC,KAAK,EACL,IAAI,CAAC,aAAa,EAClB,iBAAiB,CAClB,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACxC,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC;YACtD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CACtC,KAAK,EACL,IAAI,CAAC,aAAa,EAClB,iBAAiB,CAClB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,EACxB,cAAc,EACd,sBAAsB,GAIvB;QACC,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;QACrD,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,uBAAuB,CAAC,cAAc,CAAC,CAAC;QAGpE,KAAK,MAAM,CAAC,QAAQ,EAAE,uBAAuB,CAAC,IAAI,MAAM,CAAC,OAAO,CAC9D,sBAAsB,CACvB,EAAE,CAAC;YAGF,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,IAAI,uBAAuB,CAAC,WAAW,EAAE,CAAC;gBACxC,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAC9D,KAAK,MAAM,SAAS,IAAI,uBAAuB,CAAC,UAAU,EAAE,CAAC;gBAC3D,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAMD,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,GAAG,IAAI,iBAAiB,CACjE,sBAAsB,CACvB,CAAC,CAAC;IACL,CAAC;CACF;AA9GD,8BA8GC;AAED,MAAM,iBAAiB;IACrB,YAAqB,sBAA8C;QAA9C,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC1D,UAAK,GAAiB,EAAE,CAAC;QACzB,qBAAgB,GAAG,IAAI,cAAc,EAAE,CAAC;QACxC,sCAAiC,GAAiB,EAAE,CAAC;IAHQ,CAAC;IAKvE,uBAAuB;QACrB,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,CAAC;IAClD,CAAC;CACF;AAED,MAAM,cAAc;IAApB;QACW,QAAG,GAA4C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAqD9E,CAAC;IA/CC,OAAO;QACL,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACjC,CAAC;IAED,uBAAuB;QACrB,KAAK,MAAM,mBAAmB,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1D,mBAAmB,CAAC,uBAAuB,EAAE,CAAC;QAChD,CAAC;IACH,CAAC;IAED,QAAQ,CACN,KAAY,EACZ,aAA4B,EAC5B,iBAAqC;QAErC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,QAAQ,CACxD,KAAK,EACL,aAAa,EACb,iBAAiB,CAClB,CAAC;IACJ,CAAC;IAEO,sBAAsB,CAC5B,KAAY,EACZ,aAA4B;QAE5B,MAAM,YAAY,GAAkB;YAClC,UAAU,EAAE,KAAK,CAAC,UAAU;YAC5B,aAAa,EAAE,KAAK,CAAC,aAAa;SACnC,CAAC;QACF,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;QAErD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC3C,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC;QAClB,CAAC;QAID,aAAa,CAAC,KAAK;YACjB,EAAE;gBACF,uBAAuB,CAAC,KAAK,CAAC,UAAU,CAAC;gBACzC,uBAAuB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAC/C,MAAM,mBAAmB,GAAG,IAAI,sBAAsB,CAAC,YAAY,CAAC,CAAC;QACrE,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,mBAAmB,CAAC;QAChD,OAAO,mBAAmB,CAAC;IAC7B,CAAC;CACF;AAED,MAAa,sBAAsB;IAIjC,YAAqB,OAAsB;QAAtB,YAAO,GAAP,OAAO,CAAe;QAH3C,sBAAiB,GAAG,IAAI,oBAAoB,EAAE,CAAC;QAC/C,gBAAW,GAAiC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAElB,CAAC;IAE/C,uBAAuB;QACrB,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YACvD,QAAQ,CAAC,uBAAuB,EAAE,CAAC;QACrC,CAAC;IACH,CAAC;IAMD,QAAQ,CACN,KAAY,EACZ,aAA4B,EAC5B,oBAAwC,EAAE;QAE1C,MAAM,EAAE,oBAAoB,EAAE,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,IAAI,CAAC,iBAAiB,CAAC,mCAAmC,EAAE,CAAC;QAC/D,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,CAAC;QACtC,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,iBAAiB,CACxD,KAAK,CAAC,UAAU,CACjB,CAAC;YACF,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC1E,CAAC;QAMD,IAAI,CAAC,KAAK,CAAC,iBAAiB,IAAI,KAAK,CAAC,WAAW,EAAE,QAAQ,IAAI,IAAI,EAAE,CAAC;YACpE,QAAQ,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;gBAChC,KAAK,gCAAK,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO;oBAClC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,iBAAiB,CAC3D,KAAK,CAAC,WAAW,CAAC,QAAQ,CAC3B,CAAC;oBACF,MAAM;gBACR,KAAK,gCAAK,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM;oBACjC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,iBAAiB,CAC1D,KAAK,CAAC,WAAW,CAAC,QAAQ,CAC3B,CAAC;oBACF,MAAM;YACV,CAAC;QACH,CAAC;QAED,IAAI,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC5B,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,CAAC;QAC9C,CAAC;QACD,IAAI,KAAK,CAAC,sBAAsB,EAAE,CAAC;YACjC,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,CAAC;QAChD,CAAC;QAED,IAAI,KAAK,CAAC,kBAAkB,EAAE,CAAC;YAC7B,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;QACnD,CAAC;QACD,IAAI,KAAK,CAAC,mBAAmB,EAAE,CAAC;YAC9B,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,EAAE,CAAC;QACpD,CAAC;QAED,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,MAAM,cAAc,GAAG,IAAI,GAAG,EAAqB,CAAC;QAEpD,MAAM,cAAc,GAAG,CAAC,IAAiB,EAAE,IAAsB,EAAE,EAAE;YAEnE,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,CAAC;gBACvB,QAAQ,GAAG,IAAI,CAAC;gBAEhB,IAAI,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC/D,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBACjC,kBAAkB,GAAG,kBAAkB,CAAC,QAAQ,CAC9C,OAAO,EACP,aAAa,CACd,CAAC;gBACJ,CAAC,CAAC,CAAC;gBAEH,cAAc,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;gBACvC,kBAAkB,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YACtD,CAAC;YAED,IAAI,oBAAoB,EAAE,CAAC;gBAIzB,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,YAAY,CAAC;gBAa9D,IACE,IAAI,CAAC,UAAU;oBACf,SAAS;oBACT,IAAI,CAAC,IAAI;oBACT,IAAI,CAAC,OAAO,IAAI,IAAI;oBACpB,IAAI,CAAC,SAAS,IAAI,IAAI;oBACtB,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,EAC9B,CAAC;oBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;oBAElE,MAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,CACrC,SAAS,EACT,IAAI,CAAC,IAAI,EACT,aAAa,CACd,CAAC;oBAEF,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC;oBACjD,SAAS,CAAC,sBAAsB,EAAE,CAAC;oBACnC,SAAS,CAAC,uBAAuB,IAAI,oBAAoB,CAAC;oBAM1D,SAAS,CAAC,uBAAuB;wBAC/B,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxC,SAAS,CAAC,YAAY,CAAC,iBAAiB,CACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,EAG7B,oBAAoB,CACrB,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;QAEF,IAAA,sCAAgB,EAAC,KAAK,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;QAG9C,KAAK,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,iBAAiB,EAAE,CAAC;YACnD,QAAQ,GAAG,IAAI,CAAC;YAChB,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,QAAQ,CACrE,WAAW,QAAQ,EAAE,EACrB,aAAa,CACd,CAAC;gBACF,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBACvB,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;wBAChC,kBAAkB,GAAG,kBAAkB,CAAC,QAAQ,CAC9C,OAAO,EACP,aAAa,CACd,CAAC;oBACJ,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,cAAc,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;gBACvC,kBAAkB,CAAC,WAAW,IAAI,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QAED,KAAK,MAAM,SAAS,IAAI,cAAc,EAAE,CAAC;YACvC,SAAS,CAAC,uBAAuB,IAAI,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,EAAE,CAAC;QACnD,CAAC;IACH,CAAC;IAED,WAAW,CAAC,UAAkB,EAAE,aAA4B;QAC1D,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,aAAa,CAAC,KAAK,IAAI,uBAAuB,CAAC,UAAU,CAAC,CAAC;QAC3D,MAAM,QAAQ,GAAG,IAAI,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAC;QACxC,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA5LD,wDA4LC;AAED,MAAM,oBAAoB;IAA1B;QACE,iBAAY,GAAsB,IAAI,wCAAiB,EAAE,CAAC;QAC1D,iBAAY,GAAG,CAAC,CAAC;QACjB,wCAAmC,GAAG,CAAC,CAAC;QACxC,cAAS,GAAG,CAAC,CAAC;QACd,uBAAkB,GAAG,CAAC,CAAC;QACvB,yBAAoB,GAAG,CAAC,CAAC;QACzB,sBAAiB,GAAsB,IAAI,wCAAiB,EAAE,CAAC;QAC/D,mBAAc,GAAsB,IAAI,iBAAiB,EAAE,CAAC;QAC5D,4BAAuB,GAAG,CAAC,CAAC;QAC5B,wBAAmB,GAAsB,IAAI,wCAAiB,EAAE,CAAC;QACjE,yBAAoB,GAAsB,IAAI,wCAAiB,EAAE,CAAC;QAClE,6BAAwB,GAAG,CAAC,CAAC;QAC7B,4BAAuB,GAAG,CAAC,CAAC;IAC9B,CAAC;CAAA;AAED,MAAM,iBAAiB;IAAvB;QACE,aAAQ,GAAuC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACnE,gBAAW,GAAG,CAAC,CAAC;QAChB,4BAAuB,GAAG,CAAC,CAAC;IAa9B,CAAC;IAXC,QAAQ,CAAC,OAAe,EAAE,aAA4B;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,MAAM,KAAK,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACtC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;QAE/B,aAAa,CAAC,KAAK,IAAI,uBAAuB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5D,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAED,MAAM,WAAW;IAAjB;QACE,iBAAY,GAAkC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IA0BpE,CAAC;IAxBC,YAAY,CACV,SAAiB,EACjB,UAAkB,EAClB,aAA4B;QAE5B,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC9C,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,aAAa,CAAC,KAAK;YACjB,uBAAuB,CAAC,SAAS,CAAC;gBAClC,uBAAuB,CAAC,UAAU,CAAC;gBACnC,EAAE,CAAC;QACL,MAAM,SAAS,GAAG,IAAI,YAAY,CAAC,UAAU,CAAC,CAAC;QAC/C,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;QACzC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,uBAAuB;QACrB,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YACzD,SAAS,CAAC,uBAAuB,EAAE,CAAC;QACtC,CAAC;IACH,CAAC;CACF;AAED,MAAM,YAAY;IAUhB,YAAqB,UAAkB;QAAlB,eAAU,GAAV,UAAU,CAAQ;QATvC,gBAAW,GAAG,CAAC,CAAC;QAChB,2BAAsB,GAAG,CAAC,CAAC;QAI3B,4BAAuB,GAAG,CAAC,CAAC;QAC5B,4BAAuB,GAAG,CAAC,CAAC;QAC5B,iBAAY,GAAsB,IAAI,wCAAiB,EAAE,CAAC;IAEhB,CAAC;IAE3C,uBAAuB;QAErB,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IAC1E,CAAC;CACF;AAED,SAAS,uBAAuB,CAAC,CAAS;IAIxC,OAAO,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC,CAAC"}