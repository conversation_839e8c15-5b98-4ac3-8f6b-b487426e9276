{"version": 3, "file": "determineApolloConfig.js", "sourceRoot": "", "sources": ["../../src/determineApolloConfig.ts"], "names": [], "mappings": ";;;AAAA,+DAAsD;AAMtD,SAAgB,qBAAqB,CACnC,KAAoC,EACpC,MAAc;IAEd,MAAM,YAAY,GAAiB,EAAE,CAAC;IAEtC,MAAM,EACJ,UAAU,EACV,gBAAgB,EAChB,eAAe,EACf,oBAAoB,GACrB,GAAG,OAAO,CAAC,GAAG,CAAC;IAGhB,IAAI,KAAK,EAAE,GAAG,EAAE,CAAC;QACf,YAAY,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;IACtC,CAAC;SAAM,IAAI,UAAU,EAAE,CAAC;QACtB,YAAY,CAAC,GAAG,GAAG,UAAU,CAAC,IAAI,EAAE,CAAC;IACvC,CAAC;IACD,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,UAAU,CAAC,KAAK,YAAY,CAAC,GAAG,EAAE,CAAC;QACpD,MAAM,CAAC,IAAI,CACT,sEAAsE;YACpE,mDAAmD,CACtD,CAAC;IACJ,CAAC;IAID,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;QACrB,sBAAsB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;IAC3C,CAAC;IAGD,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;QACrB,YAAY,CAAC,OAAO,GAAG,IAAA,6BAAU,EAAC,QAAQ,CAAC;aACxC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC;aACxB,MAAM,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC;IAGD,IAAI,KAAK,EAAE,QAAQ,EAAE,CAAC;QACpB,YAAY,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IACzC,CAAC;SAAM,IAAI,gBAAgB,EAAE,CAAC;QAC5B,YAAY,CAAC,QAAQ,GAAG,gBAAgB,CAAC;IAC3C,CAAC;IAGD,MAAM,OAAO,GAAG,KAAK,EAAE,OAAO,IAAI,eAAe,CAAC;IAClD,MAAM,YAAY,GAAG,KAAK,EAAE,YAAY,IAAI,oBAAoB,CAAC;IAEjE,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC1B,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CACb,yDAAyD;gBACvD,4EAA4E,CAC/E,CAAC;QACJ,CAAC;QACD,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CACb,8DAA8D;gBAC5D,iFAAiF,CACpF,CAAC;QACJ,CAAC;IACH,CAAC;SAAM,IAAI,OAAO,EAAE,CAAC;QAKnB,YAAY,CAAC,QAAQ,GAAG,YAAY;YAClC,CAAC,CAAC,GAAG,OAAO,IAAI,YAAY,EAAE;YAC9B,CAAC,CAAC,OAAO,CAAC;IACd,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC;AA1ED,sDA0EC;AAED,SAAS,sBAAsB,CAAC,KAAa;IAG3C,MAAM,sBAAsB,GAAG,0BAA0B,CAAC;IAC1D,IAAI,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QACvC,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,sBAAsB,CAAE,CAAC;QAC1D,MAAM,IAAI,KAAK,CACb,6JAA6J,YAAY,CAAC,IAAI,CAC5K,IAAI,CACL,+IAA+I,CACjJ,CAAC;IACJ,CAAC;AACH,CAAC"}