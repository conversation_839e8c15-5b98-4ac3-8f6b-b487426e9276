// backend/src/index.ts
import express from 'express'
import { ApolloServer } from 'apollo-server-express'
import { typeDefs } from './schema/typeDefs'
import { resolvers } from './schema/resolvers'
import { createContext } from './context'
import cors from 'cors'

async function startServer() {
  const app = express()
  
  app.use(cors())
  app.use(express.json())

  const server = new ApolloServer({
    typeDefs,
    resolvers,
    context: createContext,
  })

  await server.start()
  server.applyMiddleware({ app, path: '/graphql' })

  const PORT = process.env.PORT || 4000
  
  app.listen(PORT, () => {
    console.log(`🚀 Server ready at http://localhost:${PORT}${server.graphqlPath}`)
  })
}

startServer().catch(error => {
  console.error('Error starting server:', error)
})
// backend/src/schema/typeDefs.ts
import { gql } from 'apollo-server-express'

export const typeDefs = gql`
  type User {
    id: ID!
    email: String!
    name: String!
    role: UserRole!
    profile: UserProfile
    progress: [CourseProgress!]!
    createdAt: String!
  }

  type UserProfile {
    id: ID!
    avatar: String
    bio: String
    level: String!
    xp: Int!
    achievements: [Achievement!]!
  }

  type Course {
    id: ID!
    title: String!
    description: String!
    level: CourseLevel!
    modules: [Module!]!
    instructor: User!
    students: [User!]!
    createdAt: String!
  }

  type Module {
    id: ID!
    title: String!
    description: String!
    order: Int!
    lessons: [Lesson!]!
    course: Course!
  }

  type Lesson {
    id: ID!
    title: String!
    content: String!
    type: LessonType!
    duration: Int!
    order: Int!
    module: Module!
    quizzes: [Quiz!]!
  }

  type Quiz {
    id: ID!
    title: String!
    questions: [Question!]!
    lesson: Lesson!
    timeLimit: Int
  }

  type Question {
    id: ID!
    text: String!
    type: QuestionType!
    options: [String!]
    correctAnswer: String!
    explanation: String
    quiz: Quiz!
  }

  type CourseProgress {
    id: ID!
    user: User!
    course: Course!
    completedLessons: [String!]!
    currentModule: String
    score: Float
    lastAccessed: String!
  }

  type Achievement {
    id: ID!
    title: String!
    description: String!
    icon: String!
    xpReward: Int!
    unlockedAt: String!
  }

  enum UserRole {
    STUDENT
    INSTRUCTOR
    ADMIN
  }

  enum CourseLevel {
    L1
    L2
    L3
  }

  enum LessonType {
    VIDEO
    TEXT
    INTERACTIVE
    SIMULATION
  }

  enum QuestionType {
    MULTIPLE_CHOICE
    TRUE_FALSE
    CALCULATION
    ESSAY
  }

  type Query {
    me: User
    courses: [Course!]!
    course(id: ID!): Course
    myProgress: [CourseProgress!]!
    leaderboard: [User!]!
  }

  type Mutation {
    register(email: String!, password: String!, name: String!): AuthPayload!
    login(email: String!, password: String!): AuthPayload!
    updateProfile(input: ProfileInput!): User!
    enrollCourse(courseId: ID!): CourseProgress!
    completeLesson(lessonId: ID!): CourseProgress!
    submitQuiz(quizId: ID!, answers: [String!]!): QuizResult!
  }

  type AuthPayload {
    token: String!
    user: User!
  }

  type QuizResult {
    score: Float!
    totalQuestions: Int!
    correctAnswers: Int!
    feedback: [QuestionFeedback!]!
  }

  type QuestionFeedback {
    questionId: ID!
    correct: Boolean!
    explanation: String
  }

  input ProfileInput {
    name: String
    bio: String
    avatar: String
  }
`
