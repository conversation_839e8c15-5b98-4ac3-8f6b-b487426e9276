/*!
 * protobuf.js (c) 2016, daniel wirtz
 * licensed under the bsd-3-clause license
 * see: https://github.com/apollographql/protobuf.js for details
 */
!function(tt){"use strict";var r,e,t,i;r={1:[function(t,i){i.exports=function(t,i){var n=Array(arguments.length-1),s=0,r=2,u=!0;for(;r<arguments.length;)n[s++]=arguments[r++];return new Promise(function(r,e){n[s]=function(t){if(u)if(u=!1,t)e(t);else{for(var i=Array(arguments.length-1),n=0;n<i.length;)i[n++]=arguments[n];r.apply(null,i)}};try{t.apply(i||null,n)}catch(t){u&&(u=!1,e(t))}})}},{}],2:[function(t,i,n){var r=n;r.length=function(t){var i=t.length;if(!i)return 0;for(var n=0;1<--i%4&&"="===t.charAt(i);)++n;return Math.ceil(3*t.length)/4-n};for(var h=Array(64),f=Array(123),e=0;e<64;)f[h[e]=e<26?e+65:e<52?e+71:e<62?e-4:e-59|43]=e++;r.encode=function(t,i,n){for(var r,e=null,s=[],u=0,o=0;i<n;){var f=t[i++];switch(o){case 0:s[u++]=h[f>>2],r=(3&f)<<4,o=1;break;case 1:s[u++]=h[r|f>>4],r=(15&f)<<2,o=2;break;case 2:s[u++]=h[r|f>>6],s[u++]=h[63&f],o=0}8191<u&&((e||(e=[])).push(String.fromCharCode.apply(String,s)),u=0)}return o&&(s[u++]=h[r],s[u++]=61,1===o&&(s[u++]=61)),e?(u&&e.push(String.fromCharCode.apply(String,s.slice(0,u))),e.join("")):String.fromCharCode.apply(String,s.slice(0,u))};var a="invalid encoding";r.decode=function(t,i,n){for(var r,e=n,s=0,u=0;u<t.length;){var o=t.charCodeAt(u++);if(61===o&&1<s)break;if((o=f[o])===tt)throw Error(a);switch(s){case 0:r=o,s=1;break;case 1:i[n++]=r<<2|(48&o)>>4,r=o,s=2;break;case 2:i[n++]=(15&r)<<4|(60&o)>>2,r=o,s=3;break;case 3:i[n++]=(3&r)<<6|o,s=0}}if(1===s)throw Error(a);return n-e},r.test=function(t){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(t)}},{}],3:[function(t,i){function c(i,n){"string"==typeof i&&(n=i,i=tt);var f=[];function h(t){if("string"!=typeof t){var i=a();if(c.verbose&&console.log("codegen: "+i),i="return "+i,t){for(var n=Object.keys(t),r=Array(n.length+1),e=Array(n.length),s=0;s<n.length;)r[s]=n[s],e[s]=t[n[s++]];return r[s]=i,Function.apply(null,r).apply(null,e)}return Function(i)()}for(var u=Array(arguments.length-1),o=0;o<u.length;)u[o]=arguments[++o];if(o=0,t=t.replace(/%([%dfijs])/g,function(t,i){var n=u[o++];switch(i){case"d":case"f":return+n+"";case"i":return Math.floor(n)+"";case"j":return JSON.stringify(n);case"s":return n+""}return"%"}),o!==u.length)throw Error("parameter count mismatch");return f.push(t),h}function a(t){return"function "+(t||n||"")+"("+(i&&i.join(",")||"")+"){\n  "+f.join("\n  ")+"\n}"}return h.toString=a,h}(i.exports=c).verbose=!1},{}],4:[function(t,i){function n(){this.i={}}(i.exports=n).prototype.on=function(t,i,n){return(this.i[t]||(this.i[t]=[])).push({fn:i,ctx:n||this}),this},n.prototype.off=function(t,i){if(t===tt)this.i={};else if(i===tt)this.i[t]=[];else for(var n=this.i[t],r=0;r<n.length;)n[r].fn===i?n.splice(r,1):++r;return this},n.prototype.emit=function(t){var i=this.i[t];if(i){for(var n=[],r=1;r<arguments.length;)n.push(arguments[r++]);for(r=0;r<i.length;)i[r].fn.apply(i[r++].ctx,n)}return this}},{}],5:[function(t,i){i.exports=o;var s=t(1),u=t(7)("fs");function o(n,r,e){return"function"==typeof r?(e=r,r={}):r||(r={}),e?!r.xhr&&u&&u.readFile?u.readFile(n,function(t,i){return t&&"undefined"!=typeof XMLHttpRequest?o.xhr(n,r,e):t?e(t):e(null,r.binary?i:i.toString("utf8"))}):o.xhr(n,r,e):s(o,this,n,r)}o.xhr=function(t,n,r){var e=new XMLHttpRequest;e.onreadystatechange=function(){if(4!==e.readyState)return tt;if(0!==e.status&&200!==e.status)return r(Error("status "+e.status));if(n.binary){var t=e.response;if(!t){t=[];for(var i=0;i<e.responseText.length;++i)t.push(255&e.responseText.charCodeAt(i))}return r(null,"undefined"!=typeof Uint8Array?new Uint8Array(t):t)}return r(null,e.responseText)},n.binary&&("overrideMimeType"in e&&e.overrideMimeType("text/plain; charset=x-user-defined"),e.responseType="arraybuffer"),e.open("GET",t),e.send()}},{1:1,7:7}],6:[function(t,i){function n(o){return"undefined"!=typeof Float32Array?function(){var r=new Float32Array([-0]),e=new Uint8Array(r.buffer),t=128===e[3];function i(t,i,n){r[0]=t,i[n]=e[0],i[n+1]=e[1],i[n+2]=e[2],i[n+3]=e[3]}function n(t,i,n){r[0]=t,i[n]=e[3],i[n+1]=e[2],i[n+2]=e[1],i[n+3]=e[0]}function s(t,i){return e[0]=t[i],e[1]=t[i+1],e[2]=t[i+2],e[3]=t[i+3],r[0]}function u(t,i){return e[3]=t[i],e[2]=t[i+1],e[1]=t[i+2],e[0]=t[i+3],r[0]}o.writeFloatLE=t?i:n,o.writeFloatBE=t?n:i,o.readFloatLE=t?s:u,o.readFloatBE=t?u:s}():function(){function t(t,i,n,r){var e=i<0?1:0;if(e&&(i=-i),0===i)t(0<1/i?0:2147483648,n,r);else if(isNaN(i))t(2143289344,n,r);else if(34028234663852886e22<i)t((e<<31|2139095040)>>>0,n,r);else if(i<11754943508222875e-54)t((e<<31|Math.round(i/1401298464324817e-60))>>>0,n,r);else{var s=Math.floor(Math.log(i)/Math.LN2);t((e<<31|s+127<<23|8388607&Math.round(i*Math.pow(2,-s)*8388608))>>>0,n,r)}}function i(t,i,n){var r=t(i,n),e=2*(r>>31)+1,s=r>>>23&255,u=8388607&r;return 255===s?u?NaN:e*(1/0):0===s?1401298464324817e-60*e*u:e*Math.pow(2,s-150)*(u+8388608)}o.writeFloatLE=t.bind(null,r),o.writeFloatBE=t.bind(null,e),o.readFloatLE=i.bind(null,s),o.readFloatBE=i.bind(null,u)}(),"undefined"!=typeof Float64Array?function(){var r=new Float64Array([-0]),e=new Uint8Array(r.buffer),t=128===e[7];function i(t,i,n){r[0]=t,i[n]=e[0],i[n+1]=e[1],i[n+2]=e[2],i[n+3]=e[3],i[n+4]=e[4],i[n+5]=e[5],i[n+6]=e[6],i[n+7]=e[7]}function n(t,i,n){r[0]=t,i[n]=e[7],i[n+1]=e[6],i[n+2]=e[5],i[n+3]=e[4],i[n+4]=e[3],i[n+5]=e[2],i[n+6]=e[1],i[n+7]=e[0]}function s(t,i){return e[0]=t[i],e[1]=t[i+1],e[2]=t[i+2],e[3]=t[i+3],e[4]=t[i+4],e[5]=t[i+5],e[6]=t[i+6],e[7]=t[i+7],r[0]}function u(t,i){return e[7]=t[i],e[6]=t[i+1],e[5]=t[i+2],e[4]=t[i+3],e[3]=t[i+4],e[2]=t[i+5],e[1]=t[i+6],e[0]=t[i+7],r[0]}o.writeDoubleLE=t?i:n,o.writeDoubleBE=t?n:i,o.readDoubleLE=t?s:u,o.readDoubleBE=t?u:s}():function(){function t(t,i,n,r,e,s){var u=r<0?1:0;if(u&&(r=-r),0===r)t(0,e,s+i),t(0<1/r?0:2147483648,e,s+n);else if(isNaN(r))t(0,e,s+i),t(2146959360,e,s+n);else if(17976931348623157e292<r)t(0,e,s+i),t((u<<31|2146435072)>>>0,e,s+n);else{var o;if(r<22250738585072014e-324)t((o=r/5e-324)>>>0,e,s+i),t((u<<31|o/4294967296)>>>0,e,s+n);else{var f=Math.floor(Math.log(r)/Math.LN2);1024===f&&(f=1023),t(4503599627370496*(o=r*Math.pow(2,-f))>>>0,e,s+i),t((u<<31|f+1023<<20|1048576*o&1048575)>>>0,e,s+n)}}}function i(t,i,n,r,e){var s=t(r,e+i),u=t(r,e+n),o=2*(u>>31)+1,f=u>>>20&2047,h=4294967296*(1048575&u)+s;return 2047===f?h?NaN:o*(1/0):0===f?5e-324*o*h:o*Math.pow(2,f-1075)*(h+4503599627370496)}o.writeDoubleLE=t.bind(null,r,0,4),o.writeDoubleBE=t.bind(null,e,4,0),o.readDoubleLE=i.bind(null,s,0,4),o.readDoubleBE=i.bind(null,u,4,0)}(),o}function r(t,i,n){i[n]=255&t,i[n+1]=t>>>8&255,i[n+2]=t>>>16&255,i[n+3]=t>>>24}function e(t,i,n){i[n]=t>>>24,i[n+1]=t>>>16&255,i[n+2]=t>>>8&255,i[n+3]=255&t}function s(t,i){return(t[i]|t[i+1]<<8|t[i+2]<<16|t[i+3]<<24)>>>0}function u(t,i){return(t[i]<<24|t[i+1]<<16|t[i+2]<<8|t[i+3])>>>0}i.exports=n(n)},{}],7:[function(t,i,n){function r(t){try{var i=eval("require")(t);if(i&&(i.length||Object.keys(i).length))return i}catch(t){}return null}i.exports=r},{}],8:[function(t,i,n){var r=n,s=r.isAbsolute=function(t){return/^(?:\/|\w+:)/.test(t)},e=r.normalize=function(t){var i=(t=t.replace(/\\/g,"/").replace(/\/{2,}/g,"/")).split("/"),n=s(t),r="";n&&(r=i.shift()+"/");for(var e=0;e<i.length;)".."===i[e]?0<e&&".."!==i[e-1]?i.splice(--e,2):n?i.splice(e,1):++e:"."===i[e]?i.splice(e,1):++e;return r+i.join("/")};r.resolve=function(t,i,n){return n||(i=e(i)),s(i)?i:(n||(t=e(t)),(t=t.replace(/(?:\/|^)[^/]+$/,"")).length?e(t+"/"+i):i)}},{}],9:[function(t,i){i.exports=function(n,r,t){var e=t||8192,s=e>>>1,u=null,o=e;return function(t){if(t<1||s<t)return n(t);e<o+t&&(u=n(e),o=0);var i=r.call(u,o,o+=t);return 7&o&&(o=1+(7|o)),i}}},{}],10:[function(t,i,n){var r=n;r.length=function(t){for(var i=0,n=0,r=0;r<t.length;++r)(n=t.charCodeAt(r))<128?i+=1:n<2048?i+=2:55296==(64512&n)&&56320==(64512&t.charCodeAt(r+1))?(++r,i+=4):i+=3;return i},r.read=function(t,i,n){if(n-i<1)return"";for(var r,e=null,s=[],u=0;i<n;)(r=t[i++])<128?s[u++]=r:191<r&&r<224?s[u++]=(31&r)<<6|63&t[i++]:239<r&&r<365?(r=((7&r)<<18|(63&t[i++])<<12|(63&t[i++])<<6|63&t[i++])-65536,s[u++]=55296+(r>>10),s[u++]=56320+(1023&r)):s[u++]=(15&r)<<12|(63&t[i++])<<6|63&t[i++],8191<u&&((e||(e=[])).push(String.fromCharCode.apply(String,s)),u=0);return e?(u&&e.push(String.fromCharCode.apply(String,s.slice(0,u))),e.join("")):String.fromCharCode.apply(String,s.slice(0,u))},r.write=function(t,i,n){for(var r,e,s=n,u=0;u<t.length;++u)(r=t.charCodeAt(u))<128?i[n++]=r:(r<2048?i[n++]=r>>6|192:(55296==(64512&r)&&56320==(64512&(e=t.charCodeAt(u+1)))?(r=65536+((1023&r)<<10)+(1023&e),++u,i[n++]=r>>18|240,i[n++]=r>>12&63|128):i[n++]=r>>12|224,i[n++]=r>>6&63|128),i[n++]=63&r|128);return n-s}},{}],11:[function(t,i){i.exports=e;var n,r=/\/|\./;function e(t,i){r.test(t)||(t="google/protobuf/"+t+".proto",i={nested:{google:{nested:{protobuf:{nested:i}}}}}),e[t]=i}e("any",{Any:{fields:{type_url:{type:"string",id:1},value:{type:"bytes",id:2}}}}),e("duration",{Duration:n={fields:{seconds:{type:"int64",id:1},nanos:{type:"int32",id:2}}}}),e("timestamp",{Timestamp:n}),e("empty",{Empty:{fields:{}}}),e("struct",{Struct:{fields:{fields:{keyType:"string",type:"Value",id:1}}},Value:{oneofs:{kind:{oneof:["nullValue","numberValue","stringValue","boolValue","structValue","listValue"]}},fields:{nullValue:{type:"NullValue",id:1},numberValue:{type:"double",id:2},stringValue:{type:"string",id:3},boolValue:{type:"bool",id:4},structValue:{type:"Struct",id:5},listValue:{type:"ListValue",id:6}}},NullValue:{values:{NULL_VALUE:0}},ListValue:{fields:{values:{rule:"repeated",type:"Value",id:1}}}}),e("wrappers",{DoubleValue:{fields:{value:{type:"double",id:1}}},FloatValue:{fields:{value:{type:"float",id:1}}},Int64Value:{fields:{value:{type:"int64",id:1}}},UInt64Value:{fields:{value:{type:"uint64",id:1}}},Int32Value:{fields:{value:{type:"int32",id:1}}},UInt32Value:{fields:{value:{type:"uint32",id:1}}},BoolValue:{fields:{value:{type:"bool",id:1}}},StringValue:{fields:{value:{type:"string",id:1}}},BytesValue:{fields:{value:{type:"bytes",id:1}}}}),e("field_mask",{FieldMask:{fields:{paths:{rule:"repeated",type:"string",id:1}}}}),e.get=function(t){return e[t]||null}},{}],12:[function(t,i,n){var r=n,l=t(15),v=t(37);function o(t,i,n,r,e){if(e===tt&&(e="d"+r),i.resolvedType)if(i.resolvedType instanceof l){t("switch(%s){",e);for(var s=i.resolvedType.values,u=Object.keys(s),o=0;o<u.length;++o)i.repeated&&s[u[o]]===i.typeDefault&&t("default:"),t("case%j:",u[o])("case %i:",s[u[o]])("m%s=%j",r,s[u[o]])("break");t("}")}else t('if(typeof %s!=="object")',e)("throw TypeError(%j)",i.fullName+": object expected")("m%s=types[%i].fromObject(%s)",r,n,e);else{var f=!1;switch(i.type){case"double":case"float":t("m%s=Number(%s)",r,e);break;case"uint32":case"fixed32":t("m%s=%s>>>0",r,e);break;case"int32":case"sint32":case"sfixed32":t("m%s=%s|0",r,e);break;case"uint64":f=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":t("if(util.Long)")("(m%s=util.Long.fromValue(%s)).unsigned=%j",r,e,f)('else if(typeof %s==="string")',e)("m%s=parseInt(%s,10)",r,e)('else if(typeof %s==="number")',e)("m%s=%s",r,e)('else if(typeof %s==="object")',e)("m%s=new util.LongBits(%s.low>>>0,%s.high>>>0).toNumber(%s)",r,e,e,f?"true":"");break;case"bytes":t('if(typeof %s==="string")',e)("util.base64.decode(%s,m%s=util.newBuffer(util.base64.length(%s)),0)",e,r,e)("else if(%s.length)",e)("m%s=%s",r,e);break;case"string":t("m%s=String(%s)",r,e);break;case"bool":t("m%s=Boolean(%s)",r,e)}}return t}function d(t,i,n,r){if(i.resolvedType)i.resolvedType instanceof l?t("d%s=o.enums===String?types[%i].values[m%s]:m%s",r,n,r,r):t("d%s=types[%i].toObject(m%s,o)",r,n,r);else{var e=!1;switch(i.type){case"double":case"float":t("d%s=o.json&&!isFinite(m%s)?String(m%s):m%s",r,r,r,r);break;case"uint64":e=!0;case"int64":case"sint64":case"fixed64":case"sfixed64":t('if(typeof m%s==="number")',r)("d%s=o.longs===String?String(m%s):m%s",r,r,r)("else")("d%s=o.longs===String?util.Long.prototype.toString.call(m%s):o.longs===Number?new util.LongBits(m%s.low>>>0,m%s.high>>>0).toNumber(%s):m%s",r,r,r,r,e?"true":"",r);break;case"bytes":t("d%s=o.bytes===String?util.base64.encode(m%s,0,m%s.length):o.bytes===Array?Array.prototype.slice.call(m%s):m%s",r,r,r,r,r);break;default:t("d%s=m%s",r,r)}}return t}r.fromObject=function(t){var i=t.fieldsArray,n=v.codegen(["d"],t.name+"$fromObject")("if(d instanceof this.ctor)")("return d");if(!i.length)return n("return new this.ctor");n("var m=new this.ctor");for(var r=0;r<i.length;++r){var e=i[r].resolve(),s=v.safeProp(e.name);if(e.map)n("if(d%s){",s)('if(typeof d%s!=="object")',s)("throw TypeError(%j)",e.fullName+": object expected")("m%s={}",s)("for(var ks=Object.keys(d%s),i=0;i<ks.length;++i){",s),o(n,e,r,s+"[ks[i]]")("}")("}");else if(e.repeated){n("if(d%s){",s);var u="d"+s;e.useToArray()&&(n("var %s",u="array"+e.id),n("if (d%s!=null&&d%s.toArray) { %s = d%s.toArray() } else { %s = d%s }",s,s,u,s,u,s)),n("if(!Array.isArray(%s))",u)("throw TypeError(%j)",e.fullName+": array expected")("m%s=[]",s)("for(var i=0;i<%s.length;++i){",u),o(n,e,r,s+"[i]",u+"[i]")("}")("}")}else e.resolvedType instanceof l||n("if(d%s!=null){",s),o(n,e,r,s),e.resolvedType instanceof l||n("}")}return n("return m")},r.toObject=function(t){var i=t.fieldsArray.slice().sort(v.compareFieldsById);if(!i.length)return v.codegen()("return {}");for(var n=v.codegen(["m","o"],t.name+"$toObject")("if(!o)")("o={}")("var d={}"),r=[],e=[],s=[],u=0;u<i.length;++u)i[u].partOf||(i[u].resolve().repeated?r:i[u].map?e:s).push(i[u]);if(r.length){for(n("if(o.arrays||o.defaults){"),u=0;u<r.length;++u)n("d%s=[]",v.safeProp(r[u].name));n("}")}if(e.length){for(n("if(o.objects||o.defaults){"),u=0;u<e.length;++u)n("d%s={}",v.safeProp(e[u].name));n("}")}if(s.length){for(n("if(o.defaults){"),u=0;u<s.length;++u){var o=s[u],f=v.safeProp(o.name);if(o.resolvedType instanceof l)n("d%s=o.enums===String?%j:%j",f,o.resolvedType.valuesById[o.typeDefault],o.typeDefault);else if(o.long)n("if(util.Long){")("var n=new util.Long(%i,%i,%j)",o.typeDefault.low,o.typeDefault.high,o.typeDefault.unsigned)("d%s=o.longs===String?n.toString():o.longs===Number?n.toNumber():n",f)("}else")("d%s=o.longs===String?%j:%i",f,o.typeDefault.toString(),o.typeDefault.toNumber());else if(o.bytes){var h="["+Array.prototype.slice.call(o.typeDefault).join(",")+"]";n("if(o.bytes===String)d%s=%j",f,String.fromCharCode.apply(String,o.typeDefault))("else{")("d%s=%s",f,h)("if(o.bytes!==Array)d%s=util.newBuffer(d%s)",f,f)("}")}else n("d%s=%j",f,o.typeDefault)}n("}")}var a=!1;for(u=0;u<i.length;++u){o=i[u];var c=t.e.indexOf(o);f=v.safeProp(o.name);o.map?(a||(a=!0,n("var ks2")),n("if(m%s&&(ks2=Object.keys(m%s)).length){",f,f)("d%s={}",f)("for(var j=0;j<ks2.length;++j){"),d(n,o,c,f+"[ks2[j]]")("}")):o.repeated?(n("if(m%s&&m%s.length){",f,f)("d%s=[]",f)("for(var j=0;j<m%s.length;++j){",f),d(n,o,c,f+"[j]")("}")):(n("if(m%s!=null&&m.hasOwnProperty(%j)){",f,o.name),d(n,o,c,f),o.partOf&&n("if(o.oneofs)")("d%s=%j",v.safeProp(o.partOf.name),o.name)),n("}")}return n("return d")}},{15:15,37:37}],13:[function(t,i){i.exports=function(t){var i=h.codegen(["r","l"],t.name+"$decode")("if(!(r instanceof Reader))")("r=Reader.create(r)")("var c=l===undefined?r.len:r.pos+l,m=new this.ctor"+(t.fieldsArray.filter(function(t){return t.map}).length?",k":""))("while(r.pos<c){")("var t=r.uint32()");t.group&&i("if((t&7)===4)")("break");i("switch(t>>>3){");for(var n=0;n<t.fieldsArray.length;++n){var r=t.e[n].resolve(),e=r.resolvedType instanceof o?"int32":r.type,s="m"+h.safeProp(r.name);i("case %i:",r.id),r.map?(i("r.skip().pos++")("if(%s===util.emptyObject)",s)("%s={}",s)("k=r.%s()",r.keyType)("r.pos++"),f.long[r.keyType]!==tt?f.basic[e]===tt?i('%s[typeof k==="object"?util.longToHash(k):k]=types[%i].decode(r,r.uint32())',s,n):i('%s[typeof k==="object"?util.longToHash(k):k]=r.%s()',s,e):f.basic[e]===tt?i("%s[k]=types[%i].decode(r,r.uint32())",s,n):i("%s[k]=r.%s()",s,e)):r.repeated?(i("if(!(%s&&%s.length))",s,s)("%s=[]",s),f.packed[e]!==tt&&i("if((t&7)===2){")("var c2=r.uint32()+r.pos")("while(r.pos<c2)")("%s.push(r.%s())",s,e)("}else"),f.basic[e]===tt?i(r.resolvedType.group?"%s.push(types[%i].decode(r))":"%s.push(types[%i].decode(r,r.uint32()))",s,n):i("%s.push(r.%s())",s,e)):f.basic[e]===tt?i(r.resolvedType.group?"%s=types[%i].decode(r)":"%s=types[%i].decode(r,r.uint32())",s,n):i("%s=r.%s()",s,e),i("break")}for(i("default:")("r.skipType(t&7)")("break")("}")("}"),n=0;n<t.e.length;++n){var u=t.e[n];u.required&&i("if(!m.hasOwnProperty(%j))",u.name)("throw util.ProtocolError(%j,{instance:m})","missing required '"+u.name+"'")}return i("return m")};var o=t(15),f=t(36),h=t(37)},{15:15,36:36,37:37}],14:[function(t,i){i.exports=function(t){for(var i,n=l.codegen(["m","w"],t.name+"$encode")("if(!w)")("w=Writer.create()"),r=t.fieldsArray.slice().sort(l.compareFieldsById),e=0;e<r.length;++e){var s=r[e].resolve(),u=t.e.indexOf(s),o=s.resolvedType instanceof a?"int32":s.type,f=c.basic[o];if(i="m"+l.safeProp(s.name),s.map)n("if(%s!=null&&Object.hasOwnProperty.call(m,%j)){",i,s.name)("for(var ks=Object.keys(%s),i=0;i<ks.length;++i){",i)("w.uint32(%i).fork().uint32(%i).%s(ks[i])",(s.id<<3|2)>>>0,8|c.mapKey[s.keyType],s.keyType),f===tt?n("types[%i].encode(%s[ks[i]],w.uint32(18).fork()).ldelim().ldelim()",u,i):n(".uint32(%i).%s(%s[ks[i]]).ldelim()",16|f,o,i),n("}")("}");else if(s.repeated){var h=i;s.useToArray()&&(h="array"+s.id,n("var %s",h),n("if (%s!=null&&%s.toArray) { %s = %s.toArray() } else { %s = %s }",i,i,h,i,h,i)),n("if(%s!=null&&%s.length){",h,h),s.packed&&c.packed[o]!==tt?n("w.uint32(%i).fork()",(s.id<<3|2)>>>0)("for(var i=0;i<%s.length;++i)",h)("w.%s(%s[i])",o,h)("w.ldelim()"):(n("for(var i=0;i<%s.length;++i)",h),f===tt?v(n,s,u,h+"[i]"):n("w.uint32(%i).%s(%s[i])",(s.id<<3|f)>>>0,o,h)),n("}")}else s.optional&&n("if(%s!=null&&Object.hasOwnProperty.call(m,%j))",i,s.name),f===tt?v(n,s,u,i):n("w.uint32(%i).%s(%s)",(s.id<<3|f)>>>0,o,i)}return n("return w")};var a=t(15),c=t(36),l=t(37);function v(t,i,n,r){if(i.resolvedType.group)t("types[%i].encode(%s,w.uint32(%i)).uint32(%i)",n,r,(i.id<<3|3)>>>0,(i.id<<3|4)>>>0);else{var e=(i.id<<3|2)>>>0;i.preEncoded()&&t("if (%s instanceof Uint8Array) {",r)("w.uint32(%i)",e)("w.bytes(%s)",r)("} else {"),t("types[%i].encode(%s,w.uint32(%i).fork()).ldelim()",n,r,e),i.preEncoded()&&t("}")}}},{15:15,36:36,37:37}],15:[function(t,i){i.exports=e;var o=t(24);((e.prototype=Object.create(o.prototype)).constructor=e).className="Enum";var n=t(23),r=t(37);function e(t,i,n,r,e){if(o.call(this,t,n),i&&"object"!=typeof i)throw TypeError("values must be an object");if(this.valuesById={},this.values=Object.create(this.valuesById),this.comment=r,this.comments=e||{},this.reserved=tt,i)for(var s=Object.keys(i),u=0;u<s.length;++u)"number"==typeof i[s[u]]&&(this.valuesById[this.values[s[u]]=i[s[u]]]=s[u])}e.fromJSON=function(t,i){var n=new e(t,i.values,i.options,i.comment,i.comments);return n.reserved=i.reserved,n},e.prototype.toJSON=function(t){var i=!!t&&!!t.keepComments;return r.toObject(["options",this.options,"values",this.values,"reserved",this.reserved&&this.reserved.length?this.reserved:tt,"comment",i?this.comment:tt,"comments",i?this.comments:tt])},e.prototype.add=function(t,i,n){if(!r.isString(t))throw TypeError("name must be a string");if(!r.isInteger(i))throw TypeError("id must be an integer");if(this.values[t]!==tt)throw Error("duplicate name '"+t+"' in "+this);if(this.isReservedId(i))throw Error("id "+i+" is reserved in "+this);if(this.isReservedName(t))throw Error("name '"+t+"' is reserved in "+this);if(this.valuesById[i]!==tt){if(!this.options||!this.options.allow_alias)throw Error("duplicate id "+i+" in "+this);this.values[t]=i}else this.valuesById[this.values[t]=i]=t;return this.comments[t]=n||null,this},e.prototype.remove=function(t){if(!r.isString(t))throw TypeError("name must be a string");var i=this.values[t];if(null==i)throw Error("name '"+t+"' does not exist in "+this);return delete this.valuesById[i],delete this.values[t],delete this.comments[t],this},e.prototype.isReservedId=function(t){return n.isReservedId(this.reserved,t)},e.prototype.isReservedName=function(t){return n.isReservedName(this.reserved,t)}},{23:23,24:24,37:37}],16:[function(t,i){i.exports=u;var o=t(24);((u.prototype=Object.create(o.prototype)).constructor=u).className="Field";var n,r=t(15),f=t(36),h=t(37),a=/^required|optional|repeated$/;function u(t,i,n,r,e,s,u){if(h.isObject(r)?(u=e,s=r,r=e=tt):h.isObject(e)&&(u=s,s=e,e=tt),o.call(this,t,s),!h.isInteger(i)||i<0)throw TypeError("id must be a non-negative integer");if(!h.isString(n))throw TypeError("type must be a string");if(r!==tt&&!a.test(r=r.toString().toLowerCase()))throw TypeError("rule must be a string rule");if(e!==tt&&!h.isString(e))throw TypeError("extend must be a string");this.rule=r&&"optional"!==r?r:tt,this.type=n,this.id=i,this.extend=e||tt,this.required="required"===r,this.optional=!this.required,this.repeated="repeated"===r,this.map=!1,this.message=null,this.partOf=null,this.typeDefault=null,this.defaultValue=null,this.long=!!h.Long&&f.long[n]!==tt,this.bytes="bytes"===n,this.resolvedType=null,this.extensionField=null,this.declaringField=null,this.u=null,this.comment=u}u.fromJSON=function(t,i){return new u(t,i.id,i.type,i.rule,i.extend,i.options,i.comment)},Object.defineProperty(u.prototype,"packed",{get:function(){return null===this.u&&(this.u=!1!==this.getOption("packed")),this.u}}),u.prototype.setOption=function(t,i,n){return"packed"===t&&(this.u=null),o.prototype.setOption.call(this,t,i,n)},u.prototype.toJSON=function(t){var i=!!t&&!!t.keepComments;return h.toObject(["rule","optional"!==this.rule&&this.rule||tt,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",i?this.comment:tt])},u.prototype.resolve=function(){if(this.resolved)return this;if((this.typeDefault=f.defaults[this.type])===tt&&(this.resolvedType=(this.declaringField?this.declaringField.parent:this.parent).lookupTypeOrEnum(this.type),this.resolvedType instanceof n?this.typeDefault=null:this.typeDefault=this.resolvedType.values[Object.keys(this.resolvedType.values)[0]]),this.options&&null!=this.options.default&&(this.typeDefault=this.options.default,this.resolvedType instanceof r&&"string"==typeof this.typeDefault&&(this.typeDefault=this.resolvedType.values[this.typeDefault])),this.options&&(!0!==this.options.packed&&(this.options.packed===tt||!this.resolvedType||this.resolvedType instanceof r)||delete this.options.packed,Object.keys(this.options).length||(this.options=tt)),this.long)this.typeDefault=h.Long.fromNumber(this.typeDefault,"u"===this.type.charAt(0)),Object.freeze&&Object.freeze(this.typeDefault);else if(this.bytes&&"string"==typeof this.typeDefault){var t;h.base64.test(this.typeDefault)?h.base64.decode(this.typeDefault,t=h.newBuffer(h.base64.length(this.typeDefault)),0):h.utf8.write(this.typeDefault,t=h.newBuffer(h.utf8.length(this.typeDefault)),0),this.typeDefault=t}return this.map?this.defaultValue=h.emptyObject:this.repeated?this.defaultValue=h.emptyArray:this.defaultValue=this.typeDefault,this.parent instanceof n&&(this.parent.ctor.prototype[this.name]=this.defaultValue),o.prototype.resolve.call(this)},u.prototype.useToArray=function(){return!!this.getOption("(js_use_toArray)")},u.prototype.preEncoded=function(){return!!this.getOption("(js_preEncoded)")},u.d=function(n,r,e,s){return"function"==typeof r?r=h.decorateType(r).name:r&&"object"==typeof r&&(r=h.decorateEnum(r).name),function(t,i){h.decorateType(t.constructor).add(new u(i,n,r,e,{default:s}))}},u.o=function(t){n=t}},{15:15,24:24,36:36,37:37}],17:[function(t,i){var r=i.exports=t(18);r.build="light",r.load=function(t,i,n){return"function"==typeof i?(n=i,i=new r.Root):i||(i=new r.Root),i.load(t,n)},r.loadSync=function(t,i){return i||(i=new r.Root),i.loadSync(t)},r.encoder=t(14),r.decoder=t(13),r.verifier=t(40),r.converter=t(12),r.ReflectionObject=t(24),r.Namespace=t(23),r.Root=t(29),r.Enum=t(15),r.Type=t(35),r.Field=t(16),r.OneOf=t(25),r.MapField=t(20),r.Service=t(33),r.Method=t(22),r.Message=t(21),r.wrappers=t(41),r.types=t(36),r.util=t(37),r.ReflectionObject.o(r.Root),r.Namespace.o(r.Type,r.Service,r.Enum),r.Root.o(r.Type),r.Field.o(r.Type)},{12:12,13:13,14:14,15:15,16:16,18:18,20:20,21:21,22:22,23:23,24:24,25:25,29:29,33:33,35:35,36:36,37:37,40:40,41:41}],18:[function(t,i,n){var r=n;function e(){r.Reader.o(r.BufferReader),r.util.o()}r.build="minimal",r.Writer=t(42),r.BufferWriter=t(43),r.Reader=t(27),r.BufferReader=t(28),r.util=t(39),r.rpc=t(31),r.roots=t(30),r.configure=e,r.Writer.o(r.BufferWriter),e()},{27:27,28:28,30:30,31:31,39:39,42:42,43:43}],19:[function(t,i){var n=i.exports=t(17);n.build="full",n.tokenize=t(34),n.parse=t(26),n.common=t(11),n.Root.o(n.Type,n.parse,n.common)},{11:11,17:17,26:26,34:34}],20:[function(t,i){i.exports=s;var u=t(16);((s.prototype=Object.create(u.prototype)).constructor=s).className="MapField";var n=t(36),o=t(37);function s(t,i,n,r,e,s){if(u.call(this,t,i,r,tt,tt,e,s),!o.isString(n))throw TypeError("keyType must be a string");this.keyType=n,this.resolvedKeyType=null,this.map=!0}s.fromJSON=function(t,i){return new s(t,i.id,i.keyType,i.type,i.options,i.comment)},s.prototype.toJSON=function(t){var i=!!t&&!!t.keepComments;return o.toObject(["keyType",this.keyType,"type",this.type,"id",this.id,"extend",this.extend,"options",this.options,"comment",i?this.comment:tt])},s.prototype.resolve=function(){if(this.resolved)return this;if(n.mapKey[this.keyType]===tt)throw Error("invalid key type: "+this.keyType);return u.prototype.resolve.call(this)},s.d=function(n,r,e){return"function"==typeof e?e=o.decorateType(e).name:e&&"object"==typeof e&&(e=o.decorateEnum(e).name),function(t,i){o.decorateType(t.constructor).add(new s(i,n,r,e))}}},{16:16,36:36,37:37}],21:[function(t,i){i.exports=r;var n=t(39);function r(t){if(t)for(var i=Object.keys(t),n=0;n<i.length;++n)this[i[n]]=t[i[n]]}r.create=function(t){return this.$type.create(t)},r.encode=function(t,i){return this.$type.encode(t,i)},r.encodeDelimited=function(t,i){return this.$type.encodeDelimited(t,i)},r.decode=function(t){return this.$type.decode(t)},r.decodeDelimited=function(t){return this.$type.decodeDelimited(t)},r.verify=function(t){return this.$type.verify(t)},r.fromObject=function(t){return this.$type.fromObject(t)},r.toObject=function(t,i){return this.$type.toObject(t,i)},r.prototype.toJSON=function(){return this.$type.toObject(this,n.toJSONOptions)}},{39:39}],22:[function(t,i){i.exports=n;var f=t(24);((n.prototype=Object.create(f.prototype)).constructor=n).className="Method";var h=t(37);function n(t,i,n,r,e,s,u,o){if(h.isObject(e)?(u=e,e=s=tt):h.isObject(s)&&(u=s,s=tt),i!==tt&&!h.isString(i))throw TypeError("type must be a string");if(!h.isString(n))throw TypeError("requestType must be a string");if(!h.isString(r))throw TypeError("responseType must be a string");f.call(this,t,u),this.type=i||"rpc",this.requestType=n,this.requestStream=!!e||tt,this.responseType=r,this.responseStream=!!s||tt,this.resolvedRequestType=null,this.resolvedResponseType=null,this.comment=o}n.fromJSON=function(t,i){return new n(t,i.type,i.requestType,i.responseType,i.requestStream,i.responseStream,i.options,i.comment)},n.prototype.toJSON=function(t){var i=!!t&&!!t.keepComments;return h.toObject(["type","rpc"!==this.type&&this.type||tt,"requestType",this.requestType,"requestStream",this.requestStream,"responseType",this.responseType,"responseStream",this.responseStream,"options",this.options,"comment",i?this.comment:tt])},n.prototype.resolve=function(){return this.resolved?this:(this.resolvedRequestType=this.parent.lookupType(this.requestType),this.resolvedResponseType=this.parent.lookupType(this.responseType),f.prototype.resolve.call(this))}},{24:24,37:37}],23:[function(t,i){i.exports=h;var n=t(24);((h.prototype=Object.create(n.prototype)).constructor=h).className="Namespace";var e,s,u,o=t(16),f=t(37);function r(t,i){if(!t||!t.length)return tt;for(var n={},r=0;r<t.length;++r)n[t[r].name]=t[r].toJSON(i);return n}function h(t,i){n.call(this,t,i),this.nested=tt,this.f=null}function a(t){return t.f=null,t}h.fromJSON=function(t,i){return new h(t,i.options).addJSON(i.nested)},h.arrayToJSON=r,h.isReservedId=function(t,i){if(t)for(var n=0;n<t.length;++n)if("string"!=typeof t[n]&&t[n][0]<=i&&t[n][1]>i)return!0;return!1},h.isReservedName=function(t,i){if(t)for(var n=0;n<t.length;++n)if(t[n]===i)return!0;return!1},Object.defineProperty(h.prototype,"nestedArray",{get:function(){return this.f||(this.f=f.toArray(this.nested))}}),h.prototype.toJSON=function(t){return f.toObject(["options",this.options,"nested",r(this.nestedArray,t)])},h.prototype.addJSON=function(t){if(t)for(var i,n=Object.keys(t),r=0;r<n.length;++r)i=t[n[r]],this.add((i.fields!==tt?e.fromJSON:i.values!==tt?u.fromJSON:i.methods!==tt?s.fromJSON:i.id!==tt?o.fromJSON:h.fromJSON)(n[r],i));return this},h.prototype.get=function(t){return this.nested&&this.nested[t]||null},h.prototype.getEnum=function(t){if(this.nested&&this.nested[t]instanceof u)return this.nested[t].values;throw Error("no such enum: "+t)},h.prototype.add=function(t){if(!(t instanceof o&&t.extend!==tt||t instanceof e||t instanceof u||t instanceof s||t instanceof h))throw TypeError("object must be a valid nested object");if(this.nested){var i=this.get(t.name);if(i){if(!(i instanceof h&&t instanceof h)||i instanceof e||i instanceof s)throw Error("duplicate name '"+t.name+"' in "+this);for(var n=i.nestedArray,r=0;r<n.length;++r)t.add(n[r]);this.remove(i),this.nested||(this.nested={}),t.setOptions(i.options,!0)}}else this.nested={};return(this.nested[t.name]=t).onAdd(this),a(this)},h.prototype.remove=function(t){if(!(t instanceof n))throw TypeError("object must be a ReflectionObject");if(t.parent!==this)throw Error(t+" is not a member of "+this);return delete this.nested[t.name],Object.keys(this.nested).length||(this.nested=tt),t.onRemove(this),a(this)},h.prototype.define=function(t,i){if(f.isString(t))t=t.split(".");else if(!Array.isArray(t))throw TypeError("illegal path");if(t&&t.length&&""===t[0])throw Error("path must be relative");for(var n=this;0<t.length;){var r=t.shift();if(n.nested&&n.nested[r]){if(!((n=n.nested[r])instanceof h))throw Error("path conflicts with non-namespace objects")}else n.add(n=new h(r))}return i&&n.addJSON(i),n},h.prototype.resolveAll=function(){for(var t=this.nestedArray,i=0;i<t.length;)t[i]instanceof h?t[i++].resolveAll():t[i++].resolve();return this.resolve()},h.prototype.lookup=function(t,i,n){if("boolean"==typeof i?(n=i,i=tt):i&&!Array.isArray(i)&&(i=[i]),f.isString(t)&&t.length){if("."===t)return this.root;t=t.split(".")}else if(!t.length)return this;if(""===t[0])return this.root.lookup(t.slice(1),i);var r=this.get(t[0]);if(r){if(1===t.length){if(!i||-1<i.indexOf(r.constructor))return r}else if(r instanceof h&&(r=r.lookup(t.slice(1),i,!0)))return r}else for(var e=0;e<this.nestedArray.length;++e)if(this.f[e]instanceof h&&(r=this.f[e].lookup(t,i,!0)))return r;return null===this.parent||n?null:this.parent.lookup(t,i)},h.prototype.lookupType=function(t){var i=this.lookup(t,[e]);if(!i)throw Error("no such type: "+t);return i},h.prototype.lookupEnum=function(t){var i=this.lookup(t,[u]);if(!i)throw Error("no such Enum '"+t+"' in "+this);return i},h.prototype.lookupTypeOrEnum=function(t){var i=this.lookup(t,[e,u]);if(!i)throw Error("no such Type or Enum '"+t+"' in "+this);return i},h.prototype.lookupService=function(t){var i=this.lookup(t,[s]);if(!i)throw Error("no such Service '"+t+"' in "+this);return i},h.o=function(t,i,n){e=t,s=i,u=n}},{16:16,24:24,37:37}],24:[function(t,i){(i.exports=e).className="ReflectionObject";var n,r=t(37);function e(t,i){if(!r.isString(t))throw TypeError("name must be a string");if(i&&!r.isObject(i))throw TypeError("options must be an object");this.options=i,this.name=t,this.parent=null,this.resolved=!1,this.comment=null,this.filename=null}Object.defineProperties(e.prototype,{root:{get:function(){for(var t=this;null!==t.parent;)t=t.parent;return t}},fullName:{get:function(){for(var t=[this.name],i=this.parent;i;)t.unshift(i.name),i=i.parent;return t.join(".")}}}),e.prototype.toJSON=function(){throw Error()},e.prototype.onAdd=function(t){this.parent&&this.parent!==t&&this.parent.remove(this),this.parent=t,this.resolved=!1;var i=t.root;i instanceof n&&i.h(this)},e.prototype.onRemove=function(t){var i=t.root;i instanceof n&&i.a(this),this.parent=null,this.resolved=!1},e.prototype.resolve=function(){return this.resolved||this.root instanceof n&&(this.resolved=!0),this},e.prototype.getOption=function(t){return this.options?this.options[t]:tt},e.prototype.setOption=function(t,i,n){return n&&this.options&&this.options[t]!==tt||((this.options||(this.options={}))[t]=i),this},e.prototype.setOptions=function(t,i){if(t)for(var n=Object.keys(t),r=0;r<n.length;++r)this.setOption(n[r],t[n[r]],i);return this},e.prototype.toString=function(){var t=this.constructor.className,i=this.fullName;return i.length?t+" "+i:t},e.o=function(t){n=t}},{37:37}],25:[function(t,i){i.exports=s;var e=t(24);((s.prototype=Object.create(e.prototype)).constructor=s).className="OneOf";var n=t(16),r=t(37);function s(t,i,n,r){if(Array.isArray(i)||(n=i,i=tt),e.call(this,t,n),i!==tt&&!Array.isArray(i))throw TypeError("fieldNames must be an Array");this.oneof=i||[],this.fieldsArray=[],this.comment=r}function u(t){if(t.parent)for(var i=0;i<t.fieldsArray.length;++i)t.fieldsArray[i].parent||t.parent.add(t.fieldsArray[i])}s.fromJSON=function(t,i){return new s(t,i.oneof,i.options,i.comment)},s.prototype.toJSON=function(t){var i=!!t&&!!t.keepComments;return r.toObject(["options",this.options,"oneof",this.oneof,"comment",i?this.comment:tt])},s.prototype.add=function(t){if(!(t instanceof n))throw TypeError("field must be a Field");return t.parent&&t.parent!==this.parent&&t.parent.remove(t),this.oneof.push(t.name),this.fieldsArray.push(t),u(t.partOf=this),this},s.prototype.remove=function(t){if(!(t instanceof n))throw TypeError("field must be a Field");var i=this.fieldsArray.indexOf(t);if(i<0)throw Error(t+" is not a member of "+this);return this.fieldsArray.splice(i,1),-1<(i=this.oneof.indexOf(t.name))&&this.oneof.splice(i,1),t.partOf=null,this},s.prototype.onAdd=function(t){e.prototype.onAdd.call(this,t);for(var i=0;i<this.oneof.length;++i){var n=t.get(this.oneof[i]);n&&!n.partOf&&(n.partOf=this).fieldsArray.push(n)}u(this)},s.prototype.onRemove=function(t){for(var i,n=0;n<this.fieldsArray.length;++n)(i=this.fieldsArray[n]).parent&&i.parent.remove(i);e.prototype.onRemove.call(this,t)},s.d=function(){for(var n=Array(arguments.length),t=0;t<arguments.length;)n[t]=arguments[t++];return function(t,i){r.decorateType(t.constructor).add(new s(i,n)),Object.defineProperty(t,i,{get:r.oneOfGetter(n),set:r.oneOfSetter(n)})}}},{16:16,24:24,37:37}],26:[function(t,i){(i.exports=Y).filename=null,Y.defaults={keepCase:!1};var M=t(34),I=t(29),F=t(35),U=t(16),_=t(20),L=t(25),q=t(15),R=t(33),z=t(22),Z=t(36),B=t(37),P=/^[1-9][0-9]*$/,H=/^-?[1-9][0-9]*$/,X=/^0[x][0-9a-fA-F]+$/,C=/^-?0[x][0-9a-fA-F]+$/,D=/^0[0-7]+$/,J=/^-?0[0-7]+$/,W=/^(?![eE])[0-9]*(?:\.[0-9]*)?(?:[eE][+-]?[0-9]+)?$/,G=/^[a-zA-Z_][a-zA-Z_0-9]*$/,K=/^(?:\.?[a-zA-Z_][a-zA-Z_0-9]*)(?:\.[a-zA-Z_][a-zA-Z_0-9]*)*$/,Q=/^(?:\.[a-zA-Z_][a-zA-Z_0-9]*)+$/;function Y(t,i,n){i instanceof I||(n=i,i=new I),n||(n=Y.defaults);var r,e,s,u,o,f=M(t,n.alternateCommentMode||!1),a=f.next,h=f.push,c=f.peek,l=f.skip,v=f.cmnt,d=!0,p=!1,y=i,w=n.keepCase?function(t){return t}:B.camelCase;function b(t,i,n){var r=Y.filename;return n||(Y.filename=null),Error("illegal "+(i||"token")+" '"+t+"' ("+(r?r+", ":"")+"line "+f.line+")")}function m(){var t,i=[];do{if('"'!==(t=a())&&"'"!==t)throw b(t);i.push(a()),l(t),t=c()}while('"'===t||"'"===t);return i.join("")}function g(i){var n=a();switch(n){case"'":case'"':return h(n),m();case"true":case"TRUE":return!0;case"false":case"FALSE":return!1}try{return function(t,i){var n=1;"-"===t.charAt(0)&&(n=-1,t=t.substring(1));switch(t){case"inf":case"INF":case"Inf":return n*(1/0);case"nan":case"NAN":case"Nan":case"NaN":return NaN;case"0":return 0}if(P.test(t))return n*parseInt(t,10);if(X.test(t))return n*parseInt(t,16);if(D.test(t))return n*parseInt(t,8);if(W.test(t))return n*parseFloat(t);throw b(t,"number",i)}(n,!0)}catch(t){if(i&&K.test(n))return n;throw b(n,"value")}}function j(t,i){for(var n,r;!i||'"'!==(n=c())&&"'"!==n?t.push([r=k(a()),l("to",!0)?k(a()):r]):t.push(m()),l(",",!0););l(";")}function k(t,i){switch(t){case"max":case"MAX":case"Max":return *********;case"0":return 0}if(!i&&"-"===t.charAt(0))throw b(t,"id");if(H.test(t))return parseInt(t,10);if(C.test(t))return parseInt(t,16);if(J.test(t))return parseInt(t,8);throw b(t,"id")}function E(){if(r!==tt)throw b("package");if(r=a(),!K.test(r))throw b(r,"name");y=y.define(r),l(";")}function O(){var t,i=c();switch(i){case"weak":t=s||(s=[]),a();break;case"public":a();default:t=e||(e=[])}i=m(),l(";"),t.push(i)}function A(){if(l("="),u=m(),!(p="proto3"===u)&&"proto2"!==u)throw b(u,"syntax");l(";")}function x(t,i){switch(i){case"option":return N(t,i),l(";"),!0;case"message":return function(t,i){if(!G.test(i=a()))throw b(i,"type name");var n=new F(i);S(n,function(t){if(!x(n,t))switch(t){case"map":!function(t){l("<");var i=a();if(Z.mapKey[i]===tt)throw b(i,"type");l(",");var n=a();if(!K.test(n))throw b(n,"type");l(">");var r=a();if(!G.test(r))throw b(r,"name");l("=");var e=new _(w(r),k(a()),i,n);S(e,function(t){if("option"!==t)throw b(t);N(e,t),l(";")},function(){$(e)}),t.add(e)}(n);break;case"required":case"optional":case"repeated":T(n,t);break;case"oneof":!function(t,i){if(!G.test(i=a()))throw b(i,"name");var n=new L(w(i));S(n,function(t){"option"===t?(N(n,t),l(";")):(h(t),T(n,"optional"))}),t.add(n)}(n,t);break;case"extensions":j(n.extensions||(n.extensions=[]));break;case"reserved":j(n.reserved||(n.reserved=[]),!0);break;default:if(!p||!K.test(t))throw b(t);h(t),T(n,"optional")}}),t.add(n)}(t,i),!0;case"enum":return function(t,i){if(!G.test(i=a()))throw b(i,"name");var n=new q(i);S(n,function(t){switch(t){case"option":N(n,t),l(";");break;case"reserved":j(n.reserved||(n.reserved=[]),!0);break;default:!function(t,i){if(!G.test(i))throw b(i,"name");l("=");var n=k(a(),!0),r={};S(r,function(t){if("option"!==t)throw b(t);N(r,t),l(";")},function(){$(r)}),t.add(i,n,r.comment)}(n,t)}}),t.add(n)}(t,i),!0;case"service":return function(t,i){if(!G.test(i=a()))throw b(i,"service name");var n=new R(i);S(n,function(t){if(!x(n,t)){if("rpc"!==t)throw b(t);!function(t,i){var n=v(),r=i;if(!G.test(i=a()))throw b(i,"name");var e,s,u,o,f=i;l("("),l("stream",!0)&&(s=!0);if(!K.test(i=a()))throw b(i);e=i,l(")"),l("returns"),l("("),l("stream",!0)&&(o=!0);if(!K.test(i=a()))throw b(i);u=i,l(")");var h=new z(f,r,e,u,s,o);h.comment=n,S(h,function(t){if("option"!==t)throw b(t);N(h,t),l(";")}),t.add(h)}(n,t)}}),t.add(n)}(t,i),!0;case"extend":return function(i,t){if(!K.test(t=a()))throw b(t,"reference");var n=t;S(null,function(t){switch(t){case"required":case"repeated":case"optional":T(i,t,n);break;default:if(!p||!K.test(t))throw b(t);h(t),T(i,"optional",n)}})}(t,i),!0}return!1}function S(t,i,n){var r=f.line;if(t&&("string"!=typeof t.comment&&(t.comment=v()),t.filename=Y.filename),l("{",!0)){for(var e;"}"!==(e=a());)i(e);l(";",!0)}else n&&n(),l(";"),t&&"string"!=typeof t.comment&&(t.comment=v(r))}function T(t,i,n){var r=a();if("group"!==r){if(!K.test(r))throw b(r,"type");var e=a();if(!G.test(e))throw b(e,"name");e=w(e),l("=");var s=new U(e,k(a()),r,i,n);S(s,function(t){if("option"!==t)throw b(t);N(s,t),l(";")},function(){$(s)}),t.add(s),p||!s.repeated||Z.packed[r]===tt&&Z.basic[r]!==tt||s.setOption("packed",!1,!0)}else!function(t,i){var n=a();if(!G.test(n))throw b(n,"name");var r=B.lcFirst(n);n===r&&(n=B.ucFirst(n));l("=");var e=k(a()),s=new F(n);s.group=!0;var u=new U(r,e,n,i);u.filename=Y.filename,S(s,function(t){switch(t){case"option":N(s,t),l(";");break;case"required":case"optional":case"repeated":T(s,t);break;default:throw b(t)}}),t.add(s).add(u)}(t,i)}function N(t,i){var n=l("(",!0);if(!K.test(i=a()))throw b(i,"name");var r=i;n&&(l(")"),r="("+r+")",i=c(),Q.test(i)&&(r+=i,a())),l("="),function t(i,n){if(l("{",!0))do{if(!G.test(o=a()))throw b(o,"name");"{"===c()?t(i,n+"."+o):(l(":"),"{"===c()?t(i,n+"."+o):V(i,n+"."+o,g(!0))),l(",",!0)}while(!l("}",!0));else V(i,n,g(!0))}(t,r)}function V(t,i,n){t.setOption&&t.setOption(i,n)}function $(t){if(l("[",!0)){for(;N(t,"option"),l(",",!0););l("]")}return t}for(;null!==(o=a());)switch(o){case"package":if(!d)throw b(o);E();break;case"import":if(!d)throw b(o);O();break;case"syntax":if(!d)throw b(o);A();break;case"option":N(y,o),l(";");break;default:if(x(y,o)){d=!1;continue}throw b(o)}return Y.filename=null,{package:r,imports:e,weakImports:s,syntax:u,root:i}}},{15:15,16:16,20:20,22:22,25:25,29:29,33:33,34:34,35:35,36:36,37:37}],27:[function(t,i){i.exports=o;var n,r=t(39),e=r.LongBits,s=r.utf8;function u(t,i){return RangeError("index out of range: "+t.pos+" + "+(i||1)+" > "+t.len)}function o(t){this.buf=t,this.pos=0,this.len=t.length}var f,h="undefined"!=typeof Uint8Array?function(t){if(t instanceof Uint8Array||Array.isArray(t))return new o(t);throw Error("illegal buffer")}:function(t){if(Array.isArray(t))return new o(t);throw Error("illegal buffer")};function a(){var t=new e(0,0),i=0;if(!(4<this.len-this.pos)){for(;i<3;++i){if(this.pos>=this.len)throw u(this);if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*i)>>>0,this.buf[this.pos++]<128)return t}return t.lo=(t.lo|(127&this.buf[this.pos++])<<7*i)>>>0,t}for(;i<4;++i)if(t.lo=(t.lo|(127&this.buf[this.pos])<<7*i)>>>0,this.buf[this.pos++]<128)return t;if(t.lo=(t.lo|(127&this.buf[this.pos])<<28)>>>0,t.hi=(t.hi|(127&this.buf[this.pos])>>4)>>>0,this.buf[this.pos++]<128)return t;if(i=0,4<this.len-this.pos){for(;i<5;++i)if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*i+3)>>>0,this.buf[this.pos++]<128)return t}else for(;i<5;++i){if(this.pos>=this.len)throw u(this);if(t.hi=(t.hi|(127&this.buf[this.pos])<<7*i+3)>>>0,this.buf[this.pos++]<128)return t}throw Error("invalid varint encoding")}function c(t,i){return(t[i-4]|t[i-3]<<8|t[i-2]<<16|t[i-1]<<24)>>>0}function l(){if(this.pos+8>this.len)throw u(this,8);return new e(c(this.buf,this.pos+=4),c(this.buf,this.pos+=4))}o.create=r.Buffer?function(t){return(o.create=function(t){return r.Buffer.isBuffer(t)?new n(t):h(t)})(t)}:h,o.prototype.c=r.Array.prototype.subarray||r.Array.prototype.slice,o.prototype.uint32=(f=4294967295,function(){if(f=(127&this.buf[this.pos])>>>0,this.buf[this.pos++]<128)return f;if(f=(f|(127&this.buf[this.pos])<<7)>>>0,this.buf[this.pos++]<128)return f;if(f=(f|(127&this.buf[this.pos])<<14)>>>0,this.buf[this.pos++]<128)return f;if(f=(f|(127&this.buf[this.pos])<<21)>>>0,this.buf[this.pos++]<128)return f;if(f=(f|(15&this.buf[this.pos])<<28)>>>0,this.buf[this.pos++]<128)return f;if((this.pos+=5)>this.len)throw this.pos=this.len,u(this,10);return f}),o.prototype.int32=function(){return 0|this.uint32()},o.prototype.sint32=function(){var t=this.uint32();return t>>>1^-(1&t)|0},o.prototype.bool=function(){return 0!==this.uint32()},o.prototype.fixed32=function(){if(this.pos+4>this.len)throw u(this,4);return c(this.buf,this.pos+=4)},o.prototype.sfixed32=function(){if(this.pos+4>this.len)throw u(this,4);return 0|c(this.buf,this.pos+=4)},o.prototype.float=function(){if(this.pos+4>this.len)throw u(this,4);var t=r.float.readFloatLE(this.buf,this.pos);return this.pos+=4,t},o.prototype.double=function(){if(this.pos+8>this.len)throw u(this,4);var t=r.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,t},o.prototype.bytes=function(){var t=this.uint32(),i=this.pos,n=this.pos+t;if(n>this.len)throw u(this,t);return this.pos+=t,Array.isArray(this.buf)?this.buf.slice(i,n):i===n?new this.buf.constructor(0):this.c.call(this.buf,i,n)},o.prototype.string=function(){var t=this.bytes();return s.read(t,0,t.length)},o.prototype.skip=function(t){if("number"==typeof t){if(this.pos+t>this.len)throw u(this,t);this.pos+=t}else do{if(this.pos>=this.len)throw u(this)}while(128&this.buf[this.pos++]);return this},o.prototype.skipType=function(t){switch(t){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:for(;4!=(t=7&this.uint32());)this.skipType(t);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+t+" at offset "+this.pos)}return this},o.o=function(t){n=t;var i=r.Long?"toLong":"toNumber";r.merge(o.prototype,{int64:function(){return a.call(this)[i](!1)},uint64:function(){return a.call(this)[i](!0)},sint64:function(){return a.call(this).zzDecode()[i](!1)},fixed64:function(){return l.call(this)[i](!0)},sfixed64:function(){return l.call(this)[i](!1)}})}},{39:39}],28:[function(t,i){i.exports=e;var n=t(27);(e.prototype=Object.create(n.prototype)).constructor=e;var r=t(39);function e(t){n.call(this,t)}r.Buffer&&(e.prototype.c=r.Buffer.prototype.slice),e.prototype.string=function(){var t=this.uint32();return this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+t,this.len))}},{27:27,39:39}],29:[function(t,i){i.exports=n;var r=t(23);((n.prototype=Object.create(r.prototype)).constructor=n).className="Root";var e,v,d,s=t(16),u=t(15),o=t(25),p=t(37);function n(t){r.call(this,"",t),this.deferred=[],this.files=[]}function y(){}n.fromJSON=function(t,i){return i||(i=new n),t.options&&i.setOptions(t.options),i.addJSON(t.nested)},n.prototype.resolvePath=p.path.resolve,n.prototype.load=function t(i,s,e){"function"==typeof s&&(e=s,s=tt);var u=this;if(!e)return p.asPromise(t,u,i,s);var o=e===y;function f(t,i){if(e){var n=e;if(e=null,o)throw t;n(t,i)}}function h(t){var i=t.lastIndexOf("google/protobuf/");if(-1<i){var n=t.substring(i);if(n in d)return n}return null}function a(t,i){try{if(p.isString(i)&&"{"===i.charAt(0)&&(i=JSON.parse(i)),p.isString(i)){v.filename=t;var n,r=v(i,u,s),e=0;if(r.imports)for(;e<r.imports.length;++e)(n=h(r.imports[e])||u.resolvePath(t,r.imports[e]))&&c(n);if(r.weakImports)for(e=0;e<r.weakImports.length;++e)(n=h(r.weakImports[e])||u.resolvePath(t,r.weakImports[e]))&&c(n,!0)}else u.setOptions(i.options).addJSON(i.nested)}catch(t){f(t)}o||l||f(null,u)}function c(n,r){if(!(-1<u.files.indexOf(n)))if(u.files.push(n),n in d)o?a(n,d[n]):(++l,setTimeout(function(){--l,a(n,d[n])}));else if(o){var t;try{t=p.fs.readFileSync(n).toString("utf8")}catch(t){return void(r||f(t))}a(n,t)}else++l,p.fetch(n,function(t,i){--l,e&&(t?r?l||f(null,u):f(t):a(n,i))})}var l=0;p.isString(i)&&(i=[i]);for(var n,r=0;r<i.length;++r)(n=u.resolvePath("",i[r]))&&c(n);return o?u:(l||f(null,u),tt)},n.prototype.loadSync=function(t,i){if(!p.isNode)throw Error("not supported");return this.load(t,i,y)},n.prototype.resolveAll=function(){if(this.deferred.length)throw Error("unresolvable extensions: "+this.deferred.map(function(t){return"'extend "+t.extend+"' in "+t.parent.fullName}).join(", "));return r.prototype.resolveAll.call(this)};var f=/^[A-Z]/;function h(t,i){var n=i.parent.lookup(i.extend);if(n){var r=new s(i.fullName,i.id,i.type,i.rule,tt,i.options);return(r.declaringField=i).extensionField=r,n.add(r),!0}return!1}n.prototype.h=function(t){if(t instanceof s)t.extend===tt||t.extensionField||h(0,t)||this.deferred.push(t);else if(t instanceof u)f.test(t.name)&&(t.parent[t.name]=t.values);else if(!(t instanceof o)){if(t instanceof e)for(var i=0;i<this.deferred.length;)h(0,this.deferred[i])?this.deferred.splice(i,1):++i;for(var n=0;n<t.nestedArray.length;++n)this.h(t.f[n]);f.test(t.name)&&(t.parent[t.name]=t)}},n.prototype.a=function(t){if(t instanceof s){if(t.extend!==tt)if(t.extensionField)t.extensionField.parent.remove(t.extensionField),t.extensionField=null;else{var i=this.deferred.indexOf(t);-1<i&&this.deferred.splice(i,1)}}else if(t instanceof u)f.test(t.name)&&delete t.parent[t.name];else if(t instanceof r){for(var n=0;n<t.nestedArray.length;++n)this.a(t.f[n]);f.test(t.name)&&delete t.parent[t.name]}},n.o=function(t,i,n){e=t,v=i,d=n}},{15:15,16:16,23:23,25:25,37:37}],30:[function(t,i){i.exports={}},{}],31:[function(t,i,n){n.Service=t(32)},{32:32}],32:[function(t,i){i.exports=n;var o=t(39);function n(t,i,n){if("function"!=typeof t)throw TypeError("rpcImpl must be a function");o.EventEmitter.call(this),this.rpcImpl=t,this.requestDelimited=!!i,this.responseDelimited=!!n}((n.prototype=Object.create(o.EventEmitter.prototype)).constructor=n).prototype.rpcCall=function t(n,i,r,e,s){if(!e)throw TypeError("request must be specified");var u=this;if(!s)return o.asPromise(t,u,n,i,r,e);if(!u.rpcImpl)return setTimeout(function(){s(Error("already ended"))},0),tt;try{return u.rpcImpl(n,i[u.requestDelimited?"encodeDelimited":"encode"](e).finish(),function(t,i){if(t)return u.emit("error",t,n),s(t);if(null===i)return u.end(!0),tt;if(!(i instanceof r))try{i=r[u.responseDelimited?"decodeDelimited":"decode"](i)}catch(t){return u.emit("error",t,n),s(t)}return u.emit("data",i,n),s(null,i)})}catch(t){return u.emit("error",t,n),setTimeout(function(){s(t)},0),tt}},n.prototype.end=function(t){return this.rpcImpl&&(t||this.rpcImpl(null,null,null),this.rpcImpl=null,this.emit("end").off()),this}},{39:39}],33:[function(t,i){i.exports=u;var r=t(23);((u.prototype=Object.create(r.prototype)).constructor=u).className="Service";var s=t(22),o=t(37),f=t(31);function u(t,i){r.call(this,t,i),this.methods={},this.l=null}function n(t){return t.l=null,t}u.fromJSON=function(t,i){var n=new u(t,i.options);if(i.methods)for(var r=Object.keys(i.methods),e=0;e<r.length;++e)n.add(s.fromJSON(r[e],i.methods[r[e]]));return i.nested&&n.addJSON(i.nested),n.comment=i.comment,n},u.prototype.toJSON=function(t){var i=r.prototype.toJSON.call(this,t),n=!!t&&!!t.keepComments;return o.toObject(["options",i&&i.options||tt,"methods",r.arrayToJSON(this.methodsArray,t)||{},"nested",i&&i.nested||tt,"comment",n?this.comment:tt])},Object.defineProperty(u.prototype,"methodsArray",{get:function(){return this.l||(this.l=o.toArray(this.methods))}}),u.prototype.get=function(t){return this.methods[t]||r.prototype.get.call(this,t)},u.prototype.resolveAll=function(){for(var t=this.methodsArray,i=0;i<t.length;++i)t[i].resolve();return r.prototype.resolve.call(this)},u.prototype.add=function(t){if(this.get(t.name))throw Error("duplicate name '"+t.name+"' in "+this);return t instanceof s?n((this.methods[t.name]=t).parent=this):r.prototype.add.call(this,t)},u.prototype.remove=function(t){if(t instanceof s){if(this.methods[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.methods[t.name],t.parent=null,n(this)}return r.prototype.remove.call(this,t)},u.prototype.create=function(t,i,n){for(var r,e=new f.Service(t,i,n),s=0;s<this.methodsArray.length;++s){var u=o.lcFirst((r=this.l[s]).resolve().name).replace(/[^$\w_]/g,"");e[u]=o.codegen(["r","c"],o.isReserved(u)?u+"_":u)("return this.rpcCall(m,q,s,r,c)")({m:r,q:r.resolvedRequestType.ctor,s:r.resolvedResponseType.ctor})}return e}},{22:22,23:23,31:31,37:37}],34:[function(t,i){i.exports=e;var E=/[\s{}=;:[\],'"()<>]/g,O=/(?:"([^"\\]*(?:\\.[^"\\]*)*)")/g,A=/(?:'([^'\\]*(?:\\.[^'\\]*)*)')/g,x=/^ *[*/]+ */,S=/^\s*\*?\/*/,T=/\n/g,N=/\s/,n=/\\(.?)/g,r={0:"\0",r:"\r",n:"\n",t:"\t"};function V(t){return t.replace(n,function(t,i){switch(i){case"\\":case"":return i;default:return r[i]||""}})}function e(o,f){o=o.toString();var h=0,a=o.length,c=1,u=null,l=null,v=0,d=!1,p=[],y=null;function w(t){return Error("illegal "+t+" (line "+c+")")}function b(t){return o.charAt(t)}function m(t,i){u=o.charAt(t++),v=c,d=!1;var n,r=t-(f?2:3);do{if(--r<0||"\n"===(n=o.charAt(r))){d=!0;break}}while(" "===n||"\t"===n);for(var e=o.substring(t,i).split(T),s=0;s<e.length;++s)e[s]=e[s].replace(f?S:x,"").trim();l=e.join("\n").trim()}function g(t){var i=j(t),n=o.substring(t,i);return/^\s*\/{1,2}/.test(n)}function j(t){for(var i=t;i<a&&"\n"!==b(i);)i++;return i}function r(){if(0<p.length)return p.shift();if(y)return function(){var t="'"===y?A:O;t.lastIndex=h-1;var i=t.exec(o);if(!i)throw w("string");return h=t.lastIndex,k(y),y=null,V(i[1])}();var t,i,n,r,e;do{if(h===a)return null;for(t=!1;N.test(n=b(h));)if("\n"===n&&++c,++h===a)return null;if("/"===b(h)){if(++h===a)throw w("comment");if("/"===b(h))if(f){if(e=!1,g(r=h)){e=!0;do{if((h=j(h))===a)break;h++}while(g(h))}else h=Math.min(a,j(h)+1);e&&m(r,h),c++,t=!0}else{for(e="/"===b(r=h+1);"\n"!==b(++h);)if(h===a)return null;++h,e&&m(r,h-1),++c,t=!0}else{if("*"!==(n=b(h)))return"/";r=h+1,e=f||"*"===b(r);do{if("\n"===n&&++c,++h===a)throw w("comment");i=n,n=b(h)}while("*"!==i||"/"!==n);++h,e&&m(r,h-2),t=!0}}}while(t);var s=h;if(E.lastIndex=0,!E.test(b(s++)))for(;s<a&&!E.test(b(s));)++s;var u=o.substring(h,h=s);return'"'!==u&&"'"!==u||(y=u),u}function k(t){p.push(t)}function e(){if(!p.length){var t=r();if(null===t)return null;k(t)}return p[0]}return Object.defineProperty({next:r,peek:e,push:k,skip:function(t,i){var n=e();if(n===t)return r(),!0;if(!i)throw w("token '"+n+"', '"+t+"' expected");return!1},cmnt:function(t){var i=null;return t===tt?v===c-1&&(f||"*"===u||d)&&(i=l):(v<t&&e(),v!==t||d||!f&&"/"!==u||(i=l)),i}},"line",{get:function(){return c}})}e.unescape=V},{}],35:[function(t,i){i.exports=m;var u=t(23);((m.prototype=Object.create(u.prototype)).constructor=m).className="Type";var o=t(15),f=t(25),h=t(16),a=t(20),c=t(33),e=t(21),s=t(27),l=t(42),v=t(37),d=t(14),p=t(13),y=t(40),w=t(12),b=t(41);function m(t,i){u.call(this,t,i),this.fields={},this.oneofs=tt,this.extensions=tt,this.reserved=tt,this.group=tt,this.v=null,this.e=null,this.p=null,this.y=null}function n(t){return t.v=t.e=t.p=null,delete t.encode,delete t.decode,delete t.verify,t}Object.defineProperties(m.prototype,{fieldsById:{get:function(){if(this.v)return this.v;this.v={};for(var t=Object.keys(this.fields),i=0;i<t.length;++i){var n=this.fields[t[i]],r=n.id;if(this.v[r])throw Error("duplicate id "+r+" in "+this);this.v[r]=n}return this.v}},fieldsArray:{get:function(){return this.e||(this.e=v.toArray(this.fields))}},oneofsArray:{get:function(){return this.p||(this.p=v.toArray(this.oneofs))}},ctor:{get:function(){return this.y||(this.ctor=m.generateConstructor(this)())},set:function(t){var i=t.prototype;i instanceof e||((t.prototype=new e).constructor=t,v.merge(t.prototype,i)),t.$type=t.prototype.$type=this,v.merge(t,e,!0),this.y=t;for(var n=0;n<this.fieldsArray.length;++n)this.e[n].resolve();var r={};for(n=0;n<this.oneofsArray.length;++n)r[this.p[n].resolve().name]={get:v.oneOfGetter(this.p[n].oneof),set:v.oneOfSetter(this.p[n].oneof)};n&&Object.defineProperties(t.prototype,r)}}}),m.generateConstructor=function(t){for(var i,n=v.codegen(["p"],t.name),r=0;r<t.fieldsArray.length;++r)(i=t.e[r]).map?n("this%s={}",v.safeProp(i.name)):i.repeated&&n("this%s=[]",v.safeProp(i.name));return n("if(p)for(var ks=Object.keys(p),i=0;i<ks.length;++i)if(p[ks[i]]!=null)")("this[ks[i]]=p[ks[i]]")},m.fromJSON=function(t,i){var n=new m(t,i.options);n.extensions=i.extensions,n.reserved=i.reserved;for(var r=Object.keys(i.fields),e=0;e<r.length;++e)n.add((void 0!==i.fields[r[e]].keyType?a.fromJSON:h.fromJSON)(r[e],i.fields[r[e]]));if(i.oneofs)for(r=Object.keys(i.oneofs),e=0;e<r.length;++e)n.add(f.fromJSON(r[e],i.oneofs[r[e]]));if(i.nested)for(r=Object.keys(i.nested),e=0;e<r.length;++e){var s=i.nested[r[e]];n.add((s.id!==tt?h.fromJSON:s.fields!==tt?m.fromJSON:s.values!==tt?o.fromJSON:s.methods!==tt?c.fromJSON:u.fromJSON)(r[e],s))}return i.extensions&&i.extensions.length&&(n.extensions=i.extensions),i.reserved&&i.reserved.length&&(n.reserved=i.reserved),i.group&&(n.group=!0),i.comment&&(n.comment=i.comment),n},m.prototype.toJSON=function(t){var i=u.prototype.toJSON.call(this,t),n=!!t&&!!t.keepComments;return v.toObject(["options",i&&i.options||tt,"oneofs",u.arrayToJSON(this.oneofsArray,t),"fields",u.arrayToJSON(this.fieldsArray.filter(function(t){return!t.declaringField}),t)||{},"extensions",this.extensions&&this.extensions.length?this.extensions:tt,"reserved",this.reserved&&this.reserved.length?this.reserved:tt,"group",this.group||tt,"nested",i&&i.nested||tt,"comment",n?this.comment:tt])},m.prototype.resolveAll=function(){for(var t=this.fieldsArray,i=0;i<t.length;)t[i++].resolve();var n=this.oneofsArray;for(i=0;i<n.length;)n[i++].resolve();return u.prototype.resolveAll.call(this)},m.prototype.get=function(t){return this.fields[t]||this.oneofs&&this.oneofs[t]||this.nested&&this.nested[t]||null},m.prototype.add=function(t){if(this.get(t.name))throw Error("duplicate name '"+t.name+"' in "+this);if(t instanceof h&&t.extend===tt){if(this.v?this.v[t.id]:this.fieldsById[t.id])throw Error("duplicate id "+t.id+" in "+this);if(this.isReservedId(t.id))throw Error("id "+t.id+" is reserved in "+this);if(this.isReservedName(t.name))throw Error("name '"+t.name+"' is reserved in "+this);return t.parent&&t.parent.remove(t),(this.fields[t.name]=t).message=this,t.onAdd(this),n(this)}return t instanceof f?(this.oneofs||(this.oneofs={}),(this.oneofs[t.name]=t).onAdd(this),n(this)):u.prototype.add.call(this,t)},m.prototype.remove=function(t){if(t instanceof h&&t.extend===tt){if(!this.fields||this.fields[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.fields[t.name],t.parent=null,t.onRemove(this),n(this)}if(t instanceof f){if(!this.oneofs||this.oneofs[t.name]!==t)throw Error(t+" is not a member of "+this);return delete this.oneofs[t.name],t.parent=null,t.onRemove(this),n(this)}return u.prototype.remove.call(this,t)},m.prototype.isReservedId=function(t){return u.isReservedId(this.reserved,t)},m.prototype.isReservedName=function(t){return u.isReservedName(this.reserved,t)},m.prototype.create=function(t){return new this.ctor(t)},m.prototype.setup=function(){for(var t=this.fullName,i=[],n=0;n<this.fieldsArray.length;++n)i.push(this.e[n].resolve().resolvedType);this.encode=d(this)({Writer:l,types:i,util:v}),this.decode=p(this)({Reader:s,types:i,util:v}),this.verify=y(this)({types:i,util:v}),this.fromObject=w.fromObject(this)({types:i,util:v}),this.toObject=w.toObject(this)({types:i,util:v});var r=b[t];if(r){var e=Object.create(this);e.fromObject=this.fromObject,this.fromObject=r.fromObject.bind(e),e.toObject=this.toObject,this.toObject=r.toObject.bind(e)}return this},m.prototype.encode=function(t,i){return this.setup().encode(t,i)},m.prototype.encodeDelimited=function(t,i){return this.encode(t,i&&i.len?i.fork():i).ldelim()},m.prototype.decode=function(t,i){return this.setup().decode(t,i)},m.prototype.decodeDelimited=function(t){return t instanceof s||(t=s.create(t)),this.decode(t,t.uint32())},m.prototype.verify=function(t){return this.setup().verify(t)},m.prototype.fromObject=function(t){return this.setup().fromObject(t)},m.prototype.toObject=function(t,i){return this.setup().toObject(t,i)},m.d=function(i){return function(t){v.decorateType(t,i)}}},{12:12,13:13,14:14,15:15,16:16,20:20,21:21,23:23,25:25,27:27,33:33,37:37,40:40,41:41,42:42}],36:[function(t,i,n){var r=n,e=t(37),s=["double","float","int32","uint32","sint32","fixed32","sfixed32","int64","uint64","sint64","fixed64","sfixed64","bool","string","bytes"];function u(t,i){var n=0,r={};for(i|=0;n<t.length;)r[s[n+i]]=t[n++];return r}r.basic=u([1,5,0,0,0,5,5,0,0,0,1,1,0,2,2]),r.defaults=u([0,0,0,0,0,0,0,0,0,0,0,0,!1,"",e.emptyArray,null]),r.long=u([0,0,0,1,1],7),r.mapKey=u([0,0,0,5,5,0,0,0,1,1,0,2],2),r.packed=u([1,5,0,0,0,5,5,0,0,0,1,1,0])},{37:37}],37:[function(r,t){var e,n,s=t.exports=r(39),i=r(30);s.codegen=r(3),s.fetch=r(5),s.path=r(8),s.fs=s.inquire("fs"),s.toArray=function(t){if(t){for(var i=Object.keys(t),n=Array(i.length),r=0;r<i.length;)n[r]=t[i[r++]];return n}return[]},s.toObject=function(t){for(var i={},n=0;n<t.length;){var r=t[n++],e=t[n++];e!==tt&&(i[r]=e)}return i};var u=/\\/g,o=/"/g;s.isReserved=function(t){return/^(?:do|if|in|for|let|new|try|var|case|else|enum|eval|false|null|this|true|void|with|break|catch|class|const|super|throw|while|yield|delete|export|import|public|return|static|switch|typeof|default|extends|finally|package|private|continue|debugger|function|arguments|interface|protected|implements|instanceof)$/.test(t)},s.safeProp=function(t){return!/^[$\w_]+$/.test(t)||s.isReserved(t)?'["'+t.replace(u,"\\\\").replace(o,'\\"')+'"]':"."+t},s.ucFirst=function(t){return t.charAt(0).toUpperCase()+t.substring(1)};var f=/_([a-z])/g;s.camelCase=function(t){return t.substring(0,1)+t.substring(1).replace(f,function(t,i){return i.toUpperCase()})},s.compareFieldsById=function(t,i){return t.id-i.id},s.decorateType=function(t,i){if(t.$type)return i&&t.$type.name!==i&&(s.decorateRoot.remove(t.$type),t.$type.name=i,s.decorateRoot.add(t.$type)),t.$type;e||(e=r(35));var n=new e(i||t.name);return s.decorateRoot.add(n),n.ctor=t,Object.defineProperty(t,"$type",{value:n,enumerable:!1}),Object.defineProperty(t.prototype,"$type",{value:n,enumerable:!1}),n};var h=0;s.decorateEnum=function(t){if(t.$type)return t.$type;n||(n=r(15));var i=new n("Enum"+h++,t);return s.decorateRoot.add(i),Object.defineProperty(t,"$type",{value:i,enumerable:!1}),i},Object.defineProperty(s,"decorateRoot",{get:function(){return i.decorated||(i.decorated=new(r(29)))}})},{15:15,29:29,3:3,30:30,35:35,39:39,5:5,8:8}],38:[function(t,i){i.exports=e;var n=t(39);function e(t,i){this.lo=t>>>0,this.hi=i>>>0}var s=e.zero=new e(0,0);s.toNumber=function(){return 0},s.zzEncode=s.zzDecode=function(){return this},s.length=function(){return 1};var r=e.zeroHash="\0\0\0\0\0\0\0\0";e.fromNumber=function(t){if(0===t)return s;var i=t<0;i&&(t=-t);var n=t>>>0,r=(t-n)/4294967296>>>0;return i&&(r=~r>>>0,n=~n>>>0,4294967295<++n&&(n=0,4294967295<++r&&(r=0))),new e(n,r)},e.from=function(t){if("number"==typeof t)return e.fromNumber(t);if(n.isString(t)){if(!n.Long)return e.fromNumber(parseInt(t,10));t=n.Long.fromString(t)}return t.low||t.high?new e(t.low>>>0,t.high>>>0):s},e.prototype.toNumber=function(t){if(!t&&this.hi>>>31){var i=1+~this.lo>>>0,n=~this.hi>>>0;return i||(n=n+1>>>0),-(i+4294967296*n)}return this.lo+4294967296*this.hi},e.prototype.toLong=function(t){return n.Long?new n.Long(0|this.lo,0|this.hi,!!t):{low:0|this.lo,high:0|this.hi,unsigned:!!t}};var u=String.prototype.charCodeAt;e.fromHash=function(t){return t===r?s:new e((u.call(t,0)|u.call(t,1)<<8|u.call(t,2)<<16|u.call(t,3)<<24)>>>0,(u.call(t,4)|u.call(t,5)<<8|u.call(t,6)<<16|u.call(t,7)<<24)>>>0)},e.prototype.toHash=function(){return String.fromCharCode(255&this.lo,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,255&this.hi,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)},e.prototype.zzEncode=function(){var t=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^t)>>>0,this.lo=(this.lo<<1^t)>>>0,this},e.prototype.zzDecode=function(){var t=-(1&this.lo);return this.lo=((this.lo>>>1|this.hi<<31)^t)>>>0,this.hi=(this.hi>>>1^t)>>>0,this},e.prototype.length=function(){var t=this.lo,i=(this.lo>>>28|this.hi<<4)>>>0,n=this.hi>>>24;return 0===n?0===i?t<16384?t<128?1:2:t<2097152?3:4:i<16384?i<128?5:6:i<2097152?7:8:n<128?9:10}},{39:39}],39:[function(t,i,n){var r=n;function e(t,i,n){for(var r=Object.keys(i),e=0;e<r.length;++e)t[r[e]]!==tt&&n||(t[r[e]]=i[r[e]]);return t}function s(t){function n(t,i){if(!(this instanceof n))return new n(t,i);Object.defineProperty(this,"message",{get:function(){return t}}),Error.captureStackTrace?Error.captureStackTrace(this,n):Object.defineProperty(this,"stack",{value:Error().stack||""}),i&&e(this,i)}return(n.prototype=Object.create(Error.prototype)).constructor=n,Object.defineProperty(n.prototype,"name",{get:function(){return t}}),n.prototype.toString=function(){return this.name+": "+this.message},n}r.asPromise=t(1),r.base64=t(2),r.EventEmitter=t(4),r.float=t(6),r.inquire=t(7),r.utf8=t(10),r.pool=t(9),r.LongBits=t(38),r.global="undefined"!=typeof window&&window||"undefined"!=typeof global&&global||"undefined"!=typeof self&&self||this,r.emptyArray=Object.freeze?Object.freeze([]):[],r.emptyObject=Object.freeze?Object.freeze({}):{},r.isNode=!!(r.global.process&&r.global.process.versions&&r.global.process.versions.node),r.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t},r.isString=function(t){return"string"==typeof t||t instanceof String},r.isObject=function(t){return t&&"object"==typeof t},r.isset=r.isSet=function(t,i){var n=t[i];return!(null==n||!t.hasOwnProperty(i))&&("object"!=typeof n||0<(Array.isArray(n)?n.length:Object.keys(n).length))},r.Buffer=function(){try{var t=r.inquire("buffer").Buffer;return t.prototype.utf8Write?t:null}catch(t){return null}}(),r.w=null,r.b=null,r.newBuffer=function(t){return"number"==typeof t?r.Buffer?r.b(t):new r.Array(t):r.Buffer?r.w(t):"undefined"==typeof Uint8Array?t:new Uint8Array(t)},r.Array="undefined"!=typeof Uint8Array?Uint8Array:Array,r.Long="undefined"==typeof process||process.env.ENABLE_LONG?r.global.dcodeIO&&r.global.dcodeIO.Long||r.global.Long||r.inquire("long"):tt,r.key2Re=/^true|false|0|1$/,r.key32Re=/^-?(?:0|[1-9][0-9]*)$/,r.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/,r.longToHash=function(t){return t?r.LongBits.from(t).toHash():r.LongBits.zeroHash},r.longFromHash=function(t,i){var n=r.LongBits.fromHash(t);return r.Long?r.Long.fromBits(n.lo,n.hi,i):n.toNumber(!!i)},r.merge=e,r.lcFirst=function(t){return t.charAt(0).toLowerCase()+t.substring(1)},r.newError=s,r.ProtocolError=s("ProtocolError"),r.oneOfGetter=function(t){for(var n={},i=0;i<t.length;++i)n[t[i]]=1;return function(){for(var t=Object.keys(this),i=t.length-1;-1<i;--i)if(1===n[t[i]]&&this[t[i]]!==tt&&null!==this[t[i]])return t[i]}},r.oneOfSetter=function(n){return function(t){for(var i=0;i<n.length;++i)n[i]!==t&&delete this[n[i]]}},r.toJSONOptions={longs:String,enums:String,bytes:String,json:!0},r.o=function(){var n=r.Buffer;n?(r.w=n.from!==Uint8Array.from&&n.from||function(t,i){return new n(t,i)},r.b=n.allocUnsafe||function(t){return new n(t)}):r.w=r.b=null}},{1:1,10:10,2:2,38:38,4:4,6:6,7:7,9:9}],40:[function(t,i){i.exports=function(t){var i=h.codegen(["m"],t.name+"$verify")('if(typeof m!=="object"||m===null)')("return%j","object expected"),n=t.oneofsArray,r={};n.length&&i("var p={}");for(var e=0;e<t.fieldsArray.length;++e){var s=t.e[e].resolve(),u="m"+h.safeProp(s.name);if(s.optional&&i("if(%s!=null&&m.hasOwnProperty(%j)){",u,s.name),s.map)i("if(!util.isObject(%s))",u)("return%j",a(s,"object"))("var k=Object.keys(%s)",u)("for(var i=0;i<k.length;++i){"),l(i,s,"k[i]"),c(i,s,e,u+"[k[i]]")("}");else if(s.repeated){var o=u;s.useToArray()&&(o="array"+s.id,i("var %s",o),i("if (%s!=null&&%s.toArray) { %s = %s.toArray() } else { %s = %s }",u,u,o,u,o,u)),i("if(!Array.isArray(%s))",o)("return%j",a(s,"array"))("for(var i=0;i<%s.length;++i){",o),s.preEncoded()&&i("if (!(%s instanceof Uint8Array)) {",o+"[i]"),c(i,s,e,o+"[i]"),s.preEncoded()&&i("}"),i("}")}else{if(s.partOf){var f=h.safeProp(s.partOf.name);1===r[s.partOf.name]&&i("if(p%s===1)",f)("return%j",s.partOf.name+": multiple values"),r[s.partOf.name]=1,i("p%s=1",f)}c(i,s,e,u)}s.optional&&i("}")}return i("return null")};var u=t(15),h=t(37);function a(t,i){return t.name+": "+i+(t.repeated&&"array"!==i?"[]":t.map&&"object"!==i?"{k:"+t.keyType+"}":"")+" expected"}function c(t,i,n,r){if(i.resolvedType)if(i.resolvedType instanceof u){t("switch(%s){",r)("default:")("return%j",a(i,"enum value"));for(var e=Object.keys(i.resolvedType.values),s=0;s<e.length;++s)t("case %i:",i.resolvedType.values[e[s]]);t("break")("}")}else t("{")("var e=types[%i].verify(%s);",n,r)("if(e)")("return%j+e",i.name+".")("}");else switch(i.type){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":t("if(!util.isInteger(%s))",r)("return%j",a(i,"integer"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":t("if(!util.isInteger(%s)&&!(%s&&util.isInteger(%s.low)&&util.isInteger(%s.high)))",r,r,r,r)("return%j",a(i,"integer|Long"));break;case"float":case"double":t('if(typeof %s!=="number")',r)("return%j",a(i,"number"));break;case"bool":t('if(typeof %s!=="boolean")',r)("return%j",a(i,"boolean"));break;case"string":t("if(!util.isString(%s))",r)("return%j",a(i,"string"));break;case"bytes":t('if(!(%s&&typeof %s.length==="number"||util.isString(%s)))',r,r,r)("return%j",a(i,"buffer"))}return t}function l(t,i,n){switch(i.keyType){case"int32":case"uint32":case"sint32":case"fixed32":case"sfixed32":t("if(!util.key32Re.test(%s))",n)("return%j",a(i,"integer key"));break;case"int64":case"uint64":case"sint64":case"fixed64":case"sfixed64":t("if(!util.key64Re.test(%s))",n)("return%j",a(i,"integer|Long key"));break;case"bool":t("if(!util.key2Re.test(%s))",n)("return%j",a(i,"boolean key"))}return t}},{15:15,37:37}],41:[function(t,i,n){var r=n,s=t(21);r[".google.protobuf.Any"]={fromObject:function(t){if(t&&t["@type"]){var i=this.lookup(t["@type"]);if(i){var n="."===t["@type"].charAt(0)?t["@type"].substr(1):t["@type"];return this.create({type_url:"/"+n,value:i.encode(i.fromObject(t)).finish()})}}return this.fromObject(t)},toObject:function(t,i){if(i&&i.json&&t.type_url&&t.value){var n=t.type_url.substring(t.type_url.lastIndexOf("/")+1),r=this.lookup(n);r&&(t=r.decode(t.value))}if(!(t instanceof this.ctor)&&t instanceof s){var e=t.$type.toObject(t,i);return e["@type"]=t.$type.fullName,e}return this.toObject(t,i)}}},{21:21}],42:[function(t,i){i.exports=a;var n,r=t(39),e=r.LongBits,s=r.base64,u=r.utf8;function o(t,i,n){this.fn=t,this.len=i,this.next=tt,this.val=n}function f(){}function h(t){this.head=t.head,this.tail=t.tail,this.len=t.len,this.next=t.states}function a(){this.len=0,this.head=new o(f,0,0),this.tail=this.head,this.states=null}function c(t,i,n){i[n]=255&t}function l(t,i){this.len=t,this.next=tt,this.val=i}function v(t,i,n){for(;t.hi;)i[n++]=127&t.lo|128,t.lo=(t.lo>>>7|t.hi<<25)>>>0,t.hi>>>=7;for(;127<t.lo;)i[n++]=127&t.lo|128,t.lo=t.lo>>>7;i[n++]=t.lo}function d(t,i,n){i[n]=255&t,i[n+1]=t>>>8&255,i[n+2]=t>>>16&255,i[n+3]=t>>>24}a.create=r.Buffer?function(){return(a.create=function(){return new n})()}:function(){return new a},a.alloc=function(t){return new r.Array(t)},r.Array!==Array&&(a.alloc=r.pool(a.alloc,r.Array.prototype.subarray)),a.prototype.g=function(t,i,n){return this.tail=this.tail.next=new o(t,i,n),this.len+=i,this},(l.prototype=Object.create(o.prototype)).fn=function(t,i,n){for(;127<t;)i[n++]=127&t|128,t>>>=7;i[n]=t},a.prototype.uint32=function(t){return this.len+=(this.tail=this.tail.next=new l((t>>>=0)<128?1:t<16384?2:t<2097152?3:t<268435456?4:5,t)).len,this},a.prototype.int32=function(t){return t<0?this.g(v,10,e.fromNumber(t)):this.uint32(t)},a.prototype.sint32=function(t){return this.uint32((t<<1^t>>31)>>>0)},a.prototype.int64=a.prototype.uint64=function(t){var i=e.from(t);return this.g(v,i.length(),i)},a.prototype.sint64=function(t){var i=e.from(t).zzEncode();return this.g(v,i.length(),i)},a.prototype.bool=function(t){return this.g(c,1,t?1:0)},a.prototype.sfixed32=a.prototype.fixed32=function(t){return this.g(d,4,t>>>0)},a.prototype.sfixed64=a.prototype.fixed64=function(t){var i=e.from(t);return this.g(d,4,i.lo).g(d,4,i.hi)},a.prototype.float=function(t){return this.g(r.float.writeFloatLE,4,t)},a.prototype.double=function(t){return this.g(r.float.writeDoubleLE,8,t)};var p=r.Array.prototype.set?function(t,i,n){i.set(t,n)}:function(t,i,n){for(var r=0;r<t.length;++r)i[n+r]=t[r]};a.prototype.bytes=function(t){var i=t.length>>>0;if(!i)return this.g(c,1,0);if(r.isString(t)){var n=a.alloc(i=s.length(t));s.decode(t,n,0),t=n}return this.uint32(i).g(p,i,t)},a.prototype.string=function(t){var i=u.length(t);return i?this.uint32(i).g(u.write,i,t):this.g(c,1,0)},a.prototype.fork=function(){return this.states=new h(this),this.head=this.tail=new o(f,0,0),this.len=0,this},a.prototype.reset=function(){return this.states?(this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next):(this.head=this.tail=new o(f,0,0),this.len=0),this},a.prototype.ldelim=function(){var t=this.head,i=this.tail,n=this.len;return this.reset().uint32(n),n&&(this.tail.next=t.next,this.tail=i,this.len+=n),this},a.prototype.finish=function(){for(var t=this.head.next,i=this.constructor.alloc(this.len),n=0;t;)t.fn(t.val,i,n),n+=t.len,t=t.next;return i},a.o=function(t){n=t}},{39:39}],43:[function(t,i){i.exports=s;var n=t(42);(s.prototype=Object.create(n.prototype)).constructor=s;var r=t(39),e=r.Buffer;function s(){n.call(this)}s.alloc=function(t){return(s.alloc=r.b)(t)};var u=e&&e.prototype instanceof Uint8Array&&"set"===e.prototype.set.name?function(t,i,n){i.set(t,n)}:function(t,i,n){if(t.copy)t.copy(i,n,0,t.length);else for(var r=0;r<t.length;)i[n++]=t[r++]};function o(t,i,n){t.length<40?r.utf8.write(t,i,n):i.utf8Write(t,n)}s.prototype.bytes=function(t){r.isString(t)&&(t=r.w(t,"base64"));var i=t.length>>>0;return this.uint32(i),i&&this.g(u,i,t),this},s.prototype.string=function(t){var i=e.byteLength(t);return this.uint32(i),i&&this.g(o,i,t),this}},{39:39,42:42}]},e={},t=[19],i=function t(i){var n=e[i];return n||r[i][0].call(n=e[i]={exports:{}},t,n,n.exports),n.exports}(t[0]),i.util.global.protobuf=i,"function"==typeof define&&define.amd&&define(["long"],function(t){return t&&t.isLong&&(i.util.Long=t,i.configure()),i}),"object"==typeof module&&module&&module.exports&&(module.exports=i)}();
//# sourceMappingURL=protobuf.min.js.map
