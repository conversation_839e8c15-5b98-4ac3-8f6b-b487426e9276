"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.courseResolvers = void 0;
exports.courseResolvers = {
    Query: {
        courses: async (_, __, { prisma }) => {
            return prisma.course.findMany({
                include: {
                    instructor: true,
                    modules: {
                        include: {
                            lessons: true
                        }
                    }
                }
            });
        },
        course: async (_, args, { prisma }) => {
            const { id } = args;
            return prisma.course.findUnique({
                where: { id },
                include: {
                    instructor: true,
                    modules: {
                        include: {
                            lessons: {
                                include: {
                                    quizzes: {
                                        include: {
                                            questions: true
                                        }
                                    }
                                }
                            }
                        }
                    },
                    students: true
                }
            });
        },
        myProgress: async (_, __, { prisma, user }) => {
            if (!user)
                throw new Error('Not authenticated');
            return prisma.courseProgress.findMany({
                where: { userId: user.userId },
                include: {
                    course: {
                        include: {
                            instructor: true,
                            modules: {
                                include: {
                                    lessons: true
                                }
                            }
                        }
                    }
                }
            });
        },
    },
    Mutation: {
        enrollCourse: async (_, args, { prisma, user }) => {
            if (!user)
                throw new Error('Not authenticated');
            const { courseId } = args;
            return prisma.courseProgress.create({
                data: {
                    userId: user.userId,
                    courseId,
                    completedLessons: "[]",
                },
                include: {
                    course: true,
                    user: true
                }
            });
        },
        completeLesson: async (_, args, { prisma, user }) => {
            if (!user)
                throw new Error('Not authenticated');
            const { lessonId } = args;
            // Find the lesson to get the course
            const lesson = await prisma.lesson.findUnique({
                where: { id: lessonId },
                include: {
                    module: {
                        include: {
                            course: true
                        }
                    }
                }
            });
            if (!lesson)
                throw new Error('Lesson not found');
            // Get existing progress or create new
            const existingProgress = await prisma.courseProgress.findUnique({
                where: {
                    userId_courseId: {
                        userId: user.userId,
                        courseId: lesson.module.course.id
                    }
                }
            });
            let completedLessons = [];
            if (existingProgress?.completedLessons) {
                try {
                    completedLessons = JSON.parse(existingProgress.completedLessons);
                }
                catch (e) {
                    completedLessons = [];
                }
            }
            // Add lesson if not already completed
            if (!completedLessons.includes(lessonId)) {
                completedLessons.push(lessonId);
            }
            const progress = await prisma.courseProgress.upsert({
                where: {
                    userId_courseId: {
                        userId: user.userId,
                        courseId: lesson.module.course.id
                    }
                },
                update: {
                    completedLessons: JSON.stringify(completedLessons),
                    lastAccessed: new Date()
                },
                create: {
                    userId: user.userId,
                    courseId: lesson.module.course.id,
                    completedLessons: JSON.stringify([lessonId]),
                },
                include: {
                    course: true,
                    user: true
                }
            });
            return progress;
        },
    },
    Course: {
        students: async (parent, _, { prisma }) => {
            const progress = await prisma.courseProgress.findMany({
                where: { courseId: parent.id },
                include: { user: true }
            });
            return progress.map(p => p.user);
        },
    }
};
//# sourceMappingURL=course.js.map