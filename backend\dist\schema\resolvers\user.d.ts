import { Context } from '../../context';
export declare const userResolvers: {
    Query: {
        me: (_: any, __: any, { prisma, user }: Context) => Promise<({
            profile: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                avatar: string | null;
                bio: string | null;
                level: string;
                xp: number;
            } | null;
            enrolledCourses: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                courseId: string;
                completedLessons: string | null;
                currentModule: string | null;
                score: number | null;
                lastAccessed: Date;
            }[];
        } & {
            id: string;
            email: string;
            password: string;
            name: string;
            role: string;
            createdAt: Date;
            updatedAt: Date;
        }) | null>;
        leaderboard: (_: any, __: any, { prisma }: Context) => Promise<({
            profile: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                avatar: string | null;
                bio: string | null;
                level: string;
                xp: number;
            } | null;
        } & {
            id: string;
            email: string;
            password: string;
            name: string;
            role: string;
            createdAt: Date;
            updatedAt: Date;
        })[]>;
    };
    Mutation: {
        register: (_: any, args: any, { prisma }: Context) => Promise<{
            token: string;
            user: {
                profile: {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    userId: string;
                    avatar: string | null;
                    bio: string | null;
                    level: string;
                    xp: number;
                } | null;
            } & {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
        }>;
        login: (_: any, args: any, { prisma }: Context) => Promise<{
            token: string;
            user: {
                profile: {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    userId: string;
                    avatar: string | null;
                    bio: string | null;
                    level: string;
                    xp: number;
                } | null;
            } & {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
        }>;
        updateProfile: (_: any, args: any, { prisma, user }: Context) => Promise<{
            profile: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                avatar: string | null;
                bio: string | null;
                level: string;
                xp: number;
            } | null;
        } & {
            id: string;
            email: string;
            password: string;
            name: string;
            role: string;
            createdAt: Date;
            updatedAt: Date;
        }>;
    };
    User: {
        progress: (parent: any, _: any, { prisma }: Context) => Promise<({
            course: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                level: string;
                title: string;
                description: string;
                instructorId: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            courseId: string;
            completedLessons: string | null;
            currentModule: string | null;
            score: number | null;
            lastAccessed: Date;
        })[]>;
    };
};
//# sourceMappingURL=user.d.ts.map