{"version": 3, "file": "plugins.d.ts", "sourceRoot": "", "sources": ["../../../src/externalTypes/plugins.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AACjE,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,KAAK,EAAE,YAAY,EAAE,kBAAkB,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAC/E,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAChD,OAAO,KAAK,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AACpD,OAAO,KAAK,EAAE,gEAAgE,EAAE,MAAM,kCAAkC,CAAC;AACzH,OAAO,KAAK,EACV,qBAAqB,EACrB,uCAAuC,EACvC,iDAAiD,EACjD,wCAAwC,EACxC,qCAAqC,EACrC,sCAAsC,EACtC,oCAAoC,EACpC,yCAAyC,EACzC,uCAAuC,EACvC,qCAAqC,EACrC,8CAA8C,EAC/C,MAAM,sBAAsB,CAAC;AAE9B,MAAM,WAAW,oBAAoB;IACnC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;IAEtC,MAAM,EAAE,aAAa,CAAC;IACtB,MAAM,EAAE,YAAY,CAAC;IACrB,mBAAmB,EAAE,OAAO,CAAC;CAC9B;AAED,MAAM,WAAW,oBAAoB;IACnC,SAAS,EAAE,aAAa,CAAC;IACzB,iBAAiB,CAAC,EAAE,MAAM,CAAC;CAC5B;AAED,MAAM,WAAW,kBAAkB,CACjC,EAAE,CAAC,QAAQ,SAAS,WAAW,GAAG,WAAW;IAG7C,eAAe,CAAC,CACd,OAAO,EAAE,oBAAoB,GAC5B,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC,CAAC;IAKzC,eAAe,CAAC,CACd,cAAc,EAAE,qBAAqB,CAAC,QAAQ,CAAC,GAC9C,OAAO,CAAC,sBAAsB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IASpD,gCAAgC,CAAC,CAAC,EAChC,cAAc,EACd,KAAK,GACN,EAAE;QACD,cAAc,EAAE,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAChD,KAAK,EAAE,KAAK,CAAC;KACd,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAGlB,sBAAsB,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAOpE,yBAAyB,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAGvE,cAAc,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;QAAE,KAAK,EAAE,KAAK,CAAA;KAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CAC7D;AAED,MAAM,WAAW,qBAAqB;IAGpC,qBAAqB,CAAC,CAAC,aAAa,EAAE,oBAAoB,GAAG,IAAI,CAAC;IAQlE,WAAW,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAO9B,cAAc,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAQjC,iBAAiB,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC;CAC5C;AAGD,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,MAAM,GAAG,CAAC,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;CACxC;AAED,MAAM,MAAM,mCAAmC,GAAG,CAChD,GAAG,CAAC,EAAE,KAAK,KACR,OAAO,CAAC,IAAI,CAAC,CAAC;AACnB,MAAM,MAAM,sCAAsC,GAAG,CACnD,GAAG,CAAC,EAAE,aAAa,CAAC,KAAK,CAAC,KACvB,OAAO,CAAC,IAAI,CAAC,CAAC;AACnB,MAAM,MAAM,qCAAqC,GAAG,CAClD,GAAG,CAAC,EAAE,KAAK,KACR,OAAO,CAAC,IAAI,CAAC,CAAC;AACnB,MAAM,MAAM,qCAAqC,GAAG,CAClD,KAAK,EAAE,KAAK,GAAG,IAAI,EACnB,MAAM,CAAC,EAAE,GAAG,KACT,IAAI,CAAC;AAEV,MAAM,WAAW,sBAAsB,CAAC,QAAQ,SAAS,WAAW;IAClE,gBAAgB,CAAC,CACf,cAAc,EAAE,qCAAqC,CAAC,QAAQ,CAAC,GAC9D,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB,eAAe,CAAC,CACd,cAAc,EAAE,oCAAoC,CAAC,QAAQ,CAAC,GAC7D,OAAO,CAAC,mCAAmC,GAAG,IAAI,CAAC,CAAC;IAEvD,kBAAkB,CAAC,CACjB,cAAc,EAAE,uCAAuC,CAAC,QAAQ,CAAC,GAChE,OAAO,CAAC,sCAAsC,GAAG,IAAI,CAAC,CAAC;IAE1D,mBAAmB,CAAC,CAClB,cAAc,EAAE,wCAAwC,CAAC,QAAQ,CAAC,GACjE,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB,kBAAkB,CAAC,CACjB,cAAc,EAAE,uCAAuC,CAAC,QAAQ,CAAC,GAChE,OAAO,CAAC,IAAI,CAAC,CAAC;IAOjB,oBAAoB,CAAC,CACnB,cAAc,EAAE,yCAAyC,CAAC,QAAQ,CAAC,GAClE,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,CAAC;IAInC,iBAAiB,CAAC,CAChB,cAAc,EAAE,sCAAsC,CAAC,QAAQ,CAAC,GAC/D,OAAO,CAAC,+BAA+B,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;IAI7D,gBAAgB,CAAC,CACf,cAAc,EAAE,qCAAqC,CAAC,QAAQ,CAAC,GAC9D,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB,4BAA4B,CAAC,CAC3B,cAAc,EAAE,iDAAiD,CAAC,QAAQ,CAAC,EAC3E,MAAM,EAAE,aAAa,CAAC,YAAY,CAAC,GAClC,OAAO,CAAC,IAAI,CAAC,CAAC;IAGjB,yBAAyB,CAAC,CACxB,cAAc,EAAE,8CAA8C,CAAC,QAAQ,CAAC,EACxE,OAAO,EAAE,gEAAgE,GACxE,OAAO,CAAC,IAAI,CAAC,CAAC;CAClB;AAYD,MAAM,MAAM,0BAA0B,CACpC,OAAO,EACP,QAAQ,EACR,KAAK,GAAG;IAAE,CAAC,OAAO,EAAE,MAAM,GAAG,GAAG,CAAA;CAAE,IAChC;IACF,MAAM,EAAE,OAAO,CAAC;IAChB,IAAI,EAAE,KAAK,CAAC;IACZ,YAAY,EAAE,QAAQ,CAAC;IACvB,IAAI,EAAE,kBAAkB,CAAC;CAC1B,CAAC;AAEF,MAAM,WAAW,+BAA+B,CAAC,QAAQ,SAAS,WAAW;IAC3E,eAAe,CAAC,EAAE,qCAAqC,CAAC;IAOxD,gBAAgB,CAAC,CACf,mBAAmB,EAAE,0BAA0B,CAAC,GAAG,EAAE,QAAQ,CAAC,GAC7D,qCAAqC,GAAG,IAAI,CAAC;CACjD"}