{"version": 3, "sources": ["../../src/lib/recursive-delete.ts"], "names": ["promises", "join", "isAbsolute", "dirname", "isError", "sleep", "timeout", "Promise", "resolve", "setTimeout", "unlinkPath", "p", "isDir", "t", "rmdir", "unlink", "e", "code", "recursiveDelete", "dir", "exclude", "previousPath", "result", "readdir", "withFileTypes", "all", "map", "part", "absolutePath", "name", "isDirectory", "isSymlink", "isSymbolicLink", "linkPath", "readlink", "stats", "stat", "pp", "isNotExcluded", "test"], "mappings": "AACA,SAASA,QAAQ,QAAQ,KAAI;AAC7B,SAASC,IAAI,EAAEC,UAAU,EAAEC,OAAO,QAAQ,OAAM;AAChD,OAAOC,aAAa,aAAY;AAEhC,MAAMC,QAAQ,CAACC,UACb,IAAIC,QAAQ,CAACC,UAAYC,WAAWD,SAASF;AAE/C,MAAMI,aAAa,OAAOC,GAAWC,QAAQ,KAAK,EAAEC,IAAI,CAAC;IACvD,IAAI;QACF,IAAID,OAAO;YACT,MAAMZ,SAASc,KAAK,CAACH;QACvB,OAAO;YACL,MAAMX,SAASe,MAAM,CAACJ;QACxB;IACF,EAAE,OAAOK,GAAG;QACV,MAAMC,OAAOb,QAAQY,MAAMA,EAAEC,IAAI;QACjC,IACE,AAACA,CAAAA,SAAS,WACRA,SAAS,eACTA,SAAS,WACTA,SAAS,QAAO,KAClBJ,IAAI,GACJ;YACA,MAAMR,MAAMQ,IAAI;YAChB,OAAOH,WAAWC,GAAGC,OAAOC;QAC9B;QAEA,IAAII,SAAS,UAAU;YACrB;QACF;QAEA,MAAMD;IACR;AACF;AAEA;;CAEC,GACD,OAAO,eAAeE,gBACpB,wCAAwC,GACxCC,GAAW,EACX,wCAAwC,GACxCC,OAAgB,EAChB,sEAAsE,GACtEC,eAAuB,EAAE;IAEzB,IAAIC;IACJ,IAAI;QACFA,SAAS,MAAMtB,SAASuB,OAAO,CAACJ,KAAK;YAAEK,eAAe;QAAK;IAC7D,EAAE,OAAOR,GAAG;QACV,IAAIZ,QAAQY,MAAMA,EAAEC,IAAI,KAAK,UAAU;YACrC;QACF;QACA,MAAMD;IACR;IAEA,MAAMT,QAAQkB,GAAG,CACfH,OAAOI,GAAG,CAAC,OAAOC;QAChB,MAAMC,eAAe3B,KAAKkB,KAAKQ,KAAKE,IAAI;QAExC,yCAAyC;QACzC,mDAAmD;QACnD,IAAIC,cAAcH,KAAKG,WAAW;QAClC,MAAMC,YAAYJ,KAAKK,cAAc;QAErC,IAAID,WAAW;YACb,MAAME,WAAW,MAAMjC,SAASkC,QAAQ,CAACN;YAEzC,IAAI;gBACF,MAAMO,QAAQ,MAAMnC,SAASoC,IAAI,CAC/BlC,WAAW+B,YACPA,WACAhC,KAAKE,QAAQyB,eAAeK;gBAElCH,cAAcK,MAAML,WAAW;YACjC,EAAE,OAAM,CAAC;QACX;QAEA,MAAMO,KAAKpC,KAAKoB,cAAcM,KAAKE,IAAI;QACvC,MAAMS,gBAAgB,CAAClB,WAAW,CAACA,QAAQmB,IAAI,CAACF;QAEhD,IAAIC,eAAe;YACjB,IAAIR,aAAa;gBACf,MAAMZ,gBAAgBU,cAAcR,SAASiB;YAC/C;YACA,OAAO3B,WAAWkB,cAAc,CAACG,aAAaD;QAChD;IACF;AAEJ"}