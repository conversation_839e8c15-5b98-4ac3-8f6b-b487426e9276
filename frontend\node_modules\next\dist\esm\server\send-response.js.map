{"version": 3, "sources": ["../../src/server/send-response.ts"], "names": ["pipeToNodeResponse", "splitCookiesString", "sendResponse", "req", "res", "response", "waitUntil", "process", "env", "NEXT_RUNTIME", "statusCode", "status", "statusMessage", "statusText", "headers", "for<PERSON>ach", "value", "name", "toLowerCase", "cookie", "append<PERSON><PERSON>er", "originalResponse", "body", "method", "end"], "mappings": "AAGA,SAASA,kBAAkB,QAAQ,kBAAiB;AACpD,SAASC,kBAAkB,QAAQ,cAAa;AAEhD;;;;;;CAMC,GACD,OAAO,eAAeC,aACpBC,GAAoB,EACpBC,GAAqB,EACrBC,QAAkB,EAClBC,SAAwB;IAExB,4BAA4B;IAC5B,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YAKvC,kCAAkC;QAClCJ;QALA,iCAAiC;QACjCD,IAAIM,UAAU,GAAGL,SAASM,MAAM;QAChCP,IAAIQ,aAAa,GAAGP,SAASQ,UAAU;SAGvCR,oBAAAA,SAASS,OAAO,qBAAhBT,kBAAkBU,OAAO,CAAC,CAACC,OAAOC;YAChC,yDAAyD;YACzD,IAAIA,KAAKC,WAAW,OAAO,cAAc;gBACvC,qFAAqF;gBACrF,KAAK,MAAMC,UAAUlB,mBAAmBe,OAAQ;oBAC9CZ,IAAIgB,YAAY,CAACH,MAAME;gBACzB;YACF,OAAO;gBACLf,IAAIgB,YAAY,CAACH,MAAMD;YACzB;QACF;QAEA;;;;;KAKC,GAED,MAAMK,mBAAmB,AAACjB,IAAyBiB,gBAAgB;QAEnE,qGAAqG;QACrG,IAAIhB,SAASiB,IAAI,IAAInB,IAAIoB,MAAM,KAAK,QAAQ;YAC1C,IAAI;gBACF,MAAMvB,mBAAmBK,SAASiB,IAAI,EAAED;YAC1C,SAAU;gBACR,IAAIf,WAAW;oBACb,MAAMA;gBACR;YACF;QACF,OAAO;YACLe,iBAAiBG,GAAG;QACtB;IACF;AACF"}