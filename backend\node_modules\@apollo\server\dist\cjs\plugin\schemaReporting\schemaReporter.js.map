{"version": 3, "file": "schemaReporter.js", "sourceRoot": "", "sources": ["../../../../src/plugin/schemaReporting/schemaReporter.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAA+B;AAU/B,yEAAmE;AAItD,QAAA,eAAe,GAAiB;;;;;;;;;;;;;;CAc5C,CAAC;AAGF,MAAa,cAAc;IAczB,YAAY,OASX;QACC,IAAI,CAAC,OAAO,GAAG;YACb,cAAc,EAAE,kBAAkB;YAClC,WAAW,EAAE,OAAO,CAAC,MAAM;YAC3B,2BAA2B,EAAE,mCAAmC;YAChE,8BAA8B,EAAE,kCAAc;SAC/C,CAAC;QAEF,IAAI,CAAC,WAAW;YACd,OAAO,CAAC,WAAW;gBACnB,4DAA4D,CAAC;QAE/D,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACzC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,CAAC;QACnE,IAAI,CAAC,0BAA0B,GAAG,OAAO,CAAC,0BAA0B,CAAC;QACrE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,oBAAK,CAAC;IAC1C,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,SAAS,GAAG,UAAU,CACzB,GAAG,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAC9C,IAAI,CAAC,yBAAyB,CAC/B,CAAC;IACJ,CAAC;IAEM,IAAI;QACT,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,sBAA+B;QACxE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAG3B,IAAI,IAAI,CAAC,OAAO,EAAE;YAAE,OAAO;QAC3B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;YAC/D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,GAAG,UAAU,CACzB,GAAG,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,cAAc,CAAC,EAC9D,MAAM,CAAC,SAAS,GAAG,IAAI,CACxB,CAAC;YACJ,CAAC;YACD,OAAO;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAIf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kEAAkE,KAAK,EAAE,CAC1E,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;gBACpB,IAAI,CAAC,SAAS,GAAG,UAAU,CACzB,GAAG,EAAE,CAAC,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,EAC9C,IAAI,CAAC,0BAA0B,CAChC,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,YAAY,CACvB,cAAuB;QAEvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC;YAC9C,MAAM,EAAE,IAAI,CAAC,YAAY;YACzB,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI;SACpD,CAAC,CAAC;QAEH,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,SAAS,wBAAwB,CAAC,IAAS;YACzC,OAAO;gBACL,4CAA4C;gBAC5C,mDAAmD;gBACnD,mCAAmC;gBACnC,oBAAoB;gBACpB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;aACrB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACd,CAAC;QAED,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,sBAAsB,EAAE,CAAC;YAC5D,OAAO,IAAI,CAAC,YAAY,CAAC;QAC3B,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,mBAAmB,EAAE,CAAC;YAChE,IAAI,CAAC,MAAM,CAAC,KAAK,CACf;gBACE,8CAA8C;gBAC9C,IAAI,CAAC,YAAY,CAAC,OAAO;gBACzB,kDAAkD;aACnD,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;YACF,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;IAClD,CAAC;IAEO,KAAK,CAAC,WAAW,CACvB,SAAwC;QAExC,MAAM,OAAO,GAAmB;YAC9B,KAAK,EAAE,uBAAe;YACtB,SAAS;SACV,CAAC;QAEF,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;YACxD,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CACb;gBACE,mCAAmC,YAAY,CAAC,MAAM,OAAO;gBAC7D,sCAAsC;aACvC,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YAGH,OAAO,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CACb;gBACE,mCAAmC;gBACnC,kCAAkC;gBAClC,iEAAiE;gBACjE,KAAK;aACN,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAhLD,wCAgLC"}