{"version": 3, "sources": ["../../src/server/web-server.ts"], "names": ["NextWebServer", "BaseServer", "constructor", "options", "handleCatchallRenderRequest", "req", "res", "parsedUrl", "pathname", "query", "Error", "normalizedPage", "serverOptions", "webServerConfig", "isDynamicRoute", "routeRegex", "getNamedRouteRegex", "interpolateDynamicPath", "normalizeVercelUrl", "Object", "keys", "routeKeys", "removeTrailingSlash", "i18nProvider", "detectedLocale", "analyze", "__next<PERSON><PERSON><PERSON>", "bubbleNoFallback", "_nextBubbleNoFallback", "isAPIRoute", "render", "err", "NoFallbackError", "assign", "renderOpts", "extendRenderOpts", "getIncrementalCache", "requestHeaders", "dev", "IncrementalCache", "requestProtocol", "appDir", "hasAppDir", "allowedRevalidateHeaderKeys", "nextConfig", "experimental", "minimalMode", "fetchCache", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "isrMemoryCacheSize", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "getPrerenderManifest", "getResponseCache", "WebResponseCache", "hasPage", "page", "getBuildId", "buildId", "getHasAppDir", "pagesType", "getPagesManifest", "getAppPathsManifest", "attachRequestMeta", "addRequestMeta", "prerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "getNextFontManifest", "nextFontManifest", "renderHTML", "renderToHTML", "disableOptimizedLoading", "runtime", "sendRenderResult", "_req", "<PERSON><PERSON><PERSON><PERSON>", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "result", "contentType", "promise", "isDynamic", "pipeTo", "transformStream", "writable", "payload", "toUnchunkedString", "String", "byteLength", "generateEtags", "generateETag", "body", "send", "findPageComponents", "params", "loadComponent", "components", "run<PERSON><PERSON>", "handleApiRequest", "loadEnvConfig", "getPublicDir", "getHasStaticDir", "get<PERSON>allback", "getFontManifest", "undefined", "handleCompression", "handleUpgrade", "getFallbackErrorComponents", "getRoutesManifest", "getMiddleware", "getFilesystemPaths", "Set", "getPrefetchRsc"], "mappings": ";;;;+BA2CA;;;eAAqBA;;;qBA5BM;oEACiB;sBACf;6BACE;6DACF;4BACF;qCACS;uBACL;6BAC4B;4BACxB;kCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBlB,MAAMA,sBAAsBC,mBAAU;IACnDC,YAAYC,OAAyB,CAAE;QACrC,KAAK,CAACA;aA0FEC,8BAA4C,OACpDC,KACAC,KACAC;YAEA,IAAI,EAAEC,QAAQ,EAAEC,KAAK,EAAE,GAAGF;YAC1B,IAAI,CAACC,UAAU;gBACb,MAAM,IAAIE,MAAM;YAClB;YAEA,4DAA4D;YAC5D,+CAA+C;YAC/C,MAAMC,iBAAiB,IAAI,CAACC,aAAa,CAACC,eAAe,CAACL,QAAQ;YAElE,IAAIA,aAAaG,gBAAgB;gBAC/BH,WAAWG;gBAEX,IAAIG,IAAAA,qBAAc,EAACN,WAAW;oBAC5B,MAAMO,aAAaC,IAAAA,8BAAkB,EAACR,UAAU;oBAChDA,WAAWS,IAAAA,mCAAsB,EAACT,UAAUC,OAAOM;oBACnDG,IAAAA,+BAAkB,EAChBb,KACA,MACAc,OAAOC,IAAI,CAACL,WAAWM,SAAS,GAChC,MACAN;gBAEJ;YACF;YAEA,wDAAwD;YACxDP,WAAWc,IAAAA,wCAAmB,EAACd;YAE/B,IAAI,IAAI,CAACe,YAAY,EAAE;gBACrB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACD,YAAY,CAACE,OAAO,CAACjB;gBAC3D,IAAIgB,gBAAgB;oBAClBjB,UAAUE,KAAK,CAACiB,YAAY,GAAGF;gBACjC;YACF;YAEA,MAAMG,mBAAmB,CAAC,CAAClB,MAAMmB,qBAAqB;YAEtD,IAAIC,IAAAA,sBAAU,EAACrB,WAAW;gBACxB,OAAOC,MAAMmB,qBAAqB;YACpC;YAEA,IAAI;gBACF,MAAM,IAAI,CAACE,MAAM,CAACzB,KAAKC,KAAKE,UAAUC,OAAOF,WAAW;gBAExD,OAAO;YACT,EAAE,OAAOwB,KAAK;gBACZ,IAAIA,eAAeC,2BAAe,IAAIL,kBAAkB;oBACtD,OAAO;gBACT;gBACA,MAAMI;YACR;QACF;QAhJE,uBAAuB;QACvBZ,OAAOc,MAAM,CAAC,IAAI,CAACC,UAAU,EAAE/B,QAAQU,eAAe,CAACsB,gBAAgB;IACzE;IAEUC,oBAAoB,EAC5BC,cAAc,EAGf,EAAE;QACD,MAAMC,MAAM,CAAC,CAAC,IAAI,CAACJ,UAAU,CAACI,GAAG;QACjC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIC,kCAAgB,CAAC;YAC1BD;YACAD;YACAG,iBAAiB;YACjBC,QAAQ,IAAI,CAACC,SAAS;YACtBC,6BACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACF,2BAA2B;YAC1DG,aAAa,IAAI,CAACA,WAAW;YAC7BC,YAAY;YACZC,qBAAqB,IAAI,CAACJ,UAAU,CAACC,YAAY,CAACG,mBAAmB;YACrEC,oBAAoB,IAAI,CAACL,UAAU,CAACC,YAAY,CAACK,kBAAkB;YACnEC,aAAa;YACbC,iBACE,IAAI,CAACxC,aAAa,CAACC,eAAe,CAACwC,uBAAuB;YAC5DC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;QACvD;IACF;IACUC,mBAAmB;QAC3B,OAAO,IAAIC,aAAgB,CAAC,IAAI,CAACV,WAAW;IAC9C;IAEA,MAAgBW,QAAQC,IAAY,EAAE;QACpC,OAAOA,SAAS,IAAI,CAAC9C,aAAa,CAACC,eAAe,CAAC6C,IAAI;IACzD;IAEUC,aAAa;QACrB,OAAO,IAAI,CAAC/C,aAAa,CAACC,eAAe,CAACsB,gBAAgB,CAACyB,OAAO;IACpE;IAEUC,eAAe;QACvB,OAAO,IAAI,CAACjD,aAAa,CAACC,eAAe,CAACiD,SAAS,KAAK;IAC1D;IAEUC,mBAAmB;QAC3B,OAAO;YACL,8DAA8D;YAC9D,CAAC,IAAI,CAACnD,aAAa,CAACC,eAAe,CAChCL,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAACI,aAAa,CAACC,eAAe,CAAC6C,IAAI,CAAC,GAAG,CAAC;QACrE;IACF;IAEUM,sBAAsB;QAC9B,MAAMN,OAAO,IAAI,CAAC9C,aAAa,CAACC,eAAe,CAAC6C,IAAI;QACpD,OAAO;YACL,CAAC,IAAI,CAAC9C,aAAa,CAACC,eAAe,CAAC6C,IAAI,CAAC,EAAE,CAAC,GAAG,EAAEA,KAAK,GAAG,CAAC;QAC5D;IACF;IAEUO,kBACR5D,GAAmB,EACnBE,SAAiC,EACjC;QACA2D,IAAAA,2BAAc,EAAC7D,KAAK,aAAa;YAAE,GAAGE,UAAUE,KAAK;QAAC;IACxD;IAEU6C,uBAAuB;YAE3B;QADJ,MAAM,EAAEa,iBAAiB,EAAE,GAAG,IAAI,CAACvD,aAAa,CAACC,eAAe;QAChE,IAAI,EAAA,mBAAA,IAAI,CAACqB,UAAU,qBAAf,iBAAiBI,GAAG,KAAI,CAAC6B,mBAAmB;YAC9C,OAAO;gBACLC,SAAS,CAAC;gBACVC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,gBAAgB,EAAE;gBAClBC,SAAS;oBACPC,eAAe;gBACjB;YACF;QACF;QACA,OAAON;IACT;IAEUO,sBAAsB;QAC9B,OAAO,IAAI,CAAC9D,aAAa,CAACC,eAAe,CAACsB,gBAAgB,CAACwC,gBAAgB;IAC7E;IA4DUC,WACRvE,GAAmB,EACnBC,GAAoB,EACpBE,QAAgB,EAChBC,KAAyB,EACzByB,UAA4B,EACL;QACvB,MAAM,EAAE2C,YAAY,EAAE,GAAG,IAAI,CAACjE,aAAa,CAACC,eAAe;QAC3D,IAAI,CAACgE,cAAc;YACjB,MAAM,IAAInE,MACR;QAEJ;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,IAAIF,aAAc0B,CAAAA,WAAWI,GAAG,GAAG,eAAe,aAAY,GAAI;YAChE9B,WAAW;QACb;QACA,OAAOqE,aACLxE,KACAC,KACAE,UACAC,OACAU,OAAOc,MAAM,CAACC,YAAY;YACxB4C,yBAAyB;YACzBC,SAAS;QACX;IAEJ;IAEA,MAAgBC,iBACdC,IAAoB,EACpB3E,GAAoB,EACpBH,OAMC,EACc;QACfG,IAAI4E,SAAS,CAAC,kBAAkB;QAEhC,yBAAyB;QACzB,iEAAiE;QACjE,IAAI/E,QAAQgF,eAAe,IAAIhF,QAAQiF,IAAI,KAAK,QAAQ;YACtD9E,IAAI4E,SAAS,CAAC,gBAAgB;QAChC;QAEA,IAAI,CAAC5E,IAAI+E,SAAS,CAAC,iBAAiB;YAClC/E,IAAI4E,SAAS,CACX,gBACA/E,QAAQmF,MAAM,CAACC,WAAW,GACtBpF,QAAQmF,MAAM,CAACC,WAAW,GAC1BpF,QAAQiF,IAAI,KAAK,SACjB,qBACA;QAER;QAEA,IAAII;QACJ,IAAIrF,QAAQmF,MAAM,CAACG,SAAS,EAAE;YAC5BD,UAAUrF,QAAQmF,MAAM,CAACI,MAAM,CAACpF,IAAIqF,eAAe,CAACC,QAAQ;QAC9D,OAAO;YACL,MAAMC,UAAU1F,QAAQmF,MAAM,CAACQ,iBAAiB;YAChDxF,IAAI4E,SAAS,CAAC,kBAAkBa,OAAOC,IAAAA,eAAU,EAACH;YAClD,IAAI1F,QAAQ8F,aAAa,EAAE;gBACzB3F,IAAI4E,SAAS,CAAC,QAAQgB,IAAAA,kBAAY,EAACL;YACrC;YACAvF,IAAI6F,IAAI,CAACN;QACX;QAEAvF,IAAI8F,IAAI;QAER,gDAAgD;QAChD,IAAIZ,SAAS,MAAMA;IACrB;IAEA,MAAgBa,mBAAmB,EACjC3C,IAAI,EACJjD,KAAK,EACL6F,MAAM,EAMP,EAAE;QACD,MAAMhB,SAAS,MAAM,IAAI,CAAC1E,aAAa,CAACC,eAAe,CAAC0F,aAAa,CAAC7C;QACtE,IAAI,CAAC4B,QAAQ,OAAO;QAEpB,OAAO;YACL7E,OAAO;gBACL,GAAIA,SAAS,CAAC,CAAC;gBACf,GAAI6F,UAAU,CAAC,CAAC;YAClB;YACAE,YAAYlB;QACd;IACF;IAEA,2EAA2E;IAC3E,+DAA+D;IAE/D,MAAgBmB,SAAS;QACvB,wDAAwD;QACxD,OAAO;IACT;IAEA,MAAgBC,mBAAmB;QACjC,4DAA4D;QAC5D,OAAO;IACT;IAEUC,gBAAgB;IACxB,2EAA2E;IAC3E,mBAAmB;IACrB;IAEUC,eAAe;QACvB,kDAAkD;QAClD,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO;IACT;IAEA,MAAgBC,cAAc;QAC5B,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAOC;IACT;IAEUC,oBAAoB;IAC5B,wEAAwE;IACxE,4EAA4E;IAC9E;IAEA,MAAgBC,gBAA+B;IAC7C,+CAA+C;IACjD;IAEA,MAAgBC,6BAAuE;QACrF,wEAAwE;QACxE,OAAO;IACT;IAEUC,oBAAyD;QACjE,4EAA4E;QAC5E,gDAAgD;QAChD,OAAOJ;IACT;IAEUK,gBAAmD;QAC3D,yEAAyE;QACzE,gDAAgD;QAChD,OAAOL;IACT;IAEUM,qBAAqB;QAC7B,OAAO,IAAIC;IACb;IAEA,MAAgBC,iBAAyC;QACvD,OAAO;IACT;AACF"}