{"version": 3, "file": "helpers.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/helpers.ts"], "names": [], "mappings": "AAkBA,OAAO,EACL,WAAW,EACX,OAAO,EACP,UAAU,EACV,sBAAsB,EACtB,aAAa,EACb,eAAe,EACf,OAAO,EACP,iBAAiB,EACjB,sBAAsB,EACtB,OAAO,GACR,MAAM,0BAA0B,CAAC;AAElC,MAAM,CAAS,IAAgB,MAAM,GAAK,MAAM,CAAC,SAAS,eAArB,CAAsB;AAE3D,MAAM,UAAU,SAAS,CAAC,KAAU;IAClC,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC;AAC5C,CAAC;AAED,OAAO,EAAE,OAAO,EAAE,CAAC;AAEnB,MAAM,UAAU,uBAAuB,CACrC,EAA8C,EAC9C,OAA0B;QADxB,UAAU,gBAAA,EAAE,EAAE,QAAA,EAAE,GAAG,SAAA;IAGrB,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;QACnC,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,SAAS;gBACf,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAA,EAAE;oBACvB,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,KAAA,EAAE;wBAC3B,CAAC,CAAC,KAAK,CAAC,CAAC;QACb,CAAC;QAED,qDAAqD;QACrD,IAAI,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;YACrC,EAAE,GAAG,GAAG,CAAC;QACX,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;YACnB,OAAO,UAAG,UAAU,cAClB,OAAO,EAAE,KAAK,QAAQ,IAAI,OAAO,EAAE,KAAK,QAAQ,CAAC,CAAC;gBAChD,EAAE;gBACJ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CACpB,CAAC;QACL,CAAC;IACH,CAAC;AACH,CAAC;AAED,IAAM,aAAa,GAAG;IACpB,gBAAgB,EAAE,uBAAuB;IACzC,WAAW,EAAE,IAAI;IACjB,aAAa,EAAE,IAAI;IACnB,2EAA2E;IAC3E,wEAAwE;IACxE,eAAe,EAAE,KAAK;CACvB,CAAC;AAEF,MAAM,UAAU,eAAe,CAAC,MAA2B;IACzD,OAAO,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACxC,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,MAAoD;IAEpD,IAAM,KAAK,GAAG,MAAM,CAAC,eAAe,CAAC;IACrC,OAAO,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC;AAClE,CAAC;AAED,MAAM,UAAU,0BAA0B,CACxC,KAAsB,EACtB,iBAA0C;IAE1C,OAAO,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAClC,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,CAAY;QAC9D,CAAC,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,UAAU,CAAC;AACxD,CAAC;AAED,MAAM,CAAC,IAAM,qBAAqB,GAAG,oBAAoB,CAAC;AAE1D,MAAM,UAAU,sBAAsB,CAAC,cAAsB;IAC3D,IAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAC1D,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;AAC3C,CAAC;AAED,MAAM,UAAU,yBAAyB,CACvC,YAA8B,EAC9B,MAA2B,EAC3B,SAA+B;IAE/B,IAAI,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,UAAC,IAAI;gBAChB,OAAA,yBAAyB,CAAC,YAAY,EAAE,IAAI,EAAE,SAAS,CAAC;YAAxD,CAAwD,CACzD;YACH,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,UAAC,KAAK;gBAClC,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,CAAC;oBACtD,IAAM,GAAG,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;oBAC1C,OAAO,CACL,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC;wBACxB,CAAC,CAAC,KAAK,CAAC,YAAY;4BAClB,yBAAyB,CACvB,KAAK,CAAC,YAAY,EAClB,MAAM,CAAC,GAAG,CAAC,EACX,SAAS,CACV,CAAC,CACL,CAAC;gBACJ,CAAC;gBACD,wDAAwD;gBACxD,gEAAgE;gBAChE,iEAAiE;gBACjE,kEAAkE;gBAClE,yDAAyD;gBACzD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CAAC;IACT,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,uBAAuB,CACrC,KAAiB;IAEjB,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC1E,CAAC;AAED,MAAM,UAAU,yBAAyB;IACvC,OAAO,IAAI,UAAU,EAAE,CAAC;AAC1B,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,QAAsB,EACtB,SAA+B;IAK/B,6EAA6E;IAC7E,gEAAgE;IAChE,IAAM,WAAW,GAAG,iBAAiB,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;IACxE,OAAO;QACL,WAAW,aAAA;QACX,cAAc,YAAC,IAAI;YACjB,IAAI,GAAG,GAAkC,WAAW,CAAC,IAAI,CAAC,CAAC;YAC3D,IAAI,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACtB,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC/B,CAAC;YACD,OAAO,GAAG,IAAI,IAAI,CAAC;QACrB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import type {\n  DocumentNode,\n  FragmentDefinitionNode,\n  SelectionSetNode,\n} from \"graphql\";\n\nimport type { NormalizedCache, InMemoryCacheConfig } from \"./types.js\";\n\nimport type { KeyFieldsContext } from \"./policies.js\";\nimport type { FragmentRegistryAPI } from \"./fragmentRegistry.js\";\n\nimport type {\n  Reference,\n  StoreValue,\n  StoreObject,\n  FragmentMap,\n  FragmentMapFunction,\n} from \"../../utilities/index.js\";\nimport {\n  isReference,\n  isField,\n  DeepMerger,\n  resultKeyNameFromField,\n  shouldInclude,\n  isNonNullObject,\n  compact,\n  createFragmentMap,\n  getFragmentDefinitions,\n  isArray,\n} from \"../../utilities/index.js\";\n\nexport const { hasOwnProperty: hasOwn } = Object.prototype;\n\nexport function isNullish(value: any): value is null | undefined {\n  return value === null || value === void 0;\n}\n\nexport { isArray };\n\nexport function defaultDataIdFromObject(\n  { __typename, id, _id }: Readonly<StoreObject>,\n  context?: KeyFieldsContext\n): string | undefined {\n  if (typeof __typename === \"string\") {\n    if (context) {\n      context.keyObject =\n        !isNullish(id) ? { id }\n        : !isNullish(_id) ? { _id }\n        : void 0;\n    }\n\n    // If there is no object.id, fall back to object._id.\n    if (isNullish(id) && !isNullish(_id)) {\n      id = _id;\n    }\n\n    if (!isNullish(id)) {\n      return `${__typename}:${\n        typeof id === \"number\" || typeof id === \"string\" ?\n          id\n        : JSON.stringify(id)\n      }`;\n    }\n  }\n}\n\nconst defaultConfig = {\n  dataIdFromObject: defaultDataIdFromObject,\n  addTypename: true,\n  resultCaching: true,\n  // Thanks to the shouldCanonizeResults helper, this should be the only line\n  // you have to change to reenable canonization by default in the future.\n  canonizeResults: false,\n};\n\nexport function normalizeConfig(config: InMemoryCacheConfig) {\n  return compact(defaultConfig, config);\n}\n\nexport function shouldCanonizeResults(\n  config: Pick<InMemoryCacheConfig, \"canonizeResults\">\n): boolean {\n  const value = config.canonizeResults;\n  return value === void 0 ? defaultConfig.canonizeResults : value;\n}\n\nexport function getTypenameFromStoreObject(\n  store: NormalizedCache,\n  objectOrReference: StoreObject | Reference\n): string | undefined {\n  return isReference(objectOrReference) ?\n      (store.get(objectOrReference.__ref, \"__typename\") as string)\n    : objectOrReference && objectOrReference.__typename;\n}\n\nexport const TypeOrFieldNameRegExp = /^[_a-z][_0-9a-z]*/i;\n\nexport function fieldNameFromStoreName(storeFieldName: string): string {\n  const match = storeFieldName.match(TypeOrFieldNameRegExp);\n  return match ? match[0] : storeFieldName;\n}\n\nexport function selectionSetMatchesResult(\n  selectionSet: SelectionSetNode,\n  result: Record<string, any>,\n  variables?: Record<string, any>\n): boolean {\n  if (isNonNullObject(result)) {\n    return isArray(result) ?\n        result.every((item) =>\n          selectionSetMatchesResult(selectionSet, item, variables)\n        )\n      : selectionSet.selections.every((field) => {\n          if (isField(field) && shouldInclude(field, variables)) {\n            const key = resultKeyNameFromField(field);\n            return (\n              hasOwn.call(result, key) &&\n              (!field.selectionSet ||\n                selectionSetMatchesResult(\n                  field.selectionSet,\n                  result[key],\n                  variables\n                ))\n            );\n          }\n          // If the selection has been skipped with @skip(true) or\n          // @include(false), it should not count against the matching. If\n          // the selection is not a field, it must be a fragment (inline or\n          // named). We will determine if selectionSetMatchesResult for that\n          // fragment when we get to it, so for now we return true.\n          return true;\n        });\n  }\n  return false;\n}\n\nexport function storeValueIsStoreObject(\n  value: StoreValue\n): value is StoreObject {\n  return isNonNullObject(value) && !isReference(value) && !isArray(value);\n}\n\nexport function makeProcessedFieldsMerger() {\n  return new DeepMerger();\n}\n\nexport function extractFragmentContext(\n  document: DocumentNode,\n  fragments?: FragmentRegistryAPI\n): {\n  fragmentMap: FragmentMap;\n  lookupFragment: FragmentMapFunction;\n} {\n  // FragmentMap consisting only of fragments defined directly in document, not\n  // including other fragments registered in the FragmentRegistry.\n  const fragmentMap = createFragmentMap(getFragmentDefinitions(document));\n  return {\n    fragmentMap,\n    lookupFragment(name) {\n      let def: FragmentDefinitionNode | null = fragmentMap[name];\n      if (!def && fragments) {\n        def = fragments.lookup(name);\n      }\n      return def || null;\n    },\n  };\n}\n"]}