export declare enum ApolloServerErrorCode {
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR",
    GRAPHQL_PARSE_FAILED = "GRAPHQL_PARSE_FAILED",
    GRAPHQL_VALIDATION_FAILED = "GRAPHQL_VALIDATION_FAILED",
    PERSISTED_QUERY_NOT_FOUND = "PERSISTED_QUERY_NOT_FOUND",
    PERSISTED_QUERY_NOT_SUPPORTED = "PERSISTED_QUERY_NOT_SUPPORTED",
    BAD_USER_INPUT = "BAD_USER_INPUT",
    OPERATION_RESOLUTION_FAILURE = "OPERATION_RESOLUTION_FAILURE",
    BAD_REQUEST = "BAD_REQUEST"
}
export declare enum ApolloServerValidationErrorCode {
    INTROSPECTION_DISABLED = "INTROSPECTION_DISABLED",
    MAX_RECURSIVE_SELECTIONS_EXCEEDED = "MAX_RECURSIVE_SELECTIONS_EXCEEDED"
}
export declare function unwrapResolverError(error: unknown): unknown;
//# sourceMappingURL=index.d.ts.map