{"version": 3, "file": "entityStore.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/entityStore.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAE7D,OAAO,EAAE,GAAG,EAAE,MAAM,UAAU,CAAC;AAC/B,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAOjC,OAAO,EACL,WAAW,EACX,aAAa,EACb,UAAU,EACV,eAAe,EACf,aAAa,EACb,eAAe,GAChB,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,MAAM,EAAE,sBAAsB,EAAE,MAAM,cAAc,CAAC;AAgB9D,IAAM,MAAM,GAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnD,IAAM,WAAW,GAAkB,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC;AAChD,IAAM,UAAU,GAAuB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAE3D;IAGE,qBACkB,QAAkB,EAClB,KAAiB;QAFnC,iBAGI;QAFc,aAAQ,GAAR,QAAQ,CAAU;QAClB,UAAK,GAAL,KAAK,CAAY;QAJzB,SAAI,GAA0B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QA6X5D,6EAA6E;QAC7E,4EAA4E;QAC5E,0EAA0E;QAClE,YAAO,GAEX,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAyDxB,4EAA4E;QACpE,SAAI,GAER,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAgExB,4EAA4E;QAC5E,oDAAoD;QAC7C,kBAAa,GAAG,UACrB,iBAAsD,EACtD,cAAsB;YAEtB,OAAA,eAAe,CACb,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC;gBAC9B,KAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,cAAc,CAAC;gBACnD,CAAC,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,CAAC,CACtC;QAJpB,CAIoB,CAAC;QAEvB,gEAAgE;QAChE,wEAAwE;QACxE,oEAAoE;QAC7D,YAAO,GAAoB,UAAC,QAAQ;YACzC,OAAO,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAC1B,KAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAC1B,CAAC,CAAC,OAAO,QAAQ,KAAK,QAAQ,CAAC;QACnC,CAAC,CAAC;QAEF,wEAAwE;QACxE,+EAA+E;QAC/E,kFAAkF;QAClF,4BAA4B;QACrB,gBAAW,GAAwB,UAAC,YAAY,EAAE,cAAc;YACrE,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;gBACrC,OAAO,aAAa,CAAC,YAAY,CAAC,CAAC;YACrC,CAAC;YAED,IAAI,WAAW,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9B,OAAO,YAAY,CAAC;YACtB,CAAC;YAEM,IAAA,EAAE,GAAI,KAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAxC,CAAyC;YAElD,IAAI,EAAE,EAAE,CAAC;gBACP,IAAM,GAAG,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;gBAC9B,IAAI,cAAc,EAAE,CAAC;oBACnB,KAAI,CAAC,KAAK,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;gBAC/B,CAAC;gBACD,OAAO,GAAG,CAAC;YACb,CAAC;QACH,CAAC,CAAC;IApiBC,CAAC;IASJ,mEAAmE;IACnE,wEAAwE;IACxE,kDAAkD;IAE3C,8BAAQ,GAAf;QACE,oBAAY,IAAI,CAAC,IAAI,EAAG;IAC1B,CAAC;IAEM,yBAAG,GAAV,UAAW,MAAc;QACvB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC;IAC9C,CAAC;IAEM,yBAAG,GAAV,UAAW,MAAc,EAAE,SAAiB;QAC1C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACrC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;YACnC,IAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtC,IAAI,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC;gBACvD,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QACD,IACE,SAAS,KAAK,YAAY;YAC1B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,MAAM,CAAC,EACpD,CAAC;YACD,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAES,4BAAM,GAAhB,UACE,MAAc,EACd,iBAA2B;QAE3B,wEAAwE;QACxE,2EAA2E;QAC3E,4EAA4E;QAC5E,sEAAsE;QACtE,6CAA6C;QAC7C,IAAI,iBAAiB;YAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAE7D,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAEM,2BAAK,GAAZ,UAAa,KAA2B,EAAE,KAA2B;QAArE,iBA0FC;QAzFC,IAAI,MAA0B,CAAC;QAE/B,+CAA+C;QAC/C,IAAI,WAAW,CAAC,KAAK,CAAC;YAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAC5C,IAAI,WAAW,CAAC,KAAK,CAAC;YAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QAE5C,IAAM,QAAQ,GACZ,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAEpE,IAAM,QAAQ,GACZ,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAEpE,uEAAuE;QACvE,wDAAwD;QACxD,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,SAAS,CAAC,OAAO,MAAM,KAAK,QAAQ,EAAE,iCAAiC,CAAC,CAAC;QAEzE,IAAM,MAAM,GAAgB,IAAI,UAAU,CAAC,qBAAqB,CAAC,CAAC,KAAK,CACrE,QAAQ,EACR,QAAQ,CACT,CAAC;QAEF,mEAAmE;QACnE,mEAAmE;QACnE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;QAE3B,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACzB,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACvB,IAAM,eAAa,GAAsB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAE7D,uEAAuE;gBACvE,sEAAsE;gBACtE,0BAA0B;gBAC1B,IAAI,CAAC,QAAQ;oBAAE,eAAa,CAAC,QAAQ,GAAG,CAAC,CAAC;gBAE1C,oEAAoE;gBACpE,+CAA+C;gBAC/C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAC,cAAc;oBAC3C,IACE,CAAC,QAAQ;wBACT,QAAQ,CAAC,cAAc,CAAC,KAAK,MAAM,CAAC,cAAc,CAAC,EACnD,CAAC;wBACD,0DAA0D;wBAC1D,uDAAuD;wBACvD,eAAa,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;wBAElC,4DAA4D;wBAC5D,6DAA6D;wBAC7D,8DAA8D;wBAC9D,6DAA6D;wBAC7D,4DAA4D;wBAC5D,6DAA6D;wBAC7D,2DAA2D;wBAC3D,IAAM,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;wBACzD,IACE,SAAS,KAAK,cAAc;4BAC5B,CAAC,KAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,EACvD,CAAC;4BACD,eAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;wBAC/B,CAAC;wBAED,kEAAkE;wBAClE,mEAAmE;wBACnE,iEAAiE;wBACjE,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,KAAI,YAAY,KAAK,CAAC,EAAE,CAAC;4BAClE,OAAO,MAAM,CAAC,cAAc,CAAC,CAAC;wBAChC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,IACE,eAAa,CAAC,UAAU;oBACxB,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC,UAAU,CAAC;oBAClC,kDAAkD;oBAClD,2DAA2D;oBAC3D,4DAA4D;oBAC5D,2CAA2C;oBAC3C,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,UAAU,EAC7D,CAAC;oBACD,OAAO,eAAa,CAAC,UAAU,CAAC;gBAClC,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,eAAa,CAAC,CAAC,OAAO,CAAC,UAAC,SAAS;oBAC3C,OAAA,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAgB,EAAE,SAAS,CAAC;gBAA7C,CAA6C,CAC9C,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEM,4BAAM,GAAb,UACE,MAAc,EACd,MAAsD;QAFxD,iBAgIC;QA5HC,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAExC,IAAI,WAAW,EAAE,CAAC;YAChB,IAAM,eAAa,GAAwB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC/D,IAAI,aAAW,GAAG,KAAK,CAAC;YACxB,IAAI,YAAU,GAAG,IAAI,CAAC;YAEtB,IAAM,eAAa,GAAG;gBACpB,MAAM,QAAA;gBACN,UAAU,YAAA;gBACV,WAAW,aAAA;gBACX,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,SAAS,EAAE,UACT,kBAA6C,EAC7C,IAA8B;oBAE9B,OAAA,KAAI,CAAC,QAAQ,CAAC,SAAS,CACrB,OAAO,kBAAkB,KAAK,QAAQ,CAAC,CAAC;wBACtC;4BACE,SAAS,EAAE,kBAAkB;4BAC7B,IAAI,EAAE,IAAI,IAAI,aAAa,CAAC,MAAM,CAAC;yBACpC;wBACH,CAAC,CAAC,kBAAkB,EACpB,EAAE,KAAK,EAAE,KAAI,EAAE,CAChB;gBARD,CAQC;aAC+B,CAAC;YAErC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAC,cAAc;gBAC9C,IAAM,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;gBACzD,IAAI,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC;gBAC7C,IAAI,UAAU,KAAK,KAAK,CAAC;oBAAE,OAAO;gBAClC,IAAM,MAAM,GACV,OAAO,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CACtC,MAAM,CAAC,cAAc,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAC5C,CAAC;gBACJ,IAAI,MAAM,EAAE,CAAC;oBACX,IAAI,QAAQ,GACV,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAChC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC,wBAC7B,eAAa,KAChB,SAAS,WAAA,EACT,cAAc,gBAAA,EACd,OAAO,EAAE,KAAI,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,CAAC,IAChD,CACH,CAAC;oBACJ,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;wBAC5B,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;oBAC3C,CAAC;yBAAM,CAAC;wBACN,IAAI,QAAQ,KAAK,MAAM;4BAAE,QAAQ,GAAG,KAAK,CAAC,CAAC;wBAC3C,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;4BAC5B,eAAa,CAAC,cAAc,CAAC,GAAG,QAAQ,CAAC;4BACzC,aAAW,GAAG,IAAI,CAAC;4BACnB,UAAU,GAAG,QAAsB,CAAC;4BAEpC,IAAI,OAAO,EAAE,CAAC;gCACZ,IAAM,cAAc,GAAG,UAAC,GAAc;oCACpC,IAAI,KAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,CAAC;wCACzC,SAAS,CAAC,IAAI,CACZ,uFAAuF;4CACrF,iIAAiI;4CACjI,6BAA6B,EAC/B,GAAG,CACJ,CAAC;wCACF,OAAO,IAAI,CAAC;oCACd,CAAC;gCACH,CAAC,CAAC;gCACF,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;oCAC1B,cAAc,CAAC,QAAQ,CAAC,CAAC;gCAC3B,CAAC;qCAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;oCACnC,2EAA2E;oCAC3E,IAAI,aAAa,GAAY,KAAK,CAAC;oCACnC,IAAI,gBAAgB,SAAS,CAAC;oCAC9B,KAAoB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE,CAAC;wCAA1B,IAAM,KAAK,iBAAA;wCACd,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;4CACvB,aAAa,GAAG,IAAI,CAAC;4CACrB,IAAI,cAAc,CAAC,KAAK,CAAC;gDAAE,MAAM;wCACnC,CAAC;6CAAM,CAAC;4CACN,0EAA0E;4CAC1E,8DAA8D;4CAC9D,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;gDAClC,IAAA,EAAE,GAAI,KAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAjC,CAAkC;gDAC3C,mGAAmG;gDACnG,IAAI,EAAE,EAAE,CAAC;oDACP,gBAAgB,GAAG,KAAK,CAAC;gDAC3B,CAAC;4CACH,CAAC;wCACH,CAAC;wCACD,IAAI,aAAa,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;4CACpD,SAAS,CAAC,IAAI,CACZ,uIAAuI;gDACrI,6HAA6H,EAC/H,gBAAgB,CACjB,CAAC;4CACF,MAAM;wCACR,CAAC;oCACH,CAAC;gCACH,CAAC;4BACH,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,CAAC;oBAC1B,YAAU,GAAG,KAAK,CAAC;gBACrB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,aAAW,EAAE,CAAC;gBAChB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,eAAa,CAAC,CAAC;gBAElC,IAAI,YAAU,EAAE,CAAC;oBACf,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;wBAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;oBAC7B,CAAC;yBAAM,CAAC;wBACN,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC3B,CAAC;oBACD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBACvC,CAAC;gBAED,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,8DAA8D;IAC9D,wEAAwE;IACxE,qEAAqE;IACrE,qEAAqE;IACrE,+EAA+E;IAC/E,sEAAsE;IAC/D,4BAAM,GAAb,UACE,MAAc,EACd,SAAkB,EAClB,IAA0B;;QAE1B,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACxC,IAAI,WAAW,EAAE,CAAC;YAChB,IAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAS,WAAW,EAAE,YAAY,CAAC,CAAC;YACvE,IAAM,cAAc,GAClB,SAAS,IAAI,IAAI,CAAC,CAAC;gBACjB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,QAAQ,UAAA,EAAE,SAAS,WAAA,EAAE,IAAI,MAAA,EAAE,CAAC;gBAChE,CAAC,CAAC,SAAS,CAAC;YACd,OAAO,IAAI,CAAC,MAAM,CAChB,MAAM,EACN,cAAc,CAAC,CAAC;gBAEZ,GAAC,cAAc,IAAG,WAAW;oBAEjC,CAAC,CAAC,WAAW,CACd,CAAC;QACJ,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,2BAAK,GAAZ,UAAa,OAA2B,EAAE,KAAkB;QAC1D,IAAI,OAAO,GAAG,KAAK,CAAC;QACpB,IAAI,OAAO,CAAC,EAAE,EAAE,CAAC;YACf,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;gBACvC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YACrE,CAAC;YACD,IAAI,IAAI,YAAY,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;gBAC5C,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC;YACzD,CAAC;YACD,8DAA8D;YAC9D,+DAA+D;YAC/D,iEAAiE;YACjE,6DAA6D;YAC7D,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,EAAE,CAAC;gBACjC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,IAAI,UAAU,CAAC,CAAC;YAChE,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,2BAAK,GAAZ;QACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC;IAEM,6BAAO,GAAd;QAAA,iBAYC;QAXC,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC5B,IAAM,YAAY,GAAa,EAAE,CAAC;QAClC,IAAI,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,UAAC,EAAE;YAC7B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE,CAAC;gBACtD,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACxB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;YACxB,GAAG,CAAC,MAAM,GAAG,EAAE,YAAY,EAAE,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC;QACrD,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,6BAAO,GAAd,UAAe,OAAqC;QAApD,iBAeC;QAdC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM;YACpC,IAAI,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC;gBAC/C,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAI,OAAO,EAAE,CAAC;YACJ,IAAA,MAAM,GAAc,OAAO,OAArB,EAAK,MAAI,UAAK,OAAO,EAA7B,UAAmB,CAAF,CAAa;YACpC,MAAM,CAAC,IAAI,CAAC,MAAI,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM;gBAC/B,KAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAI,CAAC,MAAM,CAAgB,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;YACH,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;IACH,CAAC;IAcM,4BAAM,GAAb,UAAc,MAAc;QAC1B,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAClE,CAAC;IAEM,6BAAO,GAAd,UAAe,MAAc;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAM,KAAK,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,CAAC,CAAC;IACX,CAAC;IAED,wEAAwE;IACxE,qDAAqD;IAC9C,kCAAY,GAAnB,UAAoB,GAAuB;QAAvB,oBAAA,EAAA,UAAU,GAAG,EAAU;QACzC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAChD,IAAI,IAAI,YAAY,KAAK,EAAE,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,+DAA+D;YAC/D,gEAAgE;YAChE,2CAA2C;YAC3C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACrE,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,6EAA6E;IAC7E,0EAA0E;IAC1E,2EAA2E;IAC3E,mDAAmD;IAC5C,wBAAE,GAAT;QAAA,iBAqBC;QApBC,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACjC,GAAG,CAAC,OAAO,CAAC,UAAC,EAAE;YACb,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC;gBAC9B,uEAAuE;gBACvE,uEAAuE;gBACvE,4CAA4C;gBAC5C,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC5D,sEAAsE;gBACtE,mDAAmD;gBACnD,OAAO,QAAQ,CAAC,EAAE,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,IAAM,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,IAAI,WAAW,CAAC,MAAM,EAAE,CAAC;YACvB,IAAI,MAAI,GAAgB,IAAI,CAAC;YAC7B,OAAO,MAAI,YAAY,KAAK;gBAAE,MAAI,GAAG,MAAI,CAAC,MAAM,CAAC;YACjD,WAAW,CAAC,OAAO,CAAC,UAAC,EAAE,IAAK,OAAA,MAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAf,CAAe,CAAC,CAAC;QAC/C,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAOM,qCAAe,GAAtB,UAAuB,MAAc;QACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;YACpC,IAAM,OAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;YACxD,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/B,IAAI,CAAC,IAAI;gBAAE,OAAO,OAAK,CAAC;YAExB,IAAM,SAAO,GAAG,IAAI,GAAG,CAA+B,CAAC,IAAI,CAAC,CAAC,CAAC;YAC9D,qEAAqE;YACrE,kEAAkE;YAClE,SAAO,CAAC,OAAO,CAAC,UAAC,GAAG;gBAClB,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;oBACrB,OAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;oBACxB,qEAAqE;oBACrE,qEAAqE;oBACrE,+DAA+D;oBAC/D,gEAAgE;oBAChE,iEAAiE;oBACjE,sEAAsE;oBACtE,oEAAoE;gBACtE,CAAC;gBACD,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;oBACzB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;wBAC3B,IAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;wBACvB,oEAAoE;wBACpE,6BAA6B;wBAC7B,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;4BAC3B,SAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACrB,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3B,CAAC;IAyBM,kCAAY,GAAnB;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;IA8CH,kBAAC;AAAD,CAAC,AA3iBD,IA2iBC;;AAID,2EAA2E;AAC3E,2EAA2E;AAC3E,0EAA0E;AAC1E,0EAA0E;AAC1E,yEAAyE;AACzE,wEAAwE;AACxE,4EAA4E;AAC5E,2EAA2E;AAC3E,yEAAyE;AACzE,0EAA0E;AAC1E,0EAA0E;AAC1E,2EAA2E;AAC3E,4DAA4D;AAC5D;IAOE,oBACkB,OAAgB,EACxB,MAAgC;QAAhC,uBAAA,EAAA,aAAgC;QADxB,YAAO,GAAP,OAAO,CAAS;QACxB,WAAM,GAAN,MAAM,CAA0B;QARlC,MAAC,GAAgD,IAAI,CAAC;QAU5D,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEM,iCAAY,GAAnB;QACE,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAU,CAAC,CAAC,CAAC,IAAI,CAAC;QAC7C,IAAI,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC;IAC1C,CAAC;IAEM,2BAAM,GAAb,UAAc,MAAc,EAAE,cAAsB;QAClD,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC;YAC3C,IAAM,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;YACzD,IAAI,SAAS,KAAK,cAAc,EAAE,CAAC;gBACjC,0DAA0D;gBAC1D,iEAAiE;gBACjE,6DAA6D;gBAC7D,gEAAgE;gBAChE,wBAAwB;gBACxB,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;YACxC,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;IACH,CAAC;IAEM,0BAAK,GAAZ,UAAa,MAAc,EAAE,cAAsB;QACjD,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,CAAC,CAAC,KAAK,CACV,UAAU,CAAC,MAAM,EAAE,cAAc,CAAC;YAClC,uEAAuE;YACvE,sEAAsE;YACtE,sEAAsE;YACtE,qEAAqE;YACrE,uEAAuE;YACvE,wEAAwE;YACxE,6DAA6D;YAC7D,cAAc,KAAK,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CACtD,CAAC;QACJ,CAAC;IACH,CAAC;IACH,iBAAC;AAAD,CAAC,AApDD,IAoDC;AAED,SAAS,UAAU,CAAC,MAAc,EAAE,cAAsB;IACxD,oEAAoE;IACpE,uEAAuE;IACvE,oDAAoD;IACpD,OAAO,cAAc,GAAG,GAAG,GAAG,MAAM,CAAC;AACvC,CAAC;AAED,MAAM,UAAU,8BAA8B,CAC5C,KAAsB,EACtB,QAAgB;IAEhB,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE,CAAC;QACjC,yEAAyE;QACzE,wEAAwE;QACxE,0EAA0E;QAC1E,yEAAyE;QACzE,4EAA4E;QAC5E,4EAA4E;QAC5E,4EAA4E;QAC5E,+CAA+C;QAC/C,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC3C,CAAC;AACH,CAAC;AAED,WAAiB,WAAW;IAC1B,kEAAkE;IAClE;QAA0B,wBAAW;QACnC,cAAY,EAQX;gBAPC,QAAQ,cAAA,EACR,qBAAoB,EAApB,aAAa,mBAAG,IAAI,KAAA,EACpB,IAAI,UAAA;YAMJ,YAAA,MAAK,YAAC,QAAQ,EAAE,IAAI,UAAU,CAAC,aAAa,CAAC,CAAC,SAAC;YAIjC,WAAK,GAAG,IAAI,KAAK,CAAC,KAAI,CAAC,CAAC;YAiBxB,iBAAW,GAAG,IAAI,IAAI,CAAc,aAAa,CAAC,CAAC;YApBjE,IAAI,IAAI;gBAAE,KAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;QAC/B,CAAC;QAIM,uBAAQ,GAAf,UACE,OAAe,EACf,MAAmC;YAEnC,wEAAwE;YACxE,sEAAsE;YACtE,sCAAsC;YACtC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC9C,CAAC;QAEM,0BAAW,GAAlB;YACE,+BAA+B;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC;QAGM,yBAAU,GAAjB;YACE,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACjD,CAAC;QACH,WAAC;IAAD,CAAC,AAnCD,CAA0B,WAAW,GAmCpC;IAnCY,gBAAI,OAmChB,CAAA;AACH,CAAC,EAtCgB,WAAW,KAAX,WAAW,QAsC3B;AAED,6EAA6E;AAC7E,iCAAiC;AACjC;IAAoB,yBAAW;IAC7B,eACkB,EAAU,EACV,MAAmB,EACnB,MAAmC,EACnC,KAAiB;QAEjC,YAAA,MAAK,YAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAC;QALd,QAAE,GAAF,EAAE,CAAQ;QACV,YAAM,GAAN,MAAM,CAAa;QACnB,YAAM,GAAN,MAAM,CAA6B;QACnC,WAAK,GAAL,KAAK,CAAY;QAGjC,MAAM,CAAC,KAAI,CAAC,CAAC;;IACf,CAAC;IAEM,wBAAQ,GAAf,UAAgB,OAAe,EAAE,MAAmC;QAClE,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;IAEM,2BAAW,GAAlB,UAAmB,OAAe;QAAlC,iBAuDC;QAtDC,gEAAgE;QAChE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAI,OAAO,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;YACxB,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACvB,uEAAuE;gBACvE,uEAAuE;gBACvE,uEAAuE;gBACvE,6CAA6C;gBAC7C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM;oBACpC,IAAM,cAAc,GAAG,KAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACzC,IAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;oBACnD,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACvB,iEAAiE;wBACjE,kEAAkE;wBAClE,+DAA+D;wBAC/D,oEAAoE;wBACpE,sBAAsB;wBACtB,KAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACtB,CAAC;yBAAM,IAAI,CAAC,cAAc,EAAE,CAAC;wBAC3B,iEAAiE;wBACjE,gEAAgE;wBAChE,kEAAkE;wBAClE,6CAA6C;wBAC7C,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;wBACrC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,UAAC,cAAc;4BACpD,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;wBAC3C,CAAC,CAAC,CAAC;oBACL,CAAC;yBAAM,IAAI,cAAc,KAAK,iBAAiB,EAAE,CAAC;wBAChD,kEAAkE;wBAClE,gEAAgE;wBAChE,WAAW;wBACX,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAC,cAAc;4BACjD,IACE,CAAC,KAAK,CACJ,cAAc,CAAC,cAAc,CAAC,EAC9B,iBAAiB,CAAC,cAAc,CAAC,CAClC,EACD,CAAC;gCACD,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;4BAC3C,CAAC;wBACH,CAAC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,kEAAkE;QAClE,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAExC,gDAAgD;QAChD,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;IAEM,wBAAQ,GAAf;QACE,6BACK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GACtB,IAAI,CAAC,IAAI,EACZ;IACJ,CAAC;IAEM,+BAAe,GAAtB,UAAuB,MAAc;QACnC,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACvD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,uBAE9B,UAAU,GACV,gBAAK,CAAC,eAAe,YAAC,MAAM,CAAC,EAEpC,CAAC,CAAC,UAAU,CAAC;IACjB,CAAC;IAEM,0BAAU,GAAjB;QACE,IAAI,CAAC,GAAgB,IAAI,CAAC,MAAM,CAAC;QACjC,OAAQ,CAAW,CAAC,MAAM;YAAE,CAAC,GAAI,CAAW,CAAC,MAAM,CAAC;QACpD,OAAO,CAAC,CAAC,UAAU,CAAC,KAAK,CACvB,CAAC;QACD,mBAAmB;QACnB,SAAS,CACV,CAAC;IACJ,CAAC;IACH,YAAC;AAAD,CAAC,AAlGD,CAAoB,WAAW,GAkG9B;AAED,6EAA6E;AAC7E,6EAA6E;AAC7E,+EAA+E;AAC/E,gEAAgE;AAChE;IAAoB,yBAAK;IACvB,eAAY,IAAsB;QAChC,OAAA,MAAK,YACH,mBAAmB,EACnB,IAAI,EACJ,cAAO,CAAC,EACR,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAC/C,SAAC;IACJ,CAAC;IAEM,2BAAW,GAAlB;QACE,gCAAgC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,qBAAK,GAAZ,UAAa,KAA2B,EAAE,KAA2B;QACnE,0EAA0E;QAC1E,sEAAsE;QACtE,sEAAsE;QACtE,uEAAuE;QACvE,2BAA2B;QAC3B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACzC,CAAC;IACH,YAAC;AAAD,CAAC,AAvBD,CAAoB,KAAK,GAuBxB;AAED,SAAS,qBAAqB,CAC5B,cAA2B,EAC3B,cAA2B,EAC3B,QAAyB;IAEzB,IAAM,aAAa,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC/C,IAAM,aAAa,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC/C,uEAAuE;IACvE,qEAAqE;IACrE,iEAAiE;IACjE,oEAAoE;IACpE,gEAAgE;IAChE,OAAO,KAAK,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC;AAC7E,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAC,KAAU;IAC9C,8DAA8D;IAC9D,OAAO,CAAC,CAAC,CAAC,KAAK,YAAY,WAAW,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACjE,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\nimport type { OptimisticDependencyFunction } from \"optimism\";\nimport { dep } from \"optimism\";\nimport { equal } from \"@wry/equality\";\nimport { Trie } from \"@wry/trie\";\n\nimport type {\n  StoreValue,\n  StoreObject,\n  Reference,\n} from \"../../utilities/index.js\";\nimport {\n  isReference,\n  makeReference,\n  DeepMerger,\n  maybeDeepFreeze,\n  canUseWeakMap,\n  isNonNullObject,\n} from \"../../utilities/index.js\";\nimport type { NormalizedCache, NormalizedCacheObject } from \"./types.js\";\nimport { hasOwn, fieldNameFromStoreName } from \"./helpers.js\";\nimport type { Policies, StorageType } from \"./policies.js\";\nimport type { Cache } from \"../core/types/Cache.js\";\nimport type {\n  SafeReadonly,\n  Modifier,\n  Modifiers,\n  ReadFieldOptions,\n  ToReferenceFunction,\n  CanReadFunction,\n  InvalidateModifier,\n  DeleteModifier,\n  ModifierDetails,\n} from \"../core/types/common.js\";\nimport type { DocumentNode, FieldNode, SelectionSetNode } from \"graphql\";\n\nconst DELETE: DeleteModifier = Object.create(null);\nconst delModifier: Modifier<any> = () => DELETE;\nconst INVALIDATE: InvalidateModifier = Object.create(null);\n\nexport abstract class EntityStore implements NormalizedCache {\n  protected data: NormalizedCacheObject = Object.create(null);\n\n  constructor(\n    public readonly policies: Policies,\n    public readonly group: CacheGroup\n  ) {}\n\n  public abstract addLayer(\n    layerId: string,\n    replay: (layer: EntityStore) => any\n  ): Layer;\n\n  public abstract removeLayer(layerId: string): EntityStore;\n\n  // Although the EntityStore class is abstract, it contains concrete\n  // implementations of the various NormalizedCache interface methods that\n  // are inherited by the Root and Layer subclasses.\n\n  public toObject(): NormalizedCacheObject {\n    return { ...this.data };\n  }\n\n  public has(dataId: string): boolean {\n    return this.lookup(dataId, true) !== void 0;\n  }\n\n  public get(dataId: string, fieldName: string): StoreValue {\n    this.group.depend(dataId, fieldName);\n    if (hasOwn.call(this.data, dataId)) {\n      const storeObject = this.data[dataId];\n      if (storeObject && hasOwn.call(storeObject, fieldName)) {\n        return storeObject[fieldName];\n      }\n    }\n    if (\n      fieldName === \"__typename\" &&\n      hasOwn.call(this.policies.rootTypenamesById, dataId)\n    ) {\n      return this.policies.rootTypenamesById[dataId];\n    }\n    if (this instanceof Layer) {\n      return this.parent.get(dataId, fieldName);\n    }\n  }\n\n  protected lookup(\n    dataId: string,\n    dependOnExistence?: boolean\n  ): StoreObject | undefined {\n    // The has method (above) calls lookup with dependOnExistence = true, so\n    // that it can later be invalidated when we add or remove a StoreObject for\n    // this dataId. Any consumer who cares about the contents of the StoreObject\n    // should not rely on this dependency, since the contents could change\n    // without the object being added or removed.\n    if (dependOnExistence) this.group.depend(dataId, \"__exists\");\n\n    if (hasOwn.call(this.data, dataId)) {\n      return this.data[dataId];\n    }\n\n    if (this instanceof Layer) {\n      return this.parent.lookup(dataId, dependOnExistence);\n    }\n\n    if (this.policies.rootTypenamesById[dataId]) {\n      return Object.create(null);\n    }\n  }\n\n  public merge(older: string | StoreObject, newer: StoreObject | string): void {\n    let dataId: string | undefined;\n\n    // Convert unexpected references to ID strings.\n    if (isReference(older)) older = older.__ref;\n    if (isReference(newer)) newer = newer.__ref;\n\n    const existing: StoreObject | undefined =\n      typeof older === \"string\" ? this.lookup((dataId = older)) : older;\n\n    const incoming: StoreObject | undefined =\n      typeof newer === \"string\" ? this.lookup((dataId = newer)) : newer;\n\n    // If newer was a string ID, but that ID was not defined in this store,\n    // then there are no fields to be merged, so we're done.\n    if (!incoming) return;\n\n    invariant(typeof dataId === \"string\", \"store.merge expects a string ID\");\n\n    const merged: StoreObject = new DeepMerger(storeObjectReconciler).merge(\n      existing,\n      incoming\n    );\n\n    // Even if merged === existing, existing may have come from a lower\n    // layer, so we always need to set this.data[dataId] on this level.\n    this.data[dataId] = merged;\n\n    if (merged !== existing) {\n      delete this.refs[dataId];\n      if (this.group.caching) {\n        const fieldsToDirty: Record<string, 1> = Object.create(null);\n\n        // If we added a new StoreObject where there was previously none, dirty\n        // anything that depended on the existence of this dataId, such as the\n        // EntityStore#has method.\n        if (!existing) fieldsToDirty.__exists = 1;\n\n        // Now invalidate dependents who called getFieldValue for any fields\n        // that are changing as a result of this merge.\n        Object.keys(incoming).forEach((storeFieldName) => {\n          if (\n            !existing ||\n            existing[storeFieldName] !== merged[storeFieldName]\n          ) {\n            // Always dirty the full storeFieldName, which may include\n            // serialized arguments following the fieldName prefix.\n            fieldsToDirty[storeFieldName] = 1;\n\n            // Also dirty fieldNameFromStoreName(storeFieldName) if it's\n            // different from storeFieldName and this field does not have\n            // keyArgs configured, because that means the cache can't make\n            // any assumptions about how field values with the same field\n            // name but different arguments might be interrelated, so it\n            // must err on the side of invalidating all field values that\n            // share the same short fieldName, regardless of arguments.\n            const fieldName = fieldNameFromStoreName(storeFieldName);\n            if (\n              fieldName !== storeFieldName &&\n              !this.policies.hasKeyArgs(merged.__typename, fieldName)\n            ) {\n              fieldsToDirty[fieldName] = 1;\n            }\n\n            // If merged[storeFieldName] has become undefined, and this is the\n            // Root layer, actually delete the property from the merged object,\n            // which is guaranteed to have been created fresh in this method.\n            if (merged[storeFieldName] === void 0 && !(this instanceof Layer)) {\n              delete merged[storeFieldName];\n            }\n          }\n        });\n\n        if (\n          fieldsToDirty.__typename &&\n          !(existing && existing.__typename) &&\n          // Since we return default root __typename strings\n          // automatically from store.get, we don't need to dirty the\n          // ROOT_QUERY.__typename field if merged.__typename is equal\n          // to the default string (usually \"Query\").\n          this.policies.rootTypenamesById[dataId] === merged.__typename\n        ) {\n          delete fieldsToDirty.__typename;\n        }\n\n        Object.keys(fieldsToDirty).forEach((fieldName) =>\n          this.group.dirty(dataId as string, fieldName)\n        );\n      }\n    }\n  }\n\n  public modify(\n    dataId: string,\n    fields: Modifier<any> | Modifiers<Record<string, any>>\n  ): boolean {\n    const storeObject = this.lookup(dataId);\n\n    if (storeObject) {\n      const changedFields: Record<string, any> = Object.create(null);\n      let needToMerge = false;\n      let allDeleted = true;\n\n      const sharedDetails = {\n        DELETE,\n        INVALIDATE,\n        isReference,\n        toReference: this.toReference,\n        canRead: this.canRead,\n        readField: <V = StoreValue>(\n          fieldNameOrOptions: string | ReadFieldOptions,\n          from?: StoreObject | Reference\n        ) =>\n          this.policies.readField<V>(\n            typeof fieldNameOrOptions === \"string\" ?\n              {\n                fieldName: fieldNameOrOptions,\n                from: from || makeReference(dataId),\n              }\n            : fieldNameOrOptions,\n            { store: this }\n          ),\n      } satisfies Partial<ModifierDetails>;\n\n      Object.keys(storeObject).forEach((storeFieldName) => {\n        const fieldName = fieldNameFromStoreName(storeFieldName);\n        let fieldValue = storeObject[storeFieldName];\n        if (fieldValue === void 0) return;\n        const modify: Modifier<StoreValue> | undefined =\n          typeof fields === \"function\" ? fields : (\n            fields[storeFieldName] || fields[fieldName]\n          );\n        if (modify) {\n          let newValue =\n            modify === delModifier ? DELETE : (\n              modify(maybeDeepFreeze(fieldValue), {\n                ...sharedDetails,\n                fieldName,\n                storeFieldName,\n                storage: this.getStorage(dataId, storeFieldName),\n              })\n            );\n          if (newValue === INVALIDATE) {\n            this.group.dirty(dataId, storeFieldName);\n          } else {\n            if (newValue === DELETE) newValue = void 0;\n            if (newValue !== fieldValue) {\n              changedFields[storeFieldName] = newValue;\n              needToMerge = true;\n              fieldValue = newValue as StoreValue;\n\n              if (__DEV__) {\n                const checkReference = (ref: Reference) => {\n                  if (this.lookup(ref.__ref) === undefined) {\n                    invariant.warn(\n                      \"cache.modify: You are trying to write a Reference that is not part of the store: %o\\n\" +\n                        \"Please make sure to set the `mergeIntoStore` parameter to `true` when creating a Reference that is not part of the store yet:\\n\" +\n                        \"`toReference(object, true)`\",\n                      ref\n                    );\n                    return true;\n                  }\n                };\n                if (isReference(newValue)) {\n                  checkReference(newValue);\n                } else if (Array.isArray(newValue)) {\n                  // Warn about writing \"mixed\" arrays of Reference and non-Reference objects\n                  let seenReference: boolean = false;\n                  let someNonReference: unknown;\n                  for (const value of newValue) {\n                    if (isReference(value)) {\n                      seenReference = true;\n                      if (checkReference(value)) break;\n                    } else {\n                      // Do not warn on primitive values, since those could never be represented\n                      // by a reference. This is a valid (albeit uncommon) use case.\n                      if (typeof value === \"object\" && !!value) {\n                        const [id] = this.policies.identify(value);\n                        // check if object could even be referenced, otherwise we are not interested in it for this warning\n                        if (id) {\n                          someNonReference = value;\n                        }\n                      }\n                    }\n                    if (seenReference && someNonReference !== undefined) {\n                      invariant.warn(\n                        \"cache.modify: Writing an array with a mix of both References and Objects will not result in the Objects being normalized correctly.\\n\" +\n                          \"Please convert the object instance %o to a Reference before writing it to the cache by calling `toReference(object, true)`.\",\n                        someNonReference\n                      );\n                      break;\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n        if (fieldValue !== void 0) {\n          allDeleted = false;\n        }\n      });\n\n      if (needToMerge) {\n        this.merge(dataId, changedFields);\n\n        if (allDeleted) {\n          if (this instanceof Layer) {\n            this.data[dataId] = void 0;\n          } else {\n            delete this.data[dataId];\n          }\n          this.group.dirty(dataId, \"__exists\");\n        }\n\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  // If called with only one argument, removes the entire entity\n  // identified by dataId. If called with a fieldName as well, removes all\n  // fields of that entity whose names match fieldName according to the\n  // fieldNameFromStoreName helper function. If called with a fieldName\n  // and variables, removes all fields of that entity whose names match fieldName\n  // and whose arguments when cached exactly match the variables passed.\n  public delete(\n    dataId: string,\n    fieldName?: string,\n    args?: Record<string, any>\n  ) {\n    const storeObject = this.lookup(dataId);\n    if (storeObject) {\n      const typename = this.getFieldValue<string>(storeObject, \"__typename\");\n      const storeFieldName =\n        fieldName && args ?\n          this.policies.getStoreFieldName({ typename, fieldName, args })\n        : fieldName;\n      return this.modify(\n        dataId,\n        storeFieldName ?\n          {\n            [storeFieldName]: delModifier,\n          }\n        : delModifier\n      );\n    }\n    return false;\n  }\n\n  public evict(options: Cache.EvictOptions, limit: EntityStore): boolean {\n    let evicted = false;\n    if (options.id) {\n      if (hasOwn.call(this.data, options.id)) {\n        evicted = this.delete(options.id, options.fieldName, options.args);\n      }\n      if (this instanceof Layer && this !== limit) {\n        evicted = this.parent.evict(options, limit) || evicted;\n      }\n      // Always invalidate the field to trigger rereading of watched\n      // queries, even if no cache data was modified by the eviction,\n      // because queries may depend on computed fields with custom read\n      // functions, whose values are not stored in the EntityStore.\n      if (options.fieldName || evicted) {\n        this.group.dirty(options.id, options.fieldName || \"__exists\");\n      }\n    }\n    return evicted;\n  }\n\n  public clear(): void {\n    this.replace(null);\n  }\n\n  public extract(): NormalizedCacheObject {\n    const obj = this.toObject();\n    const extraRootIds: string[] = [];\n    this.getRootIdSet().forEach((id) => {\n      if (!hasOwn.call(this.policies.rootTypenamesById, id)) {\n        extraRootIds.push(id);\n      }\n    });\n    if (extraRootIds.length) {\n      obj.__META = { extraRootIds: extraRootIds.sort() };\n    }\n    return obj;\n  }\n\n  public replace(newData: NormalizedCacheObject | null): void {\n    Object.keys(this.data).forEach((dataId) => {\n      if (!(newData && hasOwn.call(newData, dataId))) {\n        this.delete(dataId);\n      }\n    });\n    if (newData) {\n      const { __META, ...rest } = newData;\n      Object.keys(rest).forEach((dataId) => {\n        this.merge(dataId, rest[dataId] as StoreObject);\n      });\n      if (__META) {\n        __META.extraRootIds.forEach(this.retain, this);\n      }\n    }\n  }\n\n  public abstract getStorage(\n    idOrObj: string | StoreObject,\n    ...storeFieldNames: (string | number)[]\n  ): StorageType;\n\n  // Maps root entity IDs to the number of times they have been retained, minus\n  // the number of times they have been released. Retained entities keep other\n  // entities they reference (even indirectly) from being garbage collected.\n  private rootIds: {\n    [rootId: string]: number;\n  } = Object.create(null);\n\n  public retain(rootId: string): number {\n    return (this.rootIds[rootId] = (this.rootIds[rootId] || 0) + 1);\n  }\n\n  public release(rootId: string): number {\n    if (this.rootIds[rootId] > 0) {\n      const count = --this.rootIds[rootId];\n      if (!count) delete this.rootIds[rootId];\n      return count;\n    }\n    return 0;\n  }\n\n  // Return a Set<string> of all the ID strings that have been retained by\n  // this layer/root *and* any layers/roots beneath it.\n  public getRootIdSet(ids = new Set<string>()) {\n    Object.keys(this.rootIds).forEach(ids.add, ids);\n    if (this instanceof Layer) {\n      this.parent.getRootIdSet(ids);\n    } else {\n      // Official singleton IDs like ROOT_QUERY and ROOT_MUTATION are\n      // always considered roots for garbage collection, regardless of\n      // their retainment counts in this.rootIds.\n      Object.keys(this.policies.rootTypenamesById).forEach(ids.add, ids);\n    }\n    return ids;\n  }\n\n  // The goal of garbage collection is to remove IDs from the Root layer of the\n  // store that are no longer reachable starting from any IDs that have been\n  // explicitly retained (see retain and release, above). Returns an array of\n  // dataId strings that were removed from the store.\n  public gc() {\n    const ids = this.getRootIdSet();\n    const snapshot = this.toObject();\n    ids.forEach((id) => {\n      if (hasOwn.call(snapshot, id)) {\n        // Because we are iterating over an ECMAScript Set, the IDs we add here\n        // will be visited in later iterations of the forEach loop only if they\n        // were not previously contained by the Set.\n        Object.keys(this.findChildRefIds(id)).forEach(ids.add, ids);\n        // By removing IDs from the snapshot object here, we protect them from\n        // getting removed from the root store layer below.\n        delete snapshot[id];\n      }\n    });\n    const idsToRemove = Object.keys(snapshot);\n    if (idsToRemove.length) {\n      let root: EntityStore = this;\n      while (root instanceof Layer) root = root.parent;\n      idsToRemove.forEach((id) => root.delete(id));\n    }\n    return idsToRemove;\n  }\n\n  // Lazily tracks { __ref: <dataId> } strings contained by this.data[dataId].\n  private refs: {\n    [dataId: string]: Record<string, true>;\n  } = Object.create(null);\n\n  public findChildRefIds(dataId: string): Record<string, true> {\n    if (!hasOwn.call(this.refs, dataId)) {\n      const found = (this.refs[dataId] = Object.create(null));\n      const root = this.data[dataId];\n      if (!root) return found;\n\n      const workSet = new Set<Record<string | number, any>>([root]);\n      // Within the store, only arrays and objects can contain child entity\n      // references, so we can prune the traversal using this predicate:\n      workSet.forEach((obj) => {\n        if (isReference(obj)) {\n          found[obj.__ref] = true;\n          // In rare cases, a { __ref } Reference object may have other fields.\n          // This often indicates a mismerging of References with StoreObjects,\n          // but garbage collection should not be fooled by a stray __ref\n          // property in a StoreObject (ignoring all the other fields just\n          // because the StoreObject looks like a Reference). To avoid this\n          // premature termination of findChildRefIds recursion, we fall through\n          // to the code below, which will handle any other properties of obj.\n        }\n        if (isNonNullObject(obj)) {\n          Object.keys(obj).forEach((key) => {\n            const child = obj[key];\n            // No need to add primitive values to the workSet, since they cannot\n            // contain reference objects.\n            if (isNonNullObject(child)) {\n              workSet.add(child);\n            }\n          });\n        }\n      });\n    }\n    return this.refs[dataId];\n  }\n\n  // Used to compute cache keys specific to this.group.\n  /** overload for `InMemoryCache.maybeBroadcastWatch` */\n  public makeCacheKey(\n    document: DocumentNode,\n    callback: Cache.WatchCallback<any>,\n    details: string\n  ): object;\n  /** overload for `StoreReader.executeSelectionSet` */\n  public makeCacheKey(\n    selectionSet: SelectionSetNode,\n    parent: string /* = ( Reference.__ref ) */ | StoreObject,\n    varString: string | undefined,\n    canonizeResults: boolean\n  ): object;\n  /** overload for `StoreReader.executeSubSelectedArray` */\n  public makeCacheKey(\n    field: FieldNode,\n    array: readonly any[],\n    varString: string | undefined\n  ): object;\n  /** @deprecated This is only meant for internal usage,\n   * in your own code please use a `Trie` instance instead. */\n  public makeCacheKey(...args: any[]): object;\n  public makeCacheKey() {\n    return this.group.keyMaker.lookupArray(arguments);\n  }\n\n  // Bound function that can be passed around to provide easy access to fields\n  // of Reference objects as well as ordinary objects.\n  public getFieldValue = <T = StoreValue>(\n    objectOrReference: StoreObject | Reference | undefined,\n    storeFieldName: string\n  ) =>\n    maybeDeepFreeze(\n      isReference(objectOrReference) ?\n        this.get(objectOrReference.__ref, storeFieldName)\n      : objectOrReference && objectOrReference[storeFieldName]\n    ) as SafeReadonly<T>;\n\n  // Returns true for non-normalized StoreObjects and non-dangling\n  // References, indicating that readField(name, objOrRef) has a chance of\n  // working. Useful for filtering out dangling references from lists.\n  public canRead: CanReadFunction = (objOrRef) => {\n    return isReference(objOrRef) ?\n        this.has(objOrRef.__ref)\n      : typeof objOrRef === \"object\";\n  };\n\n  // Bound function that converts an id or an object with a __typename and\n  // primary key fields to a Reference object. If called with a Reference object,\n  // that same Reference object is returned. Pass true for mergeIntoStore to persist\n  // an object into the store.\n  public toReference: ToReferenceFunction = (objOrIdOrRef, mergeIntoStore) => {\n    if (typeof objOrIdOrRef === \"string\") {\n      return makeReference(objOrIdOrRef);\n    }\n\n    if (isReference(objOrIdOrRef)) {\n      return objOrIdOrRef;\n    }\n\n    const [id] = this.policies.identify(objOrIdOrRef);\n\n    if (id) {\n      const ref = makeReference(id);\n      if (mergeIntoStore) {\n        this.merge(id, objOrIdOrRef);\n      }\n      return ref;\n    }\n  };\n}\n\nexport type FieldValueGetter = EntityStore[\"getFieldValue\"];\n\n// A single CacheGroup represents a set of one or more EntityStore objects,\n// typically the Root store in a CacheGroup by itself, and all active Layer\n// stores in a group together. A single EntityStore object belongs to only\n// one CacheGroup, store.group. The CacheGroup is responsible for tracking\n// dependencies, so store.group is helpful for generating unique keys for\n// cached results that need to be invalidated when/if those dependencies\n// change. If we used the EntityStore objects themselves as cache keys (that\n// is, store rather than store.group), the cache would become unnecessarily\n// fragmented by all the different Layer objects. Instead, the CacheGroup\n// approach allows all optimistic Layer objects in the same linked list to\n// belong to one CacheGroup, with the non-optimistic Root object belonging\n// to another CacheGroup, allowing resultCaching dependencies to be tracked\n// separately for optimistic and non-optimistic entity data.\nclass CacheGroup {\n  private d: OptimisticDependencyFunction<string> | null = null;\n\n  // Used by the EntityStore#makeCacheKey method to compute cache keys\n  // specific to this CacheGroup.\n  public keyMaker!: Trie<object>;\n\n  constructor(\n    public readonly caching: boolean,\n    private parent: CacheGroup | null = null\n  ) {\n    this.resetCaching();\n  }\n\n  public resetCaching() {\n    this.d = this.caching ? dep<string>() : null;\n    this.keyMaker = new Trie(canUseWeakMap);\n  }\n\n  public depend(dataId: string, storeFieldName: string) {\n    if (this.d) {\n      this.d(makeDepKey(dataId, storeFieldName));\n      const fieldName = fieldNameFromStoreName(storeFieldName);\n      if (fieldName !== storeFieldName) {\n        // Fields with arguments that contribute extra identifying\n        // information to the fieldName (thus forming the storeFieldName)\n        // depend not only on the full storeFieldName but also on the\n        // short fieldName, so the field can be invalidated using either\n        // level of specificity.\n        this.d(makeDepKey(dataId, fieldName));\n      }\n      if (this.parent) {\n        this.parent.depend(dataId, storeFieldName);\n      }\n    }\n  }\n\n  public dirty(dataId: string, storeFieldName: string) {\n    if (this.d) {\n      this.d.dirty(\n        makeDepKey(dataId, storeFieldName),\n        // When storeFieldName === \"__exists\", that means the entity identified\n        // by dataId has either disappeared from the cache or was newly added,\n        // so the result caching system would do well to \"forget everything it\n        // knows\" about that object. To achieve that kind of invalidation, we\n        // not only dirty the associated result cache entry, but also remove it\n        // completely from the dependency graph. For the optimism implementation\n        // details, see https://github.com/benjamn/optimism/pull/195.\n        storeFieldName === \"__exists\" ? \"forget\" : \"setDirty\"\n      );\n    }\n  }\n}\n\nfunction makeDepKey(dataId: string, storeFieldName: string) {\n  // Since field names cannot have '#' characters in them, this method\n  // of joining the field name and the ID should be unambiguous, and much\n  // cheaper than JSON.stringify([dataId, fieldName]).\n  return storeFieldName + \"#\" + dataId;\n}\n\nexport function maybeDependOnExistenceOfEntity(\n  store: NormalizedCache,\n  entityId: string\n) {\n  if (supportsResultCaching(store)) {\n    // We use this pseudo-field __exists elsewhere in the EntityStore code to\n    // represent changes in the existence of the entity object identified by\n    // entityId. This dependency gets reliably dirtied whenever an object with\n    // this ID is deleted (or newly created) within this group, so any result\n    // cache entries (for example, StoreReader#executeSelectionSet results) that\n    // depend on __exists for this entityId will get dirtied as well, leading to\n    // the eventual recomputation (instead of reuse) of those result objects the\n    // next time someone reads them from the cache.\n    store.group.depend(entityId, \"__exists\");\n  }\n}\n\nexport namespace EntityStore {\n  // Refer to this class as EntityStore.Root outside this namespace.\n  export class Root extends EntityStore {\n    constructor({\n      policies,\n      resultCaching = true,\n      seed,\n    }: {\n      policies: Policies;\n      resultCaching?: boolean;\n      seed?: NormalizedCacheObject;\n    }) {\n      super(policies, new CacheGroup(resultCaching));\n      if (seed) this.replace(seed);\n    }\n\n    public readonly stump = new Stump(this);\n\n    public addLayer(\n      layerId: string,\n      replay: (layer: EntityStore) => any\n    ): Layer {\n      // Adding an optimistic Layer on top of the Root actually adds the Layer\n      // on top of the Stump, so the Stump always comes between the Root and\n      // any Layer objects that we've added.\n      return this.stump.addLayer(layerId, replay);\n    }\n\n    public removeLayer(): Root {\n      // Never remove the root layer.\n      return this;\n    }\n\n    public readonly storageTrie = new Trie<StorageType>(canUseWeakMap);\n    public getStorage(): StorageType {\n      return this.storageTrie.lookupArray(arguments);\n    }\n  }\n}\n\n// Not exported, since all Layer instances are created by the addLayer method\n// of the EntityStore.Root class.\nclass Layer extends EntityStore {\n  constructor(\n    public readonly id: string,\n    public readonly parent: EntityStore,\n    public readonly replay: (layer: EntityStore) => any,\n    public readonly group: CacheGroup\n  ) {\n    super(parent.policies, group);\n    replay(this);\n  }\n\n  public addLayer(layerId: string, replay: (layer: EntityStore) => any): Layer {\n    return new Layer(layerId, this, replay, this.group);\n  }\n\n  public removeLayer(layerId: string): EntityStore {\n    // Remove all instances of the given id, not just the first one.\n    const parent = this.parent.removeLayer(layerId);\n\n    if (layerId === this.id) {\n      if (this.group.caching) {\n        // Dirty every ID we're removing. Technically we might be able to avoid\n        // dirtying fields that have values in higher layers, but we don't have\n        // easy access to higher layers here, and we're about to recreate those\n        // layers anyway (see parent.addLayer below).\n        Object.keys(this.data).forEach((dataId) => {\n          const ownStoreObject = this.data[dataId];\n          const parentStoreObject = parent[\"lookup\"](dataId);\n          if (!parentStoreObject) {\n            // The StoreObject identified by dataId was defined in this layer\n            // but will be undefined in the parent layer, so we can delete the\n            // whole entity using this.delete(dataId). Since we're about to\n            // throw this layer away, the only goal of this deletion is to dirty\n            // the removed fields.\n            this.delete(dataId);\n          } else if (!ownStoreObject) {\n            // This layer had an entry for dataId but it was undefined, which\n            // means the entity was deleted in this layer, and it's about to\n            // become undeleted when we remove this layer, so we need to dirty\n            // all fields that are about to be reexposed.\n            this.group.dirty(dataId, \"__exists\");\n            Object.keys(parentStoreObject).forEach((storeFieldName) => {\n              this.group.dirty(dataId, storeFieldName);\n            });\n          } else if (ownStoreObject !== parentStoreObject) {\n            // If ownStoreObject is not exactly the same as parentStoreObject,\n            // dirty any fields whose values will change as a result of this\n            // removal.\n            Object.keys(ownStoreObject).forEach((storeFieldName) => {\n              if (\n                !equal(\n                  ownStoreObject[storeFieldName],\n                  parentStoreObject[storeFieldName]\n                )\n              ) {\n                this.group.dirty(dataId, storeFieldName);\n              }\n            });\n          }\n        });\n      }\n\n      return parent;\n    }\n\n    // No changes are necessary if the parent chain remains identical.\n    if (parent === this.parent) return this;\n\n    // Recreate this layer on top of the new parent.\n    return parent.addLayer(this.id, this.replay);\n  }\n\n  public toObject(): NormalizedCacheObject {\n    return {\n      ...this.parent.toObject(),\n      ...this.data,\n    };\n  }\n\n  public findChildRefIds(dataId: string): Record<string, true> {\n    const fromParent = this.parent.findChildRefIds(dataId);\n    return hasOwn.call(this.data, dataId) ?\n        {\n          ...fromParent,\n          ...super.findChildRefIds(dataId),\n        }\n      : fromParent;\n  }\n\n  public getStorage(): StorageType {\n    let p: EntityStore = this.parent;\n    while ((p as Layer).parent) p = (p as Layer).parent;\n    return p.getStorage.apply(\n      p,\n      // @ts-expect-error\n      arguments\n    );\n  }\n}\n\n// Represents a Layer permanently installed just above the Root, which allows\n// reading optimistically (and registering optimistic dependencies) even when\n// no optimistic layers are currently active. The stump.group CacheGroup object\n// is shared by any/all Layer objects added on top of the Stump.\nclass Stump extends Layer {\n  constructor(root: EntityStore.Root) {\n    super(\n      \"EntityStore.Stump\",\n      root,\n      () => {},\n      new CacheGroup(root.group.caching, root.group)\n    );\n  }\n\n  public removeLayer() {\n    // Never remove the Stump layer.\n    return this;\n  }\n\n  public merge(older: string | StoreObject, newer: string | StoreObject) {\n    // We never want to write any data into the Stump, so we forward any merge\n    // calls to the Root instead. Another option here would be to throw an\n    // exception, but the toReference(object, true) function can sometimes\n    // trigger Stump writes (which used to be Root writes, before the Stump\n    // concept was introduced).\n    return this.parent.merge(older, newer);\n  }\n}\n\nfunction storeObjectReconciler(\n  existingObject: StoreObject,\n  incomingObject: StoreObject,\n  property: string | number\n): StoreValue {\n  const existingValue = existingObject[property];\n  const incomingValue = incomingObject[property];\n  // Wherever there is a key collision, prefer the incoming value, unless\n  // it is deeply equal to the existing value. It's worth checking deep\n  // equality here (even though blindly returning incoming would be\n  // logically correct) because preserving the referential identity of\n  // existing data can prevent needless rereading and rerendering.\n  return equal(existingValue, incomingValue) ? existingValue : incomingValue;\n}\n\nexport function supportsResultCaching(store: any): store is EntityStore {\n  // When result caching is disabled, store.depend will be null.\n  return !!(store instanceof EntityStore && store.group.caching);\n}\n"]}