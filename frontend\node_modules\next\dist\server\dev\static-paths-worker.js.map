{"version": 3, "sources": ["../../../src/server/dev/static-paths-worker.ts"], "names": ["loadStaticPaths", "AppRouteRouteModule", "require", "distDir", "pathname", "config", "httpAgentOptions", "locales", "defaultLocale", "isAppPath", "page", "isrFlushToDisk", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "requestHeaders", "incremental<PERSON>ache<PERSON>andlerPath", "ppr", "setConfig", "setHttpClientAndAgentOptions", "components", "loadComponents", "getStaticPaths", "Error", "routeModule", "generateParams", "is", "revalidate", "userland", "dynamic", "dynamicParams", "generateStaticParams", "segmentPath", "collectGenerateParams", "ComponentMod", "tree", "buildAppStaticPaths", "configFileName", "serverHooks", "staticGenerationAsyncStorage", "buildStaticPaths"], "mappings": ";;;;+BA6BsBA;;;eAAAA;;;QA3Bf;QACA;uBAMA;gCAEwB;mCACc;4EAEhB;sDACgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7C,MAAM,EAAEC,mBAAmB,EAAE,GAC3BC,QAAQ;AAWH,eAAeF,gBAAgB,EACpCG,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,cAAc,EACdC,mBAAmB,EACnBC,kBAAkB,EAClBC,cAAc,EACdC,2BAA2B,EAC3BC,GAAG,EAgBJ;IAKC,oCAAoC;IACpCd,QAAQ,4CAA4Ce,SAAS,CAACZ;IAC9Da,IAAAA,+CAA4B,EAAC;QAC3BZ;IACF;IAEA,MAAMa,aAAa,MAAMC,IAAAA,8BAAc,EAAC;QACtCjB;QACA,qDAAqD;QACrDO,MAAMA,QAAQN;QACdK;IACF;IAEA,IAAI,CAACU,WAAWE,cAAc,IAAI,CAACZ,WAAW;QAC5C,yDAAyD;QACzD,mDAAmD;QACnD,MAAM,IAAIa,MACR,CAAC,uDAAuD,EAAElB,SAAS,CAAC;IAExE;IAEA,IAAIK,WAAW;QACb,MAAM,EAAEc,WAAW,EAAE,GAAGJ;QACxB,MAAMK,iBACJD,eAAetB,oBAAoBwB,EAAE,CAACF,eAClC;YACE;gBACElB,QAAQ;oBACNqB,YAAYH,YAAYI,QAAQ,CAACD,UAAU;oBAC3CE,SAASL,YAAYI,QAAQ,CAACC,OAAO;oBACrCC,eAAeN,YAAYI,QAAQ,CAACE,aAAa;gBACnD;gBACAC,sBAAsBP,YAAYI,QAAQ,CAACG,oBAAoB;gBAC/DC,aAAa3B;YACf;SACD,GACD,MAAM4B,IAAAA,4BAAqB,EAACb,WAAWc,YAAY,CAACC,IAAI;QAE9D,OAAO,MAAMC,IAAAA,0BAAmB,EAAC;YAC/BzB,MAAMN;YACNoB;YACAY,gBAAgB/B,OAAO+B,cAAc;YACrCjC;YACAW;YACAC;YACAsB,aAAAA;YACAC,8BAAAA,kEAA4B;YAC5B3B;YACAC;YACAC;YACAG;QACF;IACF;IAEA,OAAO,MAAMuB,IAAAA,uBAAgB,EAAC;QAC5B7B,MAAMN;QACNiB,gBAAgBF,WAAWE,cAAc;QACzCe,gBAAgB/B,OAAO+B,cAAc;QACrC7B;QACAC;IACF;AACF"}