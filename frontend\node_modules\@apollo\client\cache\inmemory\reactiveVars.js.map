{"version": 3, "file": "reactiveVars.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/reactiveVars.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAarC,yEAAyE;AACzE,gCAAgC;AAChC,MAAM,CAAC,IAAM,SAAS,GAAG,IAAI,IAAI,EAAoB,CAAC;AAEtD,IAAM,YAAY,GAAG,IAAI,OAAO,EAM7B,CAAC;AAEJ,SAAS,YAAY,CAAC,KAAuB;IAC3C,IAAI,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;IACpC,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,YAAY,CAAC,GAAG,CACd,KAAK,EACL,CAAC,IAAI,GAAG;YACN,IAAI,EAAE,IAAI,GAAG,EAAE;YACf,GAAG,EAAE,GAAG,EAAE;SACX,CAAC,CACH,CAAC;IACJ,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAuB;IACjD,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,EAAE,IAAK,OAAA,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAArB,CAAqB,CAAC,CAAC;AAClE,CAAC;AAED,yEAAyE;AACzE,kEAAkE;AAClE,0EAA0E;AAC1E,sEAAsE;AACtE,0EAA0E;AAC1E,wEAAwE;AACxE,mEAAmE;AACnE,wDAAwD;AACxD,MAAM,UAAU,WAAW,CAAC,KAAuB;IACjD,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAC,EAAE,IAAK,OAAA,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAArB,CAAqB,CAAC,CAAC;AAClE,CAAC;AAED,MAAM,UAAU,OAAO,CAAI,KAAQ;IACjC,IAAM,MAAM,GAAG,IAAI,GAAG,EAAoB,CAAC;IAC3C,IAAM,SAAS,GAAG,IAAI,GAAG,EAAuB,CAAC;IAEjD,IAAM,EAAE,GAAmB,UAAU,QAAQ;QAC3C,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvB,KAAK,GAAG,QAAS,CAAC;gBAClB,MAAM,CAAC,OAAO,CAAC,UAAC,KAAK;oBACnB,wDAAwD;oBACxD,2DAA2D;oBAC3D,wDAAwD;oBACxD,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;oBAClC,4DAA4D;oBAC5D,sBAAsB;oBACtB,SAAS,CAAC,KAAK,CAAC,CAAC;gBACnB,CAAC,CAAC,CAAC;gBACH,2DAA2D;gBAC3D,IAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC3C,SAAS,CAAC,KAAK,EAAE,CAAC;gBAClB,YAAY,CAAC,OAAO,CAAC,UAAC,QAAQ,IAAK,OAAA,QAAQ,CAAC,KAAK,CAAC,EAAf,CAAe,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,gEAAgE;YAChE,iEAAiE;YACjE,sCAAsC;YACtC,IAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;YACnC,IAAI,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,KAAK,CAAC,CAAC;gBACd,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,EAAE,CAAC,YAAY,GAAG,UAAC,QAAQ;QACzB,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxB,OAAO;YACL,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,IAAM,MAAM,GAAG,CAAC,EAAE,CAAC,WAAW,GAAG,UAAC,KAAK;QACrC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAClB,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACjC,OAAO,EAAE,CAAC;IACZ,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,WAAW,GAAG,UAAC,KAAK,IAAK,OAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAApB,CAAoB,CAAC;IAEjD,OAAO,EAAE,CAAC;AACZ,CAAC;AAQD,SAAS,SAAS,CAAC,KAAoB;IACrC,IAAI,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC3B,KAAK,CAAC,gBAAgB,EAAE,CAAC;IAC3B,CAAC;AACH,CAAC", "sourcesContent": ["import type { OptimisticDependencyFunction } from \"optimism\";\nimport { dep, Slot } from \"optimism\";\nimport type { InMemoryCache } from \"./inMemoryCache.js\";\nimport type { ApolloCache } from \"../../core/index.js\";\n\nexport interface ReactiveVar<T> {\n  (newValue?: T): T;\n  onNextChange(listener: ReactiveListener<T>): () => void;\n  attachCache(cache: ApolloCache<any>): this;\n  forgetCache(cache: ApolloCache<any>): boolean;\n}\n\nexport type ReactiveListener<T> = (value: T) => any;\n\n// Contextual Slot that acquires its value when custom read functions are\n// called in Policies#readField.\nexport const cacheSlot = new Slot<ApolloCache<any>>();\n\nconst cacheInfoMap = new WeakMap<\n  ApolloCache<any>,\n  {\n    vars: Set<ReactiveVar<any>>;\n    dep: OptimisticDependencyFunction<ReactiveVar<any>>;\n  }\n>();\n\nfunction getCacheInfo(cache: ApolloCache<any>) {\n  let info = cacheInfoMap.get(cache)!;\n  if (!info) {\n    cacheInfoMap.set(\n      cache,\n      (info = {\n        vars: new Set(),\n        dep: dep(),\n      })\n    );\n  }\n  return info;\n}\n\nexport function forgetCache(cache: ApolloCache<any>) {\n  getCacheInfo(cache).vars.forEach((rv) => rv.forgetCache(cache));\n}\n\n// Calling forgetCache(cache) serves to silence broadcasts and allows the\n// cache to be garbage collected. However, the varsByCache WeakMap\n// preserves the set of reactive variables that were previously associated\n// with this cache, which makes it possible to \"recall\" the cache at a\n// later time, by reattaching it to those variables. If the cache has been\n// garbage collected in the meantime, because it is no longer reachable,\n// you won't be able to call recallCache(cache), and the cache will\n// automatically disappear from the varsByCache WeakMap.\nexport function recallCache(cache: ApolloCache<any>) {\n  getCacheInfo(cache).vars.forEach((rv) => rv.attachCache(cache));\n}\n\nexport function makeVar<T>(value: T): ReactiveVar<T> {\n  const caches = new Set<ApolloCache<any>>();\n  const listeners = new Set<ReactiveListener<T>>();\n\n  const rv: ReactiveVar<T> = function (newValue) {\n    if (arguments.length > 0) {\n      if (value !== newValue) {\n        value = newValue!;\n        caches.forEach((cache) => {\n          // Invalidate any fields with custom read functions that\n          // consumed this variable, so query results involving those\n          // fields will be recomputed the next time we read them.\n          getCacheInfo(cache).dep.dirty(rv);\n          // Broadcast changes to any caches that have previously read\n          // from this variable.\n          broadcast(cache);\n        });\n        // Finally, notify any listeners added via rv.onNextChange.\n        const oldListeners = Array.from(listeners);\n        listeners.clear();\n        oldListeners.forEach((listener) => listener(value));\n      }\n    } else {\n      // When reading from the variable, obtain the current cache from\n      // context via cacheSlot. This isn't entirely foolproof, but it's\n      // the same system that powers varDep.\n      const cache = cacheSlot.getValue();\n      if (cache) {\n        attach(cache);\n        getCacheInfo(cache).dep(rv);\n      }\n    }\n\n    return value;\n  };\n\n  rv.onNextChange = (listener) => {\n    listeners.add(listener);\n    return () => {\n      listeners.delete(listener);\n    };\n  };\n\n  const attach = (rv.attachCache = (cache) => {\n    caches.add(cache);\n    getCacheInfo(cache).vars.add(rv);\n    return rv;\n  });\n\n  rv.forgetCache = (cache) => caches.delete(cache);\n\n  return rv;\n}\n\ntype Broadcastable = ApolloCache<any> & {\n  // This method is protected in InMemoryCache, which we are ignoring, but\n  // we still want some semblance of type safety when we call it.\n  broadcastWatches?: InMemoryCache[\"broadcastWatches\"];\n};\n\nfunction broadcast(cache: Broadcastable) {\n  if (cache.broadcastWatches) {\n    cache.broadcastWatches();\n  }\n}\n"]}