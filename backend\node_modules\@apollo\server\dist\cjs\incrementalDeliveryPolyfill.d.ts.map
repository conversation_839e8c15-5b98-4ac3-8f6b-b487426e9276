{"version": 3, "file": "incrementalDeliveryPolyfill.d.ts", "sourceRoot": "", "sources": ["../../src/incrementalDeliveryPolyfill.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,KAAK,aAAa,EAClB,KAAK,eAAe,EACpB,KAAK,YAAY,EAClB,MAAM,SAAS,CAAC;AAOjB,UAAU,MAAM,CAAC,CAAC;IAChB,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC;CAClB;AACD,MAAM,WAAW,oDAAoD,CACnE,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,EACvB,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAC7B,SAAQ,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC;IAC3C,OAAO,EAAE,OAAO,CAAC;IACjB,WAAW,CAAC,EAAE,aAAa,CACzB,oCAAoC,CAAC,KAAK,EAAE,WAAW,CAAC,CACzD,CAAC;IACF,UAAU,CAAC,EAAE,WAAW,CAAC;CAC1B;AAED,MAAM,WAAW,uDAAuD,CACtE,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,EACvB,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;IAE7B,OAAO,EAAE,OAAO,CAAC;IACjB,WAAW,CAAC,EAAE,aAAa,CACzB,oCAAoC,CAAC,KAAK,EAAE,WAAW,CAAC,CACzD,CAAC;IACF,UAAU,CAAC,EAAE,WAAW,CAAC;CAC1B;AAED,KAAK,oCAAoC,CACvC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,EACvB,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,IAE3B,yCAAyC,CAAC,KAAK,EAAE,WAAW,CAAC,GAC7D,0CAA0C,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AAEnE,UAAU,yCAAyC,CACjD,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,EACvB,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAC7B,SAAQ,eAAe,CAAC,KAAK,EAAE,WAAW,CAAC;IAC3C,IAAI,CAAC,EAAE,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IACtC,KAAK,CAAC,EAAE,MAAM,CAAC;CAChB;AAED,UAAU,0CAA0C,CAClD,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,EACtB,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;IAE7B,MAAM,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;IACrC,KAAK,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC;IACrB,IAAI,CAAC,EAAE,aAAa,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;IACtC,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,UAAU,CAAC,EAAE,WAAW,CAAC;CAC1B;AAED,MAAM,WAAW,8CAA8C,CAC7D,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,EACvB,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;IAE7B,aAAa,EAAE,oDAAoD,CACjE,KAAK,EACL,WAAW,CACZ,CAAC;IACF,iBAAiB,EAAE,cAAc,CAC/B,uDAAuD,CAAC,KAAK,EAAE,WAAW,CAAC,EAC3E,IAAI,EACJ,IAAI,CACL,CAAC;CACH;AA8BD,wBAAsB,oBAAoB,CACxC,IAAI,EAAE,aAAa,GAClB,OAAO,CAAC,eAAe,GAAG,8CAA8C,CAAC,CAM3E"}