{"version": 3, "sources": ["../../../../../../src/server/future/route-modules/app-route/helpers/resolve-handler-error.ts"], "names": ["getRedirectStatusCodeFromError", "isNotFoundError", "getURLFromRedirectError", "isRedirectError", "handleNotFoundResponse", "handleRedirectResponse", "resolveHandlerError", "err", "redirect", "Error", "status", "mutableCookies"], "mappings": "AAAA,SAASA,8BAA8B,QAAQ,uEAAsE;AACrH,SAASC,eAAe,QAAQ,6CAA4C;AAC5E,SACEC,uBAAuB,EACvBC,eAAe,QACV,4CAA2C;AAClD,SACEC,sBAAsB,EACtBC,sBAAsB,QACjB,kCAAiC;AAExC,OAAO,SAASC,oBAAoBC,GAAQ;IAC1C,IAAIJ,gBAAgBI,MAAM;QACxB,MAAMC,WAAWN,wBAAwBK;QACzC,IAAI,CAACC,UAAU;YACb,MAAM,IAAIC,MAAM;QAClB;QAEA,MAAMC,SAASV,+BAA+BO;QAE9C,wDAAwD;QACxD,OAAOF,uBAAuBG,UAAUD,IAAII,cAAc,EAAED;IAC9D;IAEA,IAAIT,gBAAgBM,MAAM;QACxB,0DAA0D;QAC1D,OAAOH;IACT;IAEA,6DAA6D;IAC7D,OAAO;AACT"}