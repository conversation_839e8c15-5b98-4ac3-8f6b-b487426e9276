export declare const resolvers: {
    Query: {
        courses: (_: any, __: any, { prisma }: import("../../context").Context) => Promise<({
            instructor: {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
            modules: ({
                lessons: {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    title: string;
                    content: string;
                    type: string;
                    duration: number;
                    order: number;
                    moduleId: string;
                }[];
            } & {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                courseId: string;
                title: string;
                description: string;
                order: number;
            })[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            level: string;
            title: string;
            description: string;
            instructorId: string;
        })[]>;
        course: (_: any, args: any, { prisma }: import("../../context").Context) => Promise<({
            instructor: {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
            modules: ({
                lessons: ({
                    quizzes: ({
                        questions: {
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            type: string;
                            text: string;
                            options: string | null;
                            correctAnswer: string;
                            explanation: string | null;
                            quizId: string;
                        }[];
                    } & {
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        title: string;
                        lessonId: string;
                        timeLimit: number | null;
                    })[];
                } & {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    title: string;
                    content: string;
                    type: string;
                    duration: number;
                    order: number;
                    moduleId: string;
                })[];
            } & {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                courseId: string;
                title: string;
                description: string;
                order: number;
            })[];
            students: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                courseId: string;
                completedLessons: string | null;
                currentModule: string | null;
                score: number | null;
                lastAccessed: Date;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            level: string;
            title: string;
            description: string;
            instructorId: string;
        }) | null>;
        myProgress: (_: any, __: any, { prisma, user }: import("../../context").Context) => Promise<({
            course: {
                instructor: {
                    id: string;
                    email: string;
                    password: string;
                    name: string;
                    role: string;
                    createdAt: Date;
                    updatedAt: Date;
                };
                modules: ({
                    lessons: {
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        title: string;
                        content: string;
                        type: string;
                        duration: number;
                        order: number;
                        moduleId: string;
                    }[];
                } & {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    courseId: string;
                    title: string;
                    description: string;
                    order: number;
                })[];
            } & {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                level: string;
                title: string;
                description: string;
                instructorId: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            courseId: string;
            completedLessons: string | null;
            currentModule: string | null;
            score: number | null;
            lastAccessed: Date;
        })[]>;
        me: (_: any, __: any, { prisma, user }: import("../../context").Context) => Promise<({
            profile: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                avatar: string | null;
                bio: string | null;
                level: string;
                xp: number;
            } | null;
            enrolledCourses: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                courseId: string;
                completedLessons: string | null;
                currentModule: string | null;
                score: number | null;
                lastAccessed: Date;
            }[];
        } & {
            id: string;
            email: string;
            password: string;
            name: string;
            role: string;
            createdAt: Date;
            updatedAt: Date;
        }) | null>;
        leaderboard: (_: any, __: any, { prisma }: import("../../context").Context) => Promise<({
            profile: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                avatar: string | null;
                bio: string | null;
                level: string;
                xp: number;
            } | null;
        } & {
            id: string;
            email: string;
            password: string;
            name: string;
            role: string;
            createdAt: Date;
            updatedAt: Date;
        })[]>;
    };
    Mutation: {
        submitQuiz: (_: any, args: any, { prisma, user }: import("../../context").Context) => Promise<{
            score: number;
            totalQuestions: number;
            correctAnswers: number;
            feedback: {
                questionId: string;
                correct: boolean;
                explanation: string | null;
            }[];
        }>;
        enrollCourse: (_: any, args: any, { prisma, user }: import("../../context").Context) => Promise<{
            user: {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
            course: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                level: string;
                title: string;
                description: string;
                instructorId: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            courseId: string;
            completedLessons: string | null;
            currentModule: string | null;
            score: number | null;
            lastAccessed: Date;
        }>;
        completeLesson: (_: any, args: any, { prisma, user }: import("../../context").Context) => Promise<{
            user: {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
            course: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                level: string;
                title: string;
                description: string;
                instructorId: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            courseId: string;
            completedLessons: string | null;
            currentModule: string | null;
            score: number | null;
            lastAccessed: Date;
        }>;
        register: (_: any, args: any, { prisma }: import("../../context").Context) => Promise<{
            token: string;
            user: {
                profile: {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    userId: string;
                    avatar: string | null;
                    bio: string | null;
                    level: string;
                    xp: number;
                } | null;
            } & {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
        }>;
        login: (_: any, args: any, { prisma }: import("../../context").Context) => Promise<{
            token: string;
            user: {
                profile: {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    userId: string;
                    avatar: string | null;
                    bio: string | null;
                    level: string;
                    xp: number;
                } | null;
            } & {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
        }>;
        updateProfile: (_: any, args: any, { prisma, user }: import("../../context").Context) => Promise<{
            profile: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                avatar: string | null;
                bio: string | null;
                level: string;
                xp: number;
            } | null;
        } & {
            id: string;
            email: string;
            password: string;
            name: string;
            role: string;
            createdAt: Date;
            updatedAt: Date;
        }>;
    };
    User: {
        progress: (parent: any, _: any, { prisma }: import("../../context").Context) => Promise<({
            course: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                level: string;
                title: string;
                description: string;
                instructorId: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            courseId: string;
            completedLessons: string | null;
            currentModule: string | null;
            score: number | null;
            lastAccessed: Date;
        })[]>;
    };
    Course: {
        students: (parent: any, _: any, { prisma }: import("../../context").Context) => Promise<{
            id: string;
            email: string;
            password: string;
            name: string;
            role: string;
            createdAt: Date;
            updatedAt: Date;
        }[]>;
    };
    Lesson: {
        quizzes: (parent: any, _: any, { prisma }: import("../../context").Context) => Promise<({
            questions: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                type: string;
                text: string;
                options: string | null;
                correctAnswer: string;
                explanation: string | null;
                quizId: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            title: string;
            lessonId: string;
            timeLimit: number | null;
        })[]>;
    };
};
//# sourceMappingURL=index.d.ts.map