{"version": 3, "file": "operationDerivedDataCache.js", "sourceRoot": "", "sources": ["../../../../src/plugin/usageReporting/operationDerivedDataCache.ts"], "names": [], "mappings": ";;;;;;AAEA,0DAAiC;AAOjC,SAAgB,+BAA+B,CAAC,EAC9C,MAAM,GAGP;IACC,IAAI,QAAc,CAAC;IACnB,IAAI,aAAa,GAAG,CAAC,CAAC;IACtB,OAAO,IAAI,mBAAQ,CAA+B;QAEhD,eAAe,CAAC,GAAG;YACjB,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;QACxD,CAAC;QASD,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;QAC7B,OAAO;YAEL,aAAa,EAAE,CAAC;YAGhB,IAAI,CAAC,QAAQ,IAAI,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,OAAO,EAAE,GAAG,KAAK,EAAE,CAAC;gBAEnE,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;gBACtB,MAAM,CAAC,IAAI,CACT;oBACE,iEAAiE;oBACjE,cAAc,aAAa,qBAAqB;oBAChD,gFAAgF;oBAChF,gEAAgE;oBAChE,wCAAwC;iBACzC,CAAC,IAAI,CAAC,EAAE,CAAC,CACX,CAAC;gBAGF,aAAa,GAAG,CAAC,CAAC;YACpB,CAAC;QACH,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AA5CD,0EA4CC;AAED,SAAgB,4BAA4B,CAC1C,SAAiB,EACjB,aAAqB;IAErB,OAAO,GAAG,SAAS,GAAG,aAAa,IAAI,GAAG,GAAG,aAAa,EAAE,CAAC;AAC/D,CAAC;AALD,oEAKC"}