{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/plugin/schemaReporting/index.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAoB;AACpB,+DAAyD;AACzD,+BAAoC;AACpC,qCAAmE;AACnE,2DAAqD;AACrD,gEAA0D;AAI1D,yEAAmE;AACnE,mFAA6E;AAkD7E,SAAgB,iCAAiC,CAC/C,EACE,iBAAiB,EACjB,sBAAsB,EACtB,WAAW,EACX,OAAO,MACqC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAEjE,MAAM,MAAM,GAAG,IAAA,SAAM,GAAE,CAAC;IAExB,OAAO,IAAA,kCAAc,EAAC;QACpB,sBAAsB,EAAE,iBAAiB;QACzC,sBAAsB,EAAE,KAAK;QAC7B,KAAK,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;YAC9C,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC;YACjC,IAAI,CAAC,GAAG,EAAE,CAAC;gBACT,MAAM,KAAK,CACT,2EAA2E;oBACzE,wFAAwF,CAC3F,CAAC;YACJ,CAAC;YACD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAGd,MAAM,KAAK,CACT,iFAAiF;oBAC/E,gFAAgF;oBAChF,+DAA+D,CAClE,CAAC;YACJ,CAAC;YAGD,IAAI,sBAAsB,EAAE,CAAC;gBAC3B,IAAI,CAAC;oBACH,MAAM,gBAAgB,GAAG,IAAA,wBAAc,EACrC,IAAA,qBAAW,EAAC,sBAAsB,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAC1D,CAAC;oBACF,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC;wBAC5B,MAAM,IAAI,KAAK,CACb,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC1D,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACb,MAAM,IAAI,KAAK,CACb,mEAAmE;wBACjE,aAAc,GAAa,CAAC,OAAO,EAAE,CACxC,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,IAAI,IAAA,sCAAgB,EAAC,MAAM,CAAC,EAAE,CAAC;gBAC7B,MAAM,KAAK,CACT;oBACE,0EAA0E;oBAC1E,gEAAgE;oBAChE,kEAAkE;oBAClE,4CAA4C;iBAC7C,CAAC,IAAI,CAAC,GAAG,CAAC,CACZ,CAAC;YACJ,CAAC;YAED,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC9B,MAAM,CAAC,IAAI,CACT,2DAA2D,WAAW,EAAE,CACzE,CAAC;YACJ,CAAC;YAED,MAAM,gBAAgB,GAAyC;gBAC7D,MAAM;gBACN,QAAQ;gBAGR,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,OAAO;gBACvD,cAAc,EAAE,QAAQ,OAAO,CAAC,OAAO,EAAE;gBAGzC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,0BAA0B;gBAEnD,QAAQ,EACN,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,YAAE,CAAC,QAAQ,EAAE;gBACvE,cAAc,EAAE,kBAAkB,kCAAc,EAAE;aACnD,CAAC;YACF,IAAI,qBAAiD,CAAC;YAEtD,OAAO;gBACL,qBAAqB,CAAC,EAAE,SAAS,EAAE,iBAAiB,EAAE;oBACpD,IAAI,sBAAsB,KAAK,SAAS,EAAE,CAAC;wBACzC,IAAI,qBAAqB,EAAE,CAAC;4BAG1B,OAAO;wBACT,CAAC;6BAAM,CAAC;4BACN,MAAM,CAAC,IAAI,CACT,+DAA+D,CAChE,CAAC;wBACJ,CAAC;oBACH,CAAC;oBAED,MAAM,UAAU,GACd,sBAAsB;wBACtB,iBAAiB;wBACjB,IAAA,qBAAW,EAAC,SAAS,CAAC,CAAC;oBACzB,MAAM,cAAc,GAAG,IAAA,gDAAqB,EAAC,UAAU,CAAC,CAAC;oBACzD,MAAM,YAAY,GAAiB;wBACjC,GAAG,gBAAgB;wBACnB,cAAc;qBACf,CAAC;oBAEF,qBAAqB,EAAE,IAAI,EAAE,CAAC;oBAC9B,qBAAqB,GAAG,IAAI,kCAAc,CAAC;wBACzC,YAAY;wBACZ,UAAU;wBACV,MAAM,EAAE,GAAG;wBACX,WAAW;wBACX,MAAM;wBAEN,yBAAyB,EAAE,IAAI,CAAC,KAAK,CACnC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,iBAAiB,IAAI,KAAM,CAAC,CAC9C;wBACD,0BAA0B,EAAE,KAAM;wBAClC,OAAO;qBACR,CAAC,CAAC;oBACH,qBAAqB,CAAC,KAAK,EAAE,CAAC;oBAE9B,MAAM,CAAC,IAAI,CACT,+EAA+E;wBAC7E,0CAA0C,SAAS,CACjD,QAAQ,CACT,sBAAsB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CACxD,CAAC;gBACJ,CAAC;gBACD,KAAK,CAAC,cAAc;oBAClB,qBAAqB,EAAE,IAAI,EAAE,CAAC;gBAChC,CAAC;aACF,CAAC;QACJ,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAzID,8EAyIC"}