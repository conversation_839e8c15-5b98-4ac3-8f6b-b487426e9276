import type { ApolloServerPlugin } from '../../../externalTypes/index.js';
import type { ApolloServerPluginLandingPageLocalDefaultOptions, ApolloServerPluginLandingPageProductionDefaultOptions } from './types.js';
export type { ApolloServerPluginLandingPageLocalDefaultOptions, ApolloServerPluginLandingPageProductionDefaultOptions, };
export declare function ApolloServerPluginLandingPageLocalDefault(options?: ApolloServerPluginLandingPageLocalDefaultOptions): ApolloServerPlugin;
export declare function ApolloServerPluginLandingPageProductionDefault(options?: ApolloServerPluginLandingPageProductionDefaultOptions): ApolloServerPlugin;
export declare const DEFAULT_EMBEDDED_EXPLORER_VERSION = "v3";
export declare const DEFAULT_EMBEDDED_SANDBOX_VERSION = "v2";
export declare const DEFAULT_APOLLO_SERVER_LANDING_PAGE_VERSION = "_latest";
//# sourceMappingURL=index.d.ts.map