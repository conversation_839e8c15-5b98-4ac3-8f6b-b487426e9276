import { Context } from '../../context';
export declare const courseResolvers: {
    Query: {
        courses: (_: any, __: any, { prisma }: Context) => Promise<({
            instructor: {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
            modules: ({
                lessons: {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    title: string;
                    content: string;
                    type: string;
                    duration: number;
                    order: number;
                    moduleId: string;
                }[];
            } & {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                courseId: string;
                title: string;
                description: string;
                order: number;
            })[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            level: string;
            title: string;
            description: string;
            instructorId: string;
        })[]>;
        course: (_: any, args: any, { prisma }: Context) => Promise<({
            instructor: {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
            modules: ({
                lessons: ({
                    quizzes: ({
                        questions: {
                            id: string;
                            createdAt: Date;
                            updatedAt: Date;
                            type: string;
                            text: string;
                            options: string | null;
                            correctAnswer: string;
                            explanation: string | null;
                            quizId: string;
                        }[];
                    } & {
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        title: string;
                        lessonId: string;
                        timeLimit: number | null;
                    })[];
                } & {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    title: string;
                    content: string;
                    type: string;
                    duration: number;
                    order: number;
                    moduleId: string;
                })[];
            } & {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                courseId: string;
                title: string;
                description: string;
                order: number;
            })[];
            students: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                userId: string;
                courseId: string;
                completedLessons: string | null;
                currentModule: string | null;
                score: number | null;
                lastAccessed: Date;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            level: string;
            title: string;
            description: string;
            instructorId: string;
        }) | null>;
        myProgress: (_: any, __: any, { prisma, user }: Context) => Promise<({
            course: {
                instructor: {
                    id: string;
                    email: string;
                    password: string;
                    name: string;
                    role: string;
                    createdAt: Date;
                    updatedAt: Date;
                };
                modules: ({
                    lessons: {
                        id: string;
                        createdAt: Date;
                        updatedAt: Date;
                        title: string;
                        content: string;
                        type: string;
                        duration: number;
                        order: number;
                        moduleId: string;
                    }[];
                } & {
                    id: string;
                    createdAt: Date;
                    updatedAt: Date;
                    courseId: string;
                    title: string;
                    description: string;
                    order: number;
                })[];
            } & {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                level: string;
                title: string;
                description: string;
                instructorId: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            courseId: string;
            completedLessons: string | null;
            currentModule: string | null;
            score: number | null;
            lastAccessed: Date;
        })[]>;
    };
    Mutation: {
        enrollCourse: (_: any, args: any, { prisma, user }: Context) => Promise<{
            user: {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
            course: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                level: string;
                title: string;
                description: string;
                instructorId: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            courseId: string;
            completedLessons: string | null;
            currentModule: string | null;
            score: number | null;
            lastAccessed: Date;
        }>;
        completeLesson: (_: any, args: any, { prisma, user }: Context) => Promise<{
            user: {
                id: string;
                email: string;
                password: string;
                name: string;
                role: string;
                createdAt: Date;
                updatedAt: Date;
            };
            course: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                level: string;
                title: string;
                description: string;
                instructorId: string;
            };
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            userId: string;
            courseId: string;
            completedLessons: string | null;
            currentModule: string | null;
            score: number | null;
            lastAccessed: Date;
        }>;
    };
    Course: {
        students: (parent: any, _: any, { prisma }: Context) => Promise<{
            id: string;
            email: string;
            password: string;
            name: string;
            role: string;
            createdAt: Date;
            updatedAt: Date;
        }[]>;
    };
};
//# sourceMappingURL=course.d.ts.map