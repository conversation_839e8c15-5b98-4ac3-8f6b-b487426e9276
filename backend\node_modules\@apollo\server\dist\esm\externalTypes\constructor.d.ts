import type { Logger } from '@apollo/utils.logger';
import type { IExecutableSchemaDefinition } from '@graphql-tools/schema';
import type { DocumentNode, FormattedExecutionResult, GraphQLFieldResolver, GraphQLFormattedError, GraphQLSchema, ParseOptions, ValidationRule } from 'graphql';
import type { KeyValueCache } from '@apollo/utils.keyvaluecache';
import type { GatewayInterface } from '@apollo/server-gateway-interface';
import type { ApolloServerPlugin } from './plugins.js';
import type { BaseContext } from './index.js';
import type { GraphQLExperimentalIncrementalExecutionResults } from '../incrementalDeliveryPolyfill.js';
export type DocumentStore = KeyValueCache<DocumentNode>;
export interface ApolloConfigInput {
    key?: string;
    graphRef?: string;
    graphId?: string;
    graphVariant?: string;
}
export interface ApolloConfig {
    key?: string;
    keyHash?: string;
    graphRef?: string;
}
export interface PersistedQueryOptions {
    cache?: KeyValueCache<string>;
    ttl?: number | null;
}
export interface CSRFPreventionOptions {
    requestHeaders?: string[];
}
interface ApolloServerOptionsBase<TContext extends BaseContext> {
    formatError?: (formattedError: GraphQLFormattedError, error: unknown) => GraphQLFormattedError;
    rootValue?: ((parsedQuery: DocumentNode) => unknown) | unknown;
    validationRules?: Array<ValidationRule>;
    fieldResolver?: GraphQLFieldResolver<any, TContext>;
    cache?: KeyValueCache<string> | 'bounded';
    includeStacktraceInErrorResponses?: boolean;
    logger?: Logger;
    allowBatchedHttpRequests?: boolean;
    stringifyResult?: (value: FormattedExecutionResult) => string | Promise<string>;
    introspection?: boolean;
    maxRecursiveSelections?: boolean | number;
    hideSchemaDetailsFromClientErrors?: boolean;
    plugins?: ApolloServerPlugin<TContext>[];
    persistedQueries?: PersistedQueryOptions | false;
    stopOnTerminationSignals?: boolean;
    apollo?: ApolloConfigInput;
    nodeEnv?: string;
    documentStore?: DocumentStore | null;
    dangerouslyDisableValidation?: boolean;
    csrfPrevention?: CSRFPreventionOptions | boolean;
    parseOptions?: ParseOptions;
    status400ForVariableCoercionErrors?: boolean;
    __testing_incrementalExecutionResults?: GraphQLExperimentalIncrementalExecutionResults;
}
export interface ApolloServerOptionsWithGateway<TContext extends BaseContext> extends ApolloServerOptionsBase<TContext> {
    gateway: GatewayInterface;
    schema?: undefined;
    typeDefs?: undefined;
    resolvers?: undefined;
}
export interface ApolloServerOptionsWithSchema<TContext extends BaseContext> extends ApolloServerOptionsBase<TContext> {
    schema: GraphQLSchema;
    gateway?: undefined;
    typeDefs?: undefined;
    resolvers?: undefined;
}
export interface ApolloServerOptionsWithTypeDefs<TContext extends BaseContext> extends ApolloServerOptionsBase<TContext> {
    typeDefs: IExecutableSchemaDefinition<TContext>['typeDefs'];
    resolvers?: IExecutableSchemaDefinition<TContext>['resolvers'];
    gateway?: undefined;
    schema?: undefined;
}
export type ApolloServerOptionsWithStaticSchema<TContext extends BaseContext> = ApolloServerOptionsWithSchema<TContext> | ApolloServerOptionsWithTypeDefs<TContext>;
export type ApolloServerOptions<TContext extends BaseContext> = ApolloServerOptionsWithGateway<TContext> | ApolloServerOptionsWithStaticSchema<TContext>;
export {};
//# sourceMappingURL=constructor.d.ts.map