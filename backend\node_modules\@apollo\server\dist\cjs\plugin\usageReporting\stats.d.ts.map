{"version": 3, "file": "stats.d.ts", "sourceRoot": "", "sources": ["../../../../src/plugin/usageReporting/stats.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACzE,OAAO,EACL,KAAK,MAAM,EACX,KAAK,oBAAoB,EACzB,KAAK,UAAU,EACf,KAAK,eAAe,EACpB,KAAK,kBAAkB,EACvB,KAAK,OAAO,EACZ,KAAK,aAAa,EAClB,KAAK,eAAe,EACpB,KAAK,SAAS,EACd,KAAK,YAAY,EACjB,KAAK,EACN,MAAM,kCAAkC,CAAC;AAC1C,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AAC3E,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAkB3D,qBAAa,aAAa;IACxB,KAAK,SAAK;CACX;AACD,qBAAa,SAAU,YAAW,QAAQ,CAAC,OAAO,CAAC;IAOrC,QAAQ,CAAC,MAAM,EAAE,YAAY;IAFzC,mBAAmB,UAAS;gBAEP,MAAM,EAAE,YAAY;IACzC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CACpC;IACtB,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAQ;IAClD,cAAc,SAAK;IAUnB,QAAQ,CAAC,aAAa,gBAAuB;IAE7C,uBAAuB;IAMvB,QAAQ,CAAC,EACP,cAAc,EACd,KAAK,EACL,OAAO,EACP,sBAAsB,EAItB,aAAgC,EAChC,iBAAiB,GAClB,EAAE;QACD,cAAc,EAAE,MAAM,CAAC;QACvB,KAAK,EAAE,KAAK,CAAC;QACb,OAAO,EAAE,OAAO,CAAC;QACjB,sBAAsB,EAAE,sBAAsB,CAAC;QAC/C,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,iBAAiB,EAAE,gBAAgB,EAAE,CAAC;KACvC;IA2BD,OAAO,CAAC,iBAAiB;CAqC1B;AAED,cAAM,iBAAkB,YAAW,QAAQ,CAAC,eAAe,CAAC;IAC9C,QAAQ,CAAC,sBAAsB,EAAE,sBAAsB;gBAA9C,sBAAsB,EAAE,sBAAsB;IACnE,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,CAAM;IAClC,QAAQ,CAAC,gBAAgB,iBAAwB;IACjD,QAAQ,CAAC,iCAAiC,EAAE,UAAU,EAAE,CAAM;IAE9D,uBAAuB;CAGxB;AAED,cAAM,cAAc;IAClB,QAAQ,CAAC,GAAG,EAAE;QAAE,CAAC,CAAC,EAAE,MAAM,GAAG,sBAAsB,CAAA;KAAE,CAAuB;IAM5E,OAAO,IAAI,oBAAoB,EAAE;IAIjC,uBAAuB;IAMvB,QAAQ,CACN,KAAK,EAAE,KAAK,EACZ,aAAa,EAAE,aAAa,EAC5B,iBAAiB,EAAE,gBAAgB,EAAE;IASvC,OAAO,CAAC,sBAAsB;CAyB/B;AAED,qBAAa,sBAAuB,YAAW,QAAQ,CAAC,oBAAoB,CAAC;IAI/D,QAAQ,CAAC,OAAO,EAAE,aAAa;IAH3C,iBAAiB,uBAA8B;IAC/C,WAAW,EAAE;QAAE,CAAC,CAAC,EAAE,MAAM,GAAG,WAAW,CAAA;KAAE,CAAuB;gBAE3C,OAAO,EAAE,aAAa;IAE3C,uBAAuB;IAUvB,QAAQ,CACN,KAAK,EAAE,KAAK,EACZ,aAAa,EAAE,aAAa,EAC5B,iBAAiB,GAAE,gBAAgB,EAAO;IA+J5C,WAAW,CAAC,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,GAAG,WAAW;CAU3E;AAED,cAAM,oBAAqB,YAAW,QAAQ,CAAC,kBAAkB,CAAC;IAChE,YAAY,EAAE,iBAAiB,CAA2B;IAC1D,YAAY,SAAK;IACjB,mCAAmC,SAAK;IACxC,SAAS,SAAK;IACd,kBAAkB,SAAK;IACvB,oBAAoB,SAAK;IACzB,iBAAiB,EAAE,iBAAiB,CAA2B;IAC/D,cAAc,EAAE,iBAAiB,CAA2B;IAC5D,uBAAuB,SAAK;IAC5B,mBAAmB,EAAE,iBAAiB,CAA2B;IACjE,oBAAoB,EAAE,iBAAiB,CAA2B;IAClE,wBAAwB,SAAK;IAC7B,uBAAuB,SAAK;CAC7B;AAED,cAAM,iBAAkB,YAAW,QAAQ,CAAC,eAAe,CAAC;IAC1D,QAAQ,EAAE;QAAE,CAAC,CAAC,EAAE,MAAM,GAAG,iBAAiB,CAAA;KAAE,CAAuB;IACnE,WAAW,SAAK;IAChB,uBAAuB,SAAK;IAE5B,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,GAAG,iBAAiB;CAW3E;AAED,cAAM,WAAY,YAAW,QAAQ,CAAC,SAAS,CAAC;IAC9C,YAAY,EAAE;QAAE,CAAC,CAAC,EAAE,MAAM,GAAG,YAAY,CAAA;KAAE,CAAuB;IAElE,YAAY,CACV,SAAS,EAAE,MAAM,EACjB,UAAU,EAAE,MAAM,EAClB,aAAa,EAAE,aAAa,GAC3B,YAAY;IAef,uBAAuB;CAKxB;AAED,cAAM,YAAa,YAAW,QAAQ,CAAC,UAAU,CAAC;IAUpC,QAAQ,CAAC,UAAU,EAAE,MAAM;IATvC,WAAW,SAAK;IAChB,sBAAsB,SAAK;IAI3B,uBAAuB,SAAK;IAC5B,uBAAuB,SAAK;IAC5B,YAAY,EAAE,iBAAiB,CAA2B;gBAErC,UAAU,EAAE,MAAM;IAEvC,uBAAuB;CAIxB"}