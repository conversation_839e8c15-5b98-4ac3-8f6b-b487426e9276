{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/plugin/cacheControl/index.ts"], "names": [], "mappings": ";;;;;;AACA,qCASiB;AACjB,yDAAsD;AACtD,+DAAyD;AACzD,0DAAiC;AAyCjC,SAAgB,8BAA8B,CAC5C,UAAiD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAEpE,IAAI,mBAAoE,CAAC;IAEzE,IAAI,oBAGH,CAAC;IAEF,OAAO,IAAA,kCAAc,EAAC;QACpB,sBAAsB,EAAE,cAAc;QACtC,sBAAsB,EAAE,KAAK;QAE7B,KAAK,CAAC,eAAe,CAAC,EAAE,MAAM,EAAE;YAS9B,mBAAmB,GAAG,IAAI,mBAAQ,CAChC;gBACE,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,yBAAe,CAAC;qBAC5D,MAAM;aACV,CACF,CAAC;YA<PERSON>,oBAAoB,GAAG,IAAI,mBAAQ,CAGjC;gBACA,GAAG,EACD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;qBAC/B,MAAM,CAAC,sBAAY,CAAC;qBACpB,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,MAAM;oBACtD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;yBAC/B,MAAM,CAAC,yBAAe,CAAC;yBACvB,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,MAAM;aACzD,CAAC,CAAC;YAEH,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,KAAK,CAAC,eAAe,CAAC,cAAc;YAClC,SAAS,+BAA+B,CACtC,CAAuB;gBAEvB,MAAM,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC5C,IAAI,QAAQ,EAAE,CAAC;oBACb,OAAO,QAAQ,CAAC;gBAClB,CAAC;gBACD,MAAM,UAAU,GAAG,uBAAuB,CAAC,CAAC,CAAC,CAAC;gBAC9C,mBAAmB,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;gBACvC,OAAO,UAAU,CAAC;YACpB,CAAC;YAED,SAAS,gCAAgC,CACvC,KAAqC;gBAErC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACjD,IAAI,QAAQ,EAAE,CAAC;oBACb,OAAO,QAAQ,CAAC;gBAClB,CAAC;gBACD,MAAM,UAAU,GAAG,wBAAwB,CAAC,KAAK,CAAC,CAAC;gBACnD,oBAAoB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;gBAC5C,OAAO,UAAU,CAAC;YACpB,CAAC;YAED,MAAM,aAAa,GAAW,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;YACzD,MAAM,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,IAAI,CAAC;YAClE,MAAM,EAAE,qBAAqB,EAAE,GAAG,OAAO,CAAC;YAE1C,OAAO;gBACL,KAAK,CAAC,iBAAiB;oBAUrB,IAAI,YAAY,CAAC,cAAc,CAAC,kBAAkB,CAAC,EAAE,CAAC;wBAGpD,MAAM,eAAe,GAAG,IAAA,+BAAc,GAAE,CAAC;wBACzC,OAAO;4BACL,gBAAgB,CAAC,EAAE,IAAI,EAAE;gCAItB,IAA2C,CAAC,YAAY,GAAG;oCAC1D,YAAY,EAAE,CAAC,WAAsB,EAAE,EAAE;wCACvC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;oCACvC,CAAC;oCACD,SAAS,EAAE,eAAe;oCAC1B,iBAAiB,EAAE,+BAA+B;iCACnD,CAAC;4BACJ,CAAC;yBACF,CAAC;oBACJ,CAAC;oBAED,OAAO;wBACL,gBAAgB,CAAC,EAAE,IAAI,EAAE;4BACvB,MAAM,WAAW,GAAG,IAAA,+BAAc,GAAE,CAAC;4BAErC,IAAI,aAAa,GAAG,KAAK,CAAC;4BAK1B,MAAM,UAAU,GAAG,IAAA,sBAAY,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;4BACjD,IAAI,IAAA,yBAAe,EAAC,UAAU,CAAC,EAAE,CAAC;gCAChC,MAAM,cAAc,GAClB,+BAA+B,CAAC,UAAU,CAAC,CAAC;gCAC9C,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;gCACpC,aAAa,GAAG,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC;4BACjD,CAAC;4BAID,MAAM,eAAe,GAAG,gCAAgC,CACtD,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAC5C,CAAC;4BAMF,IACE,eAAe,CAAC,aAAa;gCAC7B,WAAW,CAAC,MAAM,KAAK,SAAS,EAChC,CAAC;gCACD,aAAa,GAAG,IAAI,CAAC;gCAIrB,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC;oCAC1B,WAAW,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC;gCACxD,CAAC;4BACH,CAAC;iCAAM,CAAC;gCACN,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;4BACvC,CAAC;4BAKA,IAA2C,CAAC,YAAY,GAAG;gCAC1D,YAAY,EAAE,CAAC,WAAsB,EAAE,EAAE;oCACvC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gCACnC,CAAC;gCACD,SAAS,EAAE,WAAW;gCACtB,iBAAiB,EAAE,+BAA+B;6BACnD,CAAC;4BAMF,OAAO,GAAG,EAAE;gCAsBV,IACE,WAAW,CAAC,MAAM,KAAK,SAAS;oCAChC,CAAC,CAAC,IAAA,yBAAe,EAAC,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC;wCAC9C,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAClB,CAAC;oCACD,WAAW,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC;gCAClD,CAAC;gCAED,IAAI,qBAAqB,IAAI,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC;oCACvD,MAAM,IAAI,GAAG,IAAA,6BAAmB,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oCACtD,IAAI,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;wCACpC,MAAM,KAAK,CACT,+DAA+D,CAChE,CAAC;oCACJ,CAAC;oCACD,qBAAqB,CAAC,GAAG,CAAC,IAAI,EAAE;wCAC9B,MAAM,EAAE,WAAW,CAAC,MAAM;wCAC1B,KAAK,EAAE,WAAW,CAAC,KAAK;qCACzB,CAAC,CAAC;gCACL,CAAC;gCACD,cAAc,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;4BAC1D,CAAC,CAAC;wBACJ,CAAC;qBACF,CAAC;gBACJ,CAAC;gBAED,KAAK,CAAC,gBAAgB,CAAC,cAAc;oBAGnC,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC1B,OAAO;oBACT,CAAC;oBAED,MAAM,EAAE,QAAQ,EAAE,kBAAkB,EAAE,GAAG,cAAc,CAAC;oBAMxD,MAAM,0BAA0B,GAAG,+BAA+B,CAChE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAC3C,CAAC;oBAOF,IAAI,0BAA0B,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;wBACrD,OAAO;oBACT,CAAC;oBAED,MAAM,WAAW,GAAG,IAAA,+BAAc,GAAE,CAAC;oBACrC,WAAW,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;oBACxC,IAAI,0BAA0B,CAAC,IAAI,KAAK,wBAAwB,EAAE,CAAC;wBACjE,WAAW,CAAC,QAAQ,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;oBACxD,CAAC;oBACD,MAAM,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,EAAE,CAAC;oBAE1D,IAEE,iBAAiB;wBAOjB,0BAA0B,CAAC,IAAI,KAAK,aAAa;wBAMjD,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ;wBAC/B,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAClC,CAAC;wBACD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CACvB,eAAe,EACf,WACE,iBAAiB,CAAC,MACpB,KAAK,iBAAiB,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAC7C,CAAC;oBACJ,CAAC;yBAAM,IAAI,oBAAoB,KAAK,cAAc,EAAE,CAAC;wBAMnD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CACvB,eAAe,EACf,gCAAgC,CACjC,CAAC;oBACJ,CAAC;gBACH,CAAC;aACF,CAAC;QACJ,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AA3RD,wEA2RC;AAED,MAAM,qCAAqC,GACzC,mCAAmC,CAAC;AACtC,MAAM,gCAAgC,GAAG,UAAU,CAAC;AAQpD,SAAS,+BAA+B,CACtC,MAA0B;IAE1B,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;IAC/B,CAAC;IACD,IAAI,MAAM,KAAK,gCAAgC,EAAE,CAAC;QAChD,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IACjC,CAAC;IACD,MAAM,KAAK,GAAG,qCAAqC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjE,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC;IAChC,CAAC;IACD,OAAO;QACL,IAAI,EAAE,wBAAwB;QAC9B,IAAI,EAAE;YACJ,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;YACjB,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;SACpD;KACF,CAAC;AACJ,CAAC;AAED,SAAS,6BAA6B,CACpC,UAAoD;IAEpD,IAAI,CAAC,UAAU;QAAE,OAAO,SAAS,CAAC;IAElC,MAAM,qBAAqB,GAAG,UAAU,CAAC,IAAI,CAC3C,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,cAAc,CACvD,CAAC;IACF,IAAI,CAAC,qBAAqB;QAAE,OAAO,SAAS,CAAC;IAE7C,IAAI,CAAC,qBAAqB,CAAC,SAAS;QAAE,OAAO,SAAS,CAAC;IAEvD,MAAM,cAAc,GAAG,qBAAqB,CAAC,SAAS,CAAC,IAAI,CACzD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAC/C,CAAC;IACF,MAAM,aAAa,GAAG,qBAAqB,CAAC,SAAS,CAAC,IAAI,CACxD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,OAAO,CAC9C,CAAC;IACF,MAAM,qBAAqB,GAAG,qBAAqB,CAAC,SAAS,CAAC,IAAI,CAChE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,eAAe,CACtD,CAAC;IAEF,MAAM,WAAW,GACf,aAAa,EAAE,KAAK,EAAE,IAAI,KAAK,WAAW;QACxC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK;QAC3B,CAAC,CAAC,SAAS,CAAC;IAEhB,MAAM,KAAK,GACT,WAAW,KAAK,QAAQ,IAAI,WAAW,KAAK,SAAS;QACnD,CAAC,CAAC,WAAW;QACb,CAAC,CAAC,SAAS,CAAC;IAEhB,IACE,qBAAqB,EAAE,KAAK,EAAE,IAAI,KAAK,cAAc;QACrD,qBAAqB,CAAC,KAAK,CAAC,KAAK,EACjC,CAAC;QAED,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IACxC,CAAC;IAED,OAAO;QACL,MAAM,EACJ,cAAc,EAAE,KAAK,EAAE,IAAI,KAAK,UAAU;YACxC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC;YACtC,CAAC,CAAC,SAAS;QACf,KAAK;KACN,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB,CAAC,CAAuB;IACtD,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;QACd,MAAM,IAAI,GAAG,6BAA6B,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACjE,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACxB,KAAK,MAAM,IAAI,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACvC,MAAM,IAAI,GAAG,6BAA6B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC5D,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,wBAAwB,CAC/B,KAAqC;IAErC,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QAClB,MAAM,IAAI,GAAG,6BAA6B,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACrE,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,YAAY,CAAC,IAAe;IACnC,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC;AAC/D,CAAC"}