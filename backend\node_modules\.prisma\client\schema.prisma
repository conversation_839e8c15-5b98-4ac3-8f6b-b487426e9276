// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id          Int       @id @default(autoincrement())
  email       String    @unique
  name        String?
  password    String
  role        String    @default("student") // student, instructor, admin
  avatar      String? // Profile picture URL
  isActive    Boolean   @default(true)
  lastLoginAt DateTime?
  timezone    String    @default("Africa/Algiers")
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  studentProfile         StudentProfile?
  learningPaths          LearningPath[]
  contentRecommendations ContentRecommendation[]
  quizAttempts           QuizAttempt[]
  achievements           UserAchievement[]
  studySessions          StudySession[]
  notifications          Notification[]
  portfolios             Portfolio[]
}

model StudentProfile {
  id                Int      @id @default(autoincrement())
  userId            Int      @unique
  user              User     @relation(fields: [userId], references: [id])
  learningStyle     String   @default("visual") // visual, auditory, kinesthetic, reading/writing
  pace              String   @default("medium") // slow, medium, fast
  strengths         String   @default("") // JSON string of array
  weaknesses        String   @default("") // JSON string of array
  preferredSubjects String   @default("") // JSON string of array
  // Gamification fields
  xp                Int      @default(0)
  level             String   @default("مبتدئ")
  achievements      String   @default("") // JSON string of array
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}

model LearningPath {
  id          Int      @id @default(autoincrement())
  studentId   Int
  student     User     @relation(fields: [studentId], references: [id])
  pathData    String   @default("") // Detailed JSON string object defining the learning path
  isCompleted Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model ContentRecommendation {
  id          Int      @id @default(autoincrement())
  studentId   Int
  student     User     @relation(fields: [studentId], references: [id])
  contentId   String // ID of the recommended content (e.g., article, video, quiz)
  contentType String // Type of content (article, video, quiz, etc.)
  isCompleted Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// --- New Models for Enhanced Features ---

// Gamification System
model Achievement {
  id          Int               @id @default(autoincrement())
  name        String            @unique
  description String
  icon        String
  criteria    String // JSON criteria to unlock
  users       UserAchievement[]
}

model UserAchievement {
  id            Int         @id @default(autoincrement())
  userId        Int
  user          User        @relation(fields: [userId], references: [id])
  achievementId Int
  achievement   Achievement @relation(fields: [achievementId], references: [id])
  unlockedAt    DateTime    @default(now())
}

// Quiz and Assessment System
model Quiz {
  id         Int           @id @default(autoincrement())
  title      String
  topic      String
  difficulty String
  questions  Question[]
  attempts   QuizAttempt[]
}

model Question {
  id            Int     @id @default(autoincrement())
  quizId        Int
  quiz          Quiz    @relation(fields: [quizId], references: [id])
  text          String
  type          String // MULTIPLE_CHOICE, TRUE_FALSE, SHORT_ANSWER
  options       String  @default("") // JSON string for options
  correctAnswer String
  explanation   String?
}

model QuizAttempt {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  quizId    Int
  quiz      Quiz     @relation(fields: [quizId], references: [id])
  score     Float
  answers   String   @default("") // JSON string of user answers
  startedAt DateTime @default(now())
  endedAt   DateTime @updatedAt
}

// Learning Analytics
model StudySession {
  id        Int       @id @default(autoincrement())
  userId    Int
  user      User      @relation(fields: [userId], references: [id])
  startTime DateTime  @default(now())
  endTime   DateTime?
  duration  Int? // in minutes
  topic     String?
}

// Notification System
model Notification {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  type      String // ACHIEVEMENT, RECOMMENDATION, REMINDER
  message   String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
}

// Financial Simulator
model Portfolio {
  id        Int      @id @default(autoincrement())
  userId    Int
  user      User     @relation(fields: [userId], references: [id])
  cash      Float    @default(100000) // Start with 100,000 DZD
  holdings  String   @default("") // JSON string of stock holdings
  history   String   @default("") // JSON string of portfolio history
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
