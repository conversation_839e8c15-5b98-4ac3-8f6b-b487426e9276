# منارة المحاسبة والمالية | Manara Accounting & Finance

نظام تعليمي ذكي متكامل للتمويل والمحاسبة في الجزائر

## Overview

This is an intelligent educational platform for finance and accounting in Algeria, built according to the Algerian LMD system. The platform incorporates advanced AI features for adaptive learning and includes specialized content for the Algerian financial system.

## Features

### 🧠 AI-Powered Learning
- Adaptive learning engine that customizes content based on student performance
- Intelligent recommendation system
- Smart notifications and reminders
- Automated question generation

### 📚 Comprehensive Curriculum
- Full LMD system coverage (L1, L2, L3)
- Specialized Algerian financial content (SCF, local regulations)
- Interactive case studies
- Virtual laboratories

### 🎮 Gamification
- Points and achievement system
- Progress levels and badges
- Weekly challenges
- Peer learning rewards

### 🏛️ Financial Simulators
- Algerian stock market simulator
- Business management simulator
- Advanced financial calculator
- Risk analysis tools

### 👥 Interactive Community
- Discussion forums
- Study groups
- Mentoring program
- Peer-to-peer learning

## Tech Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **React 18** - UI library
- **TypeScript 5** - Type safety
- **Tailwind CSS** - Styling
- **Shadcn UI** - Component library
- **Framer Motion** - Animations
- **Zustand** - State management
- **React Query** - Data fetching

### Backend
- **Node.js 20** - Runtime
- **Express.js** - Web framework
- **GraphQL** with Apollo Server - API
- **Prisma ORM** - Database management
- **PostgreSQL** - Database
- **Redis** - Caching
- **Socket.io** - Real-time features
- **JWT + OAuth2** - Authentication

### Infrastructure
- **Docker** - Containerization
- **AWS/Azure** - Cloud hosting
- **CDN** - Static content delivery
- **Elasticsearch** - Advanced search
- **Sentry + DataDog** - Monitoring

## Getting Started

### Prerequisites
- Node.js 20 or higher
- npm or yarn
- PostgreSQL database
- Redis server

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd manara-accounting-finance
```

2. Install backend dependencies:
```bash
cd backend
npm install
```

3. Install frontend dependencies:
```bash
cd ../frontend
npm install
```

4. Set up environment variables:
```bash
# Backend
cp .env.example .env
# Edit .env with your configuration

# Frontend
cp .env.local.example .env.local
# Edit .env.local with your configuration
```

5. Run database migrations:
```bash
cd backend
npx prisma migrate dev
```

6. Start the development servers:

Backend:
```bash
cd backend
npm run dev
```

Frontend:
```bash
cd frontend
npm run dev
```

The frontend will be available at `http://localhost:3000` and the backend GraphQL endpoint at `http://localhost:4000/graphql`.

## Project Structure

```
manara-accounting-finance/
├── backend/
│   ├── src/
│   │   ├── config/          # Configuration files
│   │   ├── graphql/         # GraphQL schemas and resolvers
│   │   ├── middleware/      # Express middleware
│   │   ├── models/          # Database models
│   │   ├── services/        # Business logic
│   │   └── utils/           # Utility functions
│   └── package.json
├── frontend/
│   ├── app/                 # Next.js app directory
│   ├── components/          # Reusable components
│   ├── lib/                 # Utility libraries
│   ├── public/              # Static assets
│   ├── styles/              # Global styles
│   └── package.json
└── README.md
```

## Key Features Implementation

### Adaptive Learning Engine
The platform uses AI to analyze student learning patterns and customize content delivery:
- Tracks learning speed and comprehension
- Identifies strengths and weaknesses
- Generates personalized learning paths
- Provides intelligent recommendations

### Algerian Financial System Integration
Specialized content for the Algerian market:
- SCF (Système Comptable Financier) standards
- Local tax calculations
- Algerian stock market data
- Banking and insurance regulations

### Multilingual Support
- Arabic (primary) with RTL support
- French for professional terms
- English for international standards
- Tamazight (basic support)

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Contact

For questions and support, please contact the development team.

---

تم تطوير هذا المشروع لخدمة التعليم المالي والمحاسبي في الجزائر 🇩🇿
