{"version": 3, "file": "schemaManager.js", "sourceRoot": "", "sources": ["../../../src/utils/schemaManager.ts"], "names": [], "mappings": ";;;AA8BA,MAAa,aAAa;IAwBxB,YACE,OAMC;QA5Bc,kCAA6B,GAAG,IAAI,GAAG,EAErD,CAAC;QACI,cAAS,GAAG,KAAK,CAAC;QA2BxB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,yBAAyB,GAAG,OAAO,CAAC,yBAAyB,CAAC;QACnE,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,IAAI,CAAC,iBAAiB,GAAG;gBACvB,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,YAAY,EAAE,OAAO,CAAC,YAAY;aACnC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,iBAAiB,GAAG;gBACvB,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,OAAO,CAAC,SAAS;gBAI5B,iBAAiB,EAAE,OAAO,CAAC,yBAAyB,CAAC,OAAO,CAAC,SAAS,CAAC;aACxE,CAAC;QACJ,CAAC;IACH,CAAC;IAUM,KAAK,CAAC,KAAK;QAChB,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAC/C,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC;gBAGjC,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;oBAC3C,OAAO,CAAC,oBAAoB,CAAC,CAAC,aAAa,EAAE,EAAE;wBAC7C,IAAI,CAAC,8BAA8B,CAAC,aAAa,CAAC,CAAC;oBACrD,CAAC,CAAC,CAAC;YACP,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,4DAA4D,CAC7D,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC;gBACvD,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY;aAC5C,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,QAAQ,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,8BAA8B,CACjC;gBACE,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,SAAS;aAC5C,EACD,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CACzC,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAgBM,oBAAoB,CACzB,QAAuD;QAEvD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC;gBACH,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC/B,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBAIX,MAAM,IAAI,KAAK,CACb,gEACG,CAAW,CAAC,OACf,EAAE,CACH,CAAC;YACJ,CAAC;QACH,CAAC;QACD,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEjD,OAAO,GAAG,EAAE;YACV,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACtD,CAAC,CAAC;IACJ,CAAC;IAMM,oBAAoB;QACzB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QACD,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAChC,CAAC;IASM,KAAK,CAAC,IAAI;QACf,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAClD,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,8BAA8B,CACpC,aAAmC,EACnC,iBAAqC;QAErC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,iBAAiB;gBACpB,iBAAiB;oBACjB,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAC1D,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;YACnC,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACtD,IAAI,CAAC;oBACH,QAAQ,CAAC,aAAa,CAAC,CAAC;gBAC1B,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,6DAA6D,CAC9D,CAAC;oBACF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AArLD,sCAqLC"}