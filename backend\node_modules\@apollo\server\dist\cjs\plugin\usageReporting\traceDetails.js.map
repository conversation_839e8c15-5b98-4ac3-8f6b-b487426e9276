{"version": 3, "file": "traceDetails.js", "sourceRoot": "", "sources": ["../../../../src/plugin/usageReporting/traceDetails.ts"], "names": [], "mappings": ";;;AAAA,+EAAyD;AAUzD,SAAgB,gBAAgB,CAC9B,SAA8B,EAC9B,kBAAyC,EACzC,eAAwB;IAExB,MAAM,OAAO,GAAG,IAAI,gCAAK,CAAC,OAAO,EAAE,CAAC;IACpC,MAAM,iBAAiB,GAAG,CAAC,GAAG,EAAE;QAC9B,IAAI,kBAAkB,IAAI,WAAW,IAAI,kBAAkB,EAAE,CAAC;YAC5D,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC5C,IAAI,CAAC;gBAEH,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,SAAS,CAAC;oBACrD,SAAS,EAAE,SAAS;oBACpB,eAAe,EAAE,eAAe;iBACjC,CAAC,CAAC;gBACH,OAAO,sBAAsB,CAAC,YAAY,EAAE,iBAAiB,CAAC,CAAC;YACjE,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBAGX,OAAO,iCAAiC,CAAC,YAAY,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC,CAAC,EAAE,CAAC;IAOL,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QAC9C,IACE,CAAC,kBAAkB;YACnB,CAAC,MAAM,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,IAAI,CAAC;YACzD,CAAC,KAAK,IAAI,kBAAkB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;YACxD,CAAC,aAAa,IAAI,kBAAkB;gBAIlC,kBAAkB,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAChD,CAAC,WAAW,IAAI,kBAAkB;gBAChC,CAAC,kBAAkB,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAC/C,CAAC;YAID,OAAO,CAAC,aAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACH,OAAO,CAAC,aAAc,CAAC,IAAI,CAAC;oBAC1B,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,WAAW;wBAC5C,CAAC,CAAC,EAAE;wBACJ,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,OAAO,CAAC,aAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAC3C,mCAAmC,CACpC,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,OAAO,CAAC;AACjB,CAAC;AA9DD,4CA8DC;AAED,SAAS,iCAAiC,CACxC,aAAuB;IAEvB,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9C,aAAa,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QAC7B,iBAAiB,CAAC,IAAI,CAAC,GAAG,4BAA4B,CAAC;IACzD,CAAC,CAAC,CAAC;IACH,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAID,SAAS,sBAAsB,CAC7B,YAA2B,EAC3B,iBAAsC;IAEtC,MAAM,gBAAgB,GAAwB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClE,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;QAC5B,gBAAgB,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IACH,OAAO,gBAAgB,CAAC;AAC1B,CAAC"}