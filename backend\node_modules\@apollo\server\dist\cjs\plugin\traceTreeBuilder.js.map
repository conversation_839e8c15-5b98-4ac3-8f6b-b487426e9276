{"version": 3, "file": "traceTreeBuilder.js", "sourceRoot": "", "sources": ["../../../src/plugin/traceTreeBuilder.ts"], "names": [], "mappings": ";;;AAEA,qCAIiB;AACjB,+EAAiE;AAEjE,8EAAwE;AAExE,SAAS,aAAa,CAAC,OAAe;IACpC,OAAO,IAAI,KAAK,CAAC,kCAAkC,OAAO,EAAE,CAAC,CAAC;AAChE,CAAC;AAED,MAAa,gBAAgB;IAsB3B,YAAmB,OAGlB;QAxBO,aAAQ,GAAG,IAAI,gCAAK,CAAC,IAAI,EAAE,CAAC;QAC7B,UAAK,GAAG,IAAI,gCAAK,CAAC;YACvB,IAAI,EAAE,IAAI,CAAC,QAAQ;YAQnB,oBAAoB,EAAE,CAAC;SACxB,CAAC,CAAC;QAEK,YAAO,GAAG,KAAK,CAAC;QAChB,UAAK,GAAG,IAAI,GAAG,CAAqB;YAC1C,CAAC,oBAAoB,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC;SACxC,CAAC,CAAC;QASD,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QACzC,IAAI,CAAC,UAAU,IAAI,QAAQ,IAAI,UAAU,EAAE,CAAC;YAC1C,IAAI,CAAC,cAAc,GAAG,GAAG,EAAE,CACzB,IAAI,sBAAY,CAAC,UAAU,EAAE;gBAC3B,UAAU,EAAE,EAAE,QAAQ,EAAE;aACzB,CAAC,CAAC;QACP,CAAC;aAAM,IAAI,WAAW,IAAI,UAAU,EAAE,CAAC;YACrC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,SAAS,CAAC;QAC7C,CAAC;aAAM,IAAI,YAAY,IAAI,UAAU,EAAE,CAAC;YACtC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC7B,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,8CAAoB,CAAC,UAAU,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEM,WAAW;QAChB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,MAAM,aAAa,CAAC,2BAA2B,CAAC,CAAC;QACnD,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,aAAa,CAAC,sCAAsC,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,oBAAoB,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IACtC,CAAC;IAEM,UAAU;QACf,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,aAAa,CAAC,uCAAuC,CAAC,CAAC;QAC/D,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,aAAa,CAAC,0BAA0B,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,qBAAqB,CAC3C,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CACjC,CAAC;QACF,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,oBAAoB,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;QACtD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAEM,gBAAgB,CAAC,IAAwB;QAC9C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,aAAa,CAAC,6CAA6C,CAAC,CAAC;QACrE,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YA2CjB,OAAO,GAAG,EAAE,GAAE,CAAC,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;QAC7C,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QACzE,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,SAAS,EAAE,CAAC;YAEhE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;QAC1C,CAAC;QAED,OAAO,GAAG,EAAE;YACV,IAAI,CAAC,OAAO,GAAG,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;QACzE,CAAC,CAAC;IACJ,CAAC;IAEM,kBAAkB,CAAC,MAA+B;QACvD,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YAOrB,IAAI,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YAMD,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC;YAE/D,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,IAAI,CAAC,gBAAgB,CACnB,iBAAiB,CAAC,IAAI,EACtB,oBAAoB,CAAC,iBAAiB,CAAC,CACxC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,gBAAgB,CACtB,IAAgD,EAChD,KAAkB;QAElB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,aAAa,CAAC,6CAA6C,CAAC,CAAC;QACrE,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,aAAa,CAAC,2CAA2C,CAAC,CAAC;QACnE,CAAC;QAGD,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QAGzB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACpD,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,GAAG,YAAY,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,MAAM,YAAY,GAAG,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAChE,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClB,MAAM,aAAa,CAAC,4CAA4C,CAAC,CAAC;gBACpE,CAAC;gBACD,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC;IAEO,OAAO,CAAC,IAAkB;QAChC,MAAM,IAAI,GAAG,IAAI,gCAAK,CAAC,IAAI,EAAE,CAAC;QAC9B,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QACpB,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;QACjD,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC/C,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,gBAAgB,CAAC,IAAkB;QACzC,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,UAAU,CAAC;QACpB,CAAC;QAGD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC;IAClC,CAAC;IAEO,0BAA0B,CAAC,GAAiB;QAClD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YAYxB,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAC/B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EACzC,GAAG,CACJ,CAAC;YAEF,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAIxD,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;gBAC5B,OAAO,IAAI,CAAC;YACd,CAAC;YAKD,IAAI,CAAC,CAAC,cAAc,YAAY,sBAAY,CAAC,EAAE,CAAC;gBAC9C,OAAO,GAAG,CAAC;YACb,CAAC;YAQD,OAAO,IAAI,sBAAY,CAAC,cAAc,CAAC,OAAO,EAAE;gBAC9C,KAAK,EAAE,GAAG,CAAC,KAAK;gBAChB,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,SAAS,EAAE,GAAG,CAAC,SAAS;gBACxB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,aAAa,EAAE,GAAG,CAAC,aAAa;gBAChC,UAAU,EAAE,cAAc,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU;aACxD,CAAC,CAAC;QACL,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AA3QD,4CA2QC;AAgBD,SAAS,qBAAqB,CAAC,MAAwB;IACrD,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC;AAID,SAAS,oBAAoB,CAAC,CAAgB;IAC5C,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;QACpB,OAAO,EAAE,CAAC;IACZ,CAAC;IAID,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAExB,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;QAClC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,qBAAqB,CAC5B,IAAoC,EACpC,IAAgB;IAEhB,IAAI,YAAsC,CAAC;IAC3C,IAAI,OAAO,GAA4B,IAAI,CAAC;IAC5C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,OAAO,GAAG,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,YAAY,KAAK,GAAG,CAAC,CAAC;QACtE,YAAY,GAAG;YACb,GAAG;YACH,IAAI,EAAE,YAAY;YAClB,QAAQ,EAAE,OAAO,EAAE,IAAI,IAAI,SAAS;SACrC,CAAC;IACJ,CAAC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAS,oBAAoB,CAAC,KAAmB;IAC/C,OAAO,IAAI,gCAAK,CAAC,KAAK,CAAC;QACrB,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,QAAQ,EAAE,CAAC,KAAK,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CACnC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,IAAI,gCAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAC3D;QACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;KAC5B,CAAC,CAAC;AACL,CAAC;AAGD,SAAgB,oBAAoB,CAAC,IAAU;IAC7C,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC;IAC1B,MAAM,MAAM,GAAG,WAAW,GAAG,IAAI,CAAC;IAClC,OAAO,IAAI,iCAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;QACnC,OAAO,EAAE,CAAC,WAAW,GAAG,MAAM,CAAC,GAAG,IAAI;QACtC,KAAK,EAAE,MAAM,GAAG,GAAG;KACpB,CAAC,CAAC;AACL,CAAC;AAPD,oDAOC"}