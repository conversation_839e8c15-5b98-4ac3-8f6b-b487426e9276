import { Context } from '../../context';
export declare const lessonResolvers: {
    Query: {};
    Mutation: {
        submitQuiz: (_: any, args: any, { prisma, user }: Context) => Promise<{
            score: number;
            totalQuestions: number;
            correctAnswers: number;
            feedback: {
                questionId: string;
                correct: boolean;
                explanation: string | null;
            }[];
        }>;
    };
    Lesson: {
        quizzes: (parent: any, _: any, { prisma }: Context) => Promise<({
            questions: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                type: string;
                text: string;
                options: string | null;
                correctAnswer: string;
                explanation: string | null;
                quizId: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            title: string;
            lessonId: string;
            timeLimit: number | null;
        })[]>;
    };
};
//# sourceMappingURL=lesson.d.ts.map