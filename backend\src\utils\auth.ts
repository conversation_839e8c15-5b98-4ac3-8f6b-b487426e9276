Context and JWT utilities
// backend/src/utils/auth.ts
import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

export interface TokenPayload {
  userId: string
  email: string
  role: string
}

export const hashPassword = async (password: string): Promise<string> => {
  return bcrypt.hash(password, 10)
}

export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  return bcrypt.compare(password, hash)
}

export const generateToken = (payload: TokenPayload): string => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '7d' })
}

export const verifyToken = (token: string): TokenPayload => {
  return jwt.verify(token, JWT_SECRET) as TokenPayload
}
// backend/src/context.ts
import { PrismaClient } from '@prisma/client'
import { Request } from 'express'
import { verifyToken } from './utils/auth'

const prisma = new PrismaClient()

export interface Context {
  prisma: PrismaClient
  userId?: string
  userRole?: string
}

export const createContext = ({ req }: { req: Request }): Context => {
  const token = req.headers.authorization?.replace('Bearer ', '')
  
  if (token) {
    try {
      const payload = verifyToken(token)
      return {
        prisma,
        userId: payload.userId,
        userRole: payload.role,
      }
    } catch (error) {
      // Invalid token
    }
  }
  
  return { prisma }
}
